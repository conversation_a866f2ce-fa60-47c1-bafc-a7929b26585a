{"ast": null, "code": "import { InfiniteQueryObserver } from '../core/infiniteQueryObserver';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useInfiniteQuery(arg1, arg2, arg3) {\n  var options = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(options, InfiniteQueryObserver);\n}", "map": {"version": 3, "names": ["InfiniteQueryObserver", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useBaseQuery", "useInfiniteQuery", "arg1", "arg2", "arg3", "options"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-query/es/react/useInfiniteQuery.js"], "sourcesContent": ["import { InfiniteQueryObserver } from '../core/infiniteQueryObserver';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useInfiniteQuery(arg1, arg2, arg3) {\n  var options = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(options, InfiniteQueryObserver);\n}"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,YAAY,QAAQ,gBAAgB,CAAC,CAAC;;AAE/C,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACjD,IAAIC,OAAO,GAAGN,cAAc,CAACG,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAC9C,OAAOJ,YAAY,CAACK,OAAO,EAAEP,qBAAqB,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}