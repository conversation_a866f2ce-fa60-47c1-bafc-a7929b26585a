import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Button from '../common/Button';
import Input from '../common/Input';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${props => props.theme.fontSize.sm};
  font-weight: ${props => props.theme.fontWeight.medium};
  color: ${props => props.theme.colors.textPrimary};
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.sm};
  color: ${props => props.theme.colors.textPrimary};
  background-color: ${props => props.theme.colors.background};
  transition: all ${props => props.theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${props => props.theme.colors.backgroundSecondary};
    color: ${props => props.theme.colors.textMuted};
    cursor: not-allowed;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.sm};
  color: ${props => props.theme.colors.textPrimary};
  background-color: ${props => props.theme.colors.background};
  transition: all ${props => props.theme.transitions.fast};
  resize: vertical;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &::placeholder {
    color: ${props => props.theme.colors.textMuted};
  }
`;

const CheckboxItem = styled.label`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color ${props => props.theme.transitions.fast};

  &:hover {
    background-color: ${props => props.theme.colors.backgroundSecondary};
  }
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: ${props => props.theme.colors.primary};
`;

const CheckboxLabel = styled.span`
  font-size: ${props => props.theme.fontSize.sm};
  color: ${props => props.theme.colors.textPrimary};
`;

const FormActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const ErrorMessage = styled.span`
  font-size: ${props => props.theme.fontSize.xs};
  color: ${props => props.theme.colors.error};
`;

const ModuleForm = ({ module, modules, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    icon: '',
    route: '',
    parentId: '',
    sortOrder: 0,
    isActive: true
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (module) {
      setFormData({
        name: module.name || '',
        code: module.code || '',
        description: module.description || '',
        icon: module.icon || '',
        route: module.route || '',
        parentId: module.parentId || '',
        sortOrder: module.sortOrder || 0,
        isActive: module.isActive !== undefined ? module.isActive : true
      });
    }
  }, [module]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Module name is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Module code is required';
    } else if (!/^[A-Z0-9_]+$/.test(formData.code)) {
      newErrors.code = 'Module code must contain only uppercase letters, numbers, and underscores';
    }

    if (formData.sortOrder < 0) {
      newErrors.sortOrder = 'Sort order must be a non-negative number';
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const newErrors = validateForm();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      const submitData = { ...formData };
      
      // Convert empty parentId to null
      if (!submitData.parentId) {
        submitData.parentId = null;
      }

      // Convert sortOrder to number
      submitData.sortOrder = parseInt(submitData.sortOrder) || 0;

      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter out current module from parent options to prevent circular reference
  const availableParents = modules.filter(m => m.id !== module?.id);

  return (
    <Form onSubmit={handleSubmit}>
      <FormRow>
        <Input
          label="Module Name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          error={errors.name}
          placeholder="Enter module name"
          required
        />
        <Input
          label="Module Code"
          name="code"
          value={formData.code}
          onChange={handleChange}
          error={errors.code}
          placeholder="e.g., USER_MGMT"
          required
        />
      </FormRow>

      <FormGroup>
        <Label htmlFor="description">Description</Label>
        <TextArea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Enter module description (optional)"
        />
      </FormGroup>

      <FormRow>
        <Input
          label="Icon"
          name="icon"
          value={formData.icon}
          onChange={handleChange}
          placeholder="e.g., users, settings"
        />
        <Input
          label="Route"
          name="route"
          value={formData.route}
          onChange={handleChange}
          placeholder="e.g., /settings/users"
        />
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="parentId">Parent Module</Label>
          <Select
            id="parentId"
            name="parentId"
            value={formData.parentId}
            onChange={handleChange}
          >
            <option value="">No Parent (Root Module)</option>
            {availableParents.map(parentModule => (
              <option key={parentModule.id} value={parentModule.id}>
                {parentModule.name}
              </option>
            ))}
          </Select>
        </FormGroup>
        <Input
          label="Sort Order"
          type="number"
          name="sortOrder"
          value={formData.sortOrder}
          onChange={handleChange}
          error={errors.sortOrder}
          min="0"
        />
      </FormRow>

      <FormGroup>
        <CheckboxItem>
          <Checkbox
            type="checkbox"
            name="isActive"
            checked={formData.isActive}
            onChange={handleChange}
          />
          <CheckboxLabel>Active Module</CheckboxLabel>
        </CheckboxItem>
      </FormGroup>

      <FormActions>
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={loading}
        >
          {module ? 'Update Module' : 'Create Module'}
        </Button>
      </FormActions>
    </Form>
  );
};

export default ModuleForm;
