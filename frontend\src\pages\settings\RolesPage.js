import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { FiPlus, FiEdit, FiTrash2, FiSearch } from 'react-icons/fi';
import { rolesAPI } from '../../api/roles';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Table from '../../components/common/Table';
import Modal from '../../components/common/Modal';
import RoleForm from '../../components/forms/RoleForm';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  flex-wrap: wrap;
`;

const PageTitle = styled.h1`
  font-size: ${props => props.theme.fontSize['2xl']};
  font-weight: ${props => props.theme.fontWeight.bold};
  color: ${props => props.theme.colors.textPrimary};
  margin: 0;
  flex: 1;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 300px;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${props => props.theme.spacing.md};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textMuted};
`;

const SearchInput = styled(Input)`
  input {
    padding-left: ${props => props.theme.spacing.xl};
  }
`;

const TableContainer = styled(Card)`
  overflow: hidden;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const StatusBadge = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.medium};
  
  ${props => props.active ? `
    background-color: ${props.theme.colors.success}20;
    color: ${props.theme.colors.success};
  ` : `
    background-color: ${props.theme.colors.error}20;
    color: ${props.theme.colors.error};
  `}
`;

const UserCount = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.backgroundSecondary};
  color: ${props => props.theme.colors.textSecondary};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.medium};
`;

const RolesPage = () => {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    fetchRoles();
  }, [currentPage, searchTerm]);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await rolesAPI.getRoles({
        page: currentPage,
        limit: 10,
        search: searchTerm
      });
      setRoles(response.data.roles);
      setTotalPages(response.data.pagination.totalPages);
    } catch (error) {
      toast.error('Failed to fetch roles');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleAddRole = () => {
    setEditingRole(null);
    setShowModal(true);
  };

  const handleEditRole = (role) => {
    setEditingRole(role);
    setShowModal(true);
  };

  const handleDeleteRole = async (roleId) => {
    try {
      await rolesAPI.deleteRole(roleId);
      toast.success('Role deleted successfully');
      fetchRoles();
      setDeleteConfirm(null);
    } catch (error) {
      toast.error('Failed to delete role');
    }
  };

  const handleFormSubmit = async (roleData) => {
    try {
      if (editingRole) {
        await rolesAPI.updateRole(editingRole.id, roleData);
        toast.success('Role updated successfully');
      } else {
        await rolesAPI.createRole(roleData);
        toast.success('Role created successfully');
      }
      setShowModal(false);
      fetchRoles();
    } catch (error) {
      toast.error(editingRole ? 'Failed to update role' : 'Failed to create role');
    }
  };

  const columns = [
    {
      header: 'Name',
      accessor: 'name'
    },
    {
      header: 'Description',
      accessor: 'description',
      render: (description) => description || '-'
    },
    {
      header: 'Users',
      accessor: 'users',
      render: (users) => (
        <UserCount>
          {users?.length || 0} users
        </UserCount>
      )
    },
    {
      header: 'Status',
      accessor: 'isActive',
      render: (isActive) => (
        <StatusBadge active={isActive}>
          {isActive ? 'Active' : 'Inactive'}
        </StatusBadge>
      )
    },
    {
      header: 'Actions',
      accessor: 'actions',
      render: (_, role) => (
        <ActionButtons>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditRole(role)}
          >
            <FiEdit size={16} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeleteConfirm(role)}
          >
            <FiTrash2 size={16} />
          </Button>
        </ActionButtons>
      )
    }
  ];

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Role Management</PageTitle>
        <HeaderActions>
          <SearchContainer>
            <SearchIcon>
              <FiSearch size={16} />
            </SearchIcon>
            <SearchInput
              placeholder="Search roles..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </SearchContainer>
          <Button
            variant="primary"
            onClick={handleAddRole}
          >
            <FiPlus size={16} />
            Add Role
          </Button>
        </HeaderActions>
      </PageHeader>

      <TableContainer>
        <Table
          columns={columns}
          data={roles}
          loading={loading}
          emptyMessage="No roles found"
        />
      </TableContainer>

      {showModal && (
        <Modal
          title={editingRole ? 'Edit Role' : 'Add New Role'}
          onClose={() => setShowModal(false)}
        >
          <RoleForm
            role={editingRole}
            onSubmit={handleFormSubmit}
            onCancel={() => setShowModal(false)}
          />
        </Modal>
      )}

      {deleteConfirm && (
        <Modal
          title="Confirm Delete"
          onClose={() => setDeleteConfirm(null)}
        >
          <div style={{ padding: '1rem 0' }}>
            <p>Are you sure you want to delete role "{deleteConfirm.name}"?</p>
            <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem', justifyContent: 'flex-end' }}>
              <Button variant="secondary" onClick={() => setDeleteConfirm(null)}>
                Cancel
              </Button>
              <Button variant="danger" onClick={() => handleDeleteRole(deleteConfirm.id)}>
                Delete
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </PageContainer>
  );
};

export default RolesPage;
