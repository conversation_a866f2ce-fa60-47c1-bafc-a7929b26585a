{"ast": null, "code": "export const lightTheme = {\n  colors: {\n    primary: '#2563eb',\n    // Blue\n    primaryHover: '#1d4ed8',\n    secondary: '#64748b',\n    success: '#10b981',\n    warning: '#f59e0b',\n    error: '#ef4444',\n    info: '#3b82f6',\n    // Background colors\n    background: '#ffffff',\n    backgroundSecondary: '#f8fafc',\n    backgroundTertiary: '#f1f5f9',\n    // Text colors\n    textPrimary: '#1e293b',\n    textSecondary: '#64748b',\n    textMuted: '#94a3b8',\n    textInverse: '#ffffff',\n    // Border colors\n    border: '#e2e8f0',\n    borderLight: '#f1f5f9',\n    borderDark: '#cbd5e1',\n    // Sidebar\n    sidebarBg: '#1e293b',\n    sidebarText: '#cbd5e1',\n    sidebarTextActive: '#ffffff',\n    sidebarHover: '#334155',\n    // Header\n    headerBg: '#ffffff',\n    headerBorder: '#e2e8f0',\n    // Cards\n    cardBg: '#ffffff',\n    cardBorder: '#e2e8f0',\n    cardShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n  },\n  spacing: {\n    xs: '0.25rem',\n    sm: '0.5rem',\n    md: '1rem',\n    lg: '1.5rem',\n    xl: '2rem',\n    xxl: '3rem'\n  },\n  borderRadius: {\n    sm: '0.25rem',\n    md: '0.375rem',\n    lg: '0.5rem',\n    xl: '0.75rem'\n  },\n  fontSize: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem'\n  },\n  fontWeight: {\n    normal: '400',\n    medium: '500',\n    semibold: '600',\n    bold: '700'\n  },\n  shadows: {\n    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n  },\n  transitions: {\n    fast: '150ms ease-in-out',\n    normal: '300ms ease-in-out',\n    slow: '500ms ease-in-out'\n  }\n};\nexport const themeVariants = {\n  blue: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#2563eb',\n      primaryHover: '#1d4ed8'\n    }\n  },\n  green: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#059669',\n      primaryHover: '#047857'\n    }\n  },\n  purple: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#7c3aed',\n      primaryHover: '#6d28d9'\n    }\n  },\n  gray: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#374151',\n      primaryHover: '#1f2937'\n    }\n  }\n};\nexport const defaultTheme = themeVariants.blue;", "map": {"version": 3, "names": ["lightTheme", "colors", "primary", "primaryHover", "secondary", "success", "warning", "error", "info", "background", "backgroundSecondary", "backgroundTertiary", "textPrimary", "textSecondary", "textMuted", "textInverse", "border", "borderLight", "borderDark", "sidebarBg", "sidebarText", "sidebarTextActive", "sidebarHover", "headerBg", "headerBorder", "cardBg", "cardBorder", "cardShadow", "spacing", "xs", "sm", "md", "lg", "xl", "xxl", "borderRadius", "fontSize", "base", "fontWeight", "normal", "medium", "semibold", "bold", "shadows", "transitions", "fast", "slow", "themeVariants", "blue", "green", "purple", "gray", "defaultTheme"], "sources": ["D:/Projects/qmsus/frontend/src/theme/theme.js"], "sourcesContent": ["export const lightTheme = {\n  colors: {\n    primary: '#2563eb', // Blue\n    primaryHover: '#1d4ed8',\n    secondary: '#64748b',\n    success: '#10b981',\n    warning: '#f59e0b',\n    error: '#ef4444',\n    info: '#3b82f6',\n    \n    // Background colors\n    background: '#ffffff',\n    backgroundSecondary: '#f8fafc',\n    backgroundTertiary: '#f1f5f9',\n    \n    // Text colors\n    textPrimary: '#1e293b',\n    textSecondary: '#64748b',\n    textMuted: '#94a3b8',\n    textInverse: '#ffffff',\n    \n    // Border colors\n    border: '#e2e8f0',\n    borderLight: '#f1f5f9',\n    borderDark: '#cbd5e1',\n    \n    // Sidebar\n    sidebarBg: '#1e293b',\n    sidebarText: '#cbd5e1',\n    sidebarTextActive: '#ffffff',\n    sidebarHover: '#334155',\n    \n    // Header\n    headerBg: '#ffffff',\n    headerBorder: '#e2e8f0',\n    \n    // Cards\n    cardBg: '#ffffff',\n    cardBorder: '#e2e8f0',\n    cardShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'\n  },\n  \n  spacing: {\n    xs: '0.25rem',\n    sm: '0.5rem',\n    md: '1rem',\n    lg: '1.5rem',\n    xl: '2rem',\n    xxl: '3rem'\n  },\n  \n  borderRadius: {\n    sm: '0.25rem',\n    md: '0.375rem',\n    lg: '0.5rem',\n    xl: '0.75rem'\n  },\n  \n  fontSize: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem'\n  },\n  \n  fontWeight: {\n    normal: '400',\n    medium: '500',\n    semibold: '600',\n    bold: '700'\n  },\n  \n  shadows: {\n    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n  },\n  \n  transitions: {\n    fast: '150ms ease-in-out',\n    normal: '300ms ease-in-out',\n    slow: '500ms ease-in-out'\n  }\n};\n\nexport const themeVariants = {\n  blue: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#2563eb',\n      primaryHover: '#1d4ed8'\n    }\n  },\n  green: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#059669',\n      primaryHover: '#047857'\n    }\n  },\n  purple: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#7c3aed',\n      primaryHover: '#6d28d9'\n    }\n  },\n  gray: {\n    ...lightTheme,\n    colors: {\n      ...lightTheme.colors,\n      primary: '#374151',\n      primaryHover: '#1f2937'\n    }\n  }\n};\n\nexport const defaultTheme = themeVariants.blue;\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG;EACxBC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAAE;IACpBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IAEf;IACAC,UAAU,EAAE,SAAS;IACrBC,mBAAmB,EAAE,SAAS;IAC9BC,kBAAkB,EAAE,SAAS;IAE7B;IACAC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,SAAS;IACxBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IAEtB;IACAC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,SAAS;IAErB;IACAC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,SAAS;IAC5BC,YAAY,EAAE,SAAS;IAEvB;IACAC,QAAQ,EAAE,SAAS;IACnBC,YAAY,EAAE,SAAS;IAEvB;IACAC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE;EACd,CAAC;EAEDC,OAAO,EAAE;IACPC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE;EACP,CAAC;EAEDC,YAAY,EAAE;IACZL,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE;EACN,CAAC;EAEDG,QAAQ,EAAE;IACRP,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,UAAU;IACdO,IAAI,EAAE,MAAM;IACZL,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,SAAS;IACb,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE;EACT,CAAC;EAEDK,UAAU,EAAE;IACVC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACR,CAAC;EAEDC,OAAO,EAAE;IACPb,EAAE,EAAE,iCAAiC;IACrCC,EAAE,EAAE,uEAAuE;IAC3EC,EAAE,EAAE,yEAAyE;IAC7EC,EAAE,EAAE;EACN,CAAC;EAEDW,WAAW,EAAE;IACXC,IAAI,EAAE,mBAAmB;IACzBN,MAAM,EAAE,mBAAmB;IAC3BO,IAAI,EAAE;EACR;AACF,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG;EAC3BC,IAAI,EAAE;IACJ,GAAGhD,UAAU;IACbC,MAAM,EAAE;MACN,GAAGD,UAAU,CAACC,MAAM;MACpBC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE;IAChB;EACF,CAAC;EACD8C,KAAK,EAAE;IACL,GAAGjD,UAAU;IACbC,MAAM,EAAE;MACN,GAAGD,UAAU,CAACC,MAAM;MACpBC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE;IAChB;EACF,CAAC;EACD+C,MAAM,EAAE;IACN,GAAGlD,UAAU;IACbC,MAAM,EAAE;MACN,GAAGD,UAAU,CAACC,MAAM;MACpBC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE;IAChB;EACF,CAAC;EACDgD,IAAI,EAAE;IACJ,GAAGnD,UAAU;IACbC,MAAM,EAAE;MACN,GAAGD,UAAU,CAACC,MAAM;MACpBC,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE;IAChB;EACF;AACF,CAAC;AAED,OAAO,MAAMiD,YAAY,GAAGL,aAAa,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}