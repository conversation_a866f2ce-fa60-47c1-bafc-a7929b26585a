'use strict';
const bcrypt = require('bcryptjs');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert roles
    const roles = await queryInterface.bulkInsert('roles', [
      {
        name: 'Super Admin',
        description: 'Full system access',
        permissions: JSON.stringify({
          'users.create': true,
          'users.read': true,
          'users.update': true,
          'users.delete': true,
          'roles.create': true,
          'roles.read': true,
          'roles.update': true,
          'roles.delete': true,
          'modules.create': true,
          'modules.read': true,
          'modules.update': true,
          'modules.delete': true
        }),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Admin',
        description: 'Administrative access',
        permissions: JSON.stringify({
          'users.create': true,
          'users.read': true,
          'users.update': true,
          'roles.read': true,
          'modules.read': true,
          'modules.update': true
        }),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'User',
        description: 'Standard user access',
        permissions: JSON.stringify({
          'modules.read': true
        }),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], { returning: true });

    // Insert default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    const users = await queryInterface.bulkInsert('users', [
      {
        firstName: 'System',
        lastName: 'Administrator',
        email: '<EMAIL>',
        password: hashedPassword,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], { returning: true });

    // Assign Super Admin role to admin user
    await queryInterface.bulkInsert('user_roles', [
      {
        userId: 1, // First user
        roleId: 1, // Super Admin role
        assignedAt: new Date(),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);

    // Insert sample modules
    await queryInterface.bulkInsert('modules', [
      {
        name: 'Dashboard',
        description: 'Main dashboard overview',
        code: 'DASHBOARD',
        icon: 'dashboard',
        route: '/dashboard',
        parentId: null,
        sortOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Settings',
        description: 'System settings and configuration',
        code: 'SETTINGS',
        icon: 'settings',
        route: '/settings',
        parentId: null,
        sortOrder: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'User Management',
        description: 'Manage system users',
        code: 'USER_MGMT',
        icon: 'people',
        route: '/settings/users',
        parentId: 2, // Settings module
        sortOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Role Management',
        description: 'Manage user roles and permissions',
        code: 'ROLE_MGMT',
        icon: 'security',
        route: '/settings/roles',
        parentId: 2, // Settings module
        sortOrder: 2,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Module Management',
        description: 'Manage system modules',
        code: 'MODULE_MGMT',
        icon: 'apps',
        route: '/settings/modules',
        parentId: 2, // Settings module
        sortOrder: 3,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('user_roles', null, {});
    await queryInterface.bulkDelete('modules', null, {});
    await queryInterface.bulkDelete('users', null, {});
    await queryInterface.bulkDelete('roles', null, {});
  }
};
