const express = require('express');
const router = express.Router();
const moduleController = require('../controllers/moduleController');
const { authenticateToken, authorize } = require('../middleware/auth');
const { validateModule, validateId, validatePagination } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// GET /api/modules - Get all modules with pagination and search
router.get('/', validatePagination, moduleController.getModules);

// GET /api/modules/:id - Get module by ID
router.get('/:id', validateId, moduleController.getModuleById);

// POST /api/modules - Create new module (requires admin permission)
router.post('/', 
  authorize(['modules.create']), 
  validateModule, 
  moduleController.createModule
);

// PUT /api/modules/:id - Update module (requires admin permission)
router.put('/:id', 
  authorize(['modules.update']), 
  validateId, 
  validateModule, 
  moduleController.updateModule
);

// DELETE /api/modules/:id - Delete module (requires admin permission)
router.delete('/:id', 
  authorize(['modules.delete']), 
  validateId, 
  moduleController.deleteModule
);

module.exports = router;
