{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { FiMenu, FiBell, FiUser, FiSettings, FiLogOut, FiChevronDown } from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useTheme } from '../../context/ThemeContext';\nimport Button from '../common/Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderContainer = styled.header`\n  height: 64px;\n  background-color: ${props => props.theme.colors.headerBg};\n  border-bottom: 1px solid ${props => props.theme.colors.headerBorder};\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 ${props => props.theme.spacing.lg};\n  position: sticky;\n  top: 0;\n  z-index: 100;\n`;\n_c = HeaderContainer;\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n`;\n_c2 = LeftSection;\nconst MenuButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textSecondary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n`;\n_c3 = MenuButton;\nconst PageTitle = styled.h1`\n  font-size: ${props => props.theme.fontSize.xl};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0;\n`;\n_c4 = PageTitle;\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n`;\n_c5 = RightSection;\nconst ThemeSelector = styled.select`\n  padding: ${props => props.theme.spacing.sm};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  background-color: ${props => props.theme.colors.background};\n  color: ${props => props.theme.colors.textPrimary};\n  font-size: ${props => props.theme.fontSize.sm};\n  cursor: pointer;\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n_c6 = ThemeSelector;\nconst NotificationButton = styled.button`\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textSecondary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n`;\n_c7 = NotificationButton;\nconst NotificationBadge = styled.span`\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 8px;\n  height: 8px;\n  background-color: ${props => props.theme.colors.error};\n  border-radius: 50%;\n`;\n_c8 = NotificationBadge;\nconst UserMenu = styled.div`\n  position: relative;\n`;\n_c9 = UserMenu;\nconst UserButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n  padding: ${props => props.theme.spacing.sm};\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textPrimary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n  }\n`;\n_c0 = UserButton;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  background-color: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.textInverse};\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  font-size: ${props => props.theme.fontSize.sm};\n`;\n_c1 = UserAvatar;\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n_c10 = UserInfo;\nconst UserName = styled.span`\n  font-weight: ${props => props.theme.fontWeight.medium};\n  font-size: ${props => props.theme.fontSize.sm};\n`;\n_c11 = UserName;\nconst UserRole = styled.span`\n  font-size: ${props => props.theme.fontSize.xs};\n  color: ${props => props.theme.colors.textSecondary};\n`;\n_c12 = UserRole;\nconst Dropdown = styled.div`\n  position: absolute;\n  top: 100%;\n  right: 0;\n  margin-top: ${props => props.theme.spacing.xs};\n  background-color: ${props => props.theme.colors.background};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.lg};\n  min-width: 200px;\n  z-index: 1000;\n`;\n_c13 = Dropdown;\nconst DropdownItem = styled.button`\n  width: 100%;\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n  padding: ${props => props.theme.spacing.md};\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textPrimary};\n  cursor: pointer;\n  font-size: ${props => props.theme.fontSize.sm};\n  text-align: left;\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n  }\n\n  &:first-child {\n    border-top-left-radius: ${props => props.theme.borderRadius.lg};\n    border-top-right-radius: ${props => props.theme.borderRadius.lg};\n  }\n\n  &:last-child {\n    border-bottom-left-radius: ${props => props.theme.borderRadius.lg};\n    border-bottom-right-radius: ${props => props.theme.borderRadius.lg};\n  }\n`;\n_c14 = DropdownItem;\nconst Header = ({\n  onToggleSidebar,\n  sidebarCollapsed\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    currentTheme,\n    changeTheme,\n    availableThemes\n  } = useTheme();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const userMenuRef = useRef(null);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n        setShowUserMenu(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  const handleLogout = async () => {\n    await logout();\n    setShowUserMenu(false);\n  };\n  const getUserInitials = () => {\n    var _user$firstName, _user$lastName;\n    if (!user) return 'U';\n    return `${((_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName[0]) || ''}${((_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName[0]) || ''}`.toUpperCase();\n  };\n  const getUserRole = () => {\n    if (!(user !== null && user !== void 0 && user.role)) return 'User';\n    return user.role.charAt(0).toUpperCase() + user.role.slice(1);\n  };\n  return /*#__PURE__*/_jsxDEV(HeaderContainer, {\n    children: [/*#__PURE__*/_jsxDEV(LeftSection, {\n      children: [/*#__PURE__*/_jsxDEV(MenuButton, {\n        onClick: onToggleSidebar,\n        children: /*#__PURE__*/_jsxDEV(FiMenu, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RightSection, {\n      children: [/*#__PURE__*/_jsxDEV(ThemeSelector, {\n        value: currentTheme,\n        onChange: e => changeTheme(e.target.value),\n        children: availableThemes.map(theme => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: theme,\n          children: [theme.charAt(0).toUpperCase() + theme.slice(1), \" Theme\"]\n        }, theme, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NotificationButton, {\n        children: [/*#__PURE__*/_jsxDEV(FiBell, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(NotificationBadge, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UserMenu, {\n        ref: userMenuRef,\n        children: [/*#__PURE__*/_jsxDEV(UserButton, {\n          onClick: () => setShowUserMenu(!showUserMenu),\n          children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n            children: getUserInitials()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n            children: [/*#__PURE__*/_jsxDEV(UserName, {\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(UserRole, {\n              children: getUserRole()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FiChevronDown, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(Dropdown, {\n          children: [/*#__PURE__*/_jsxDEV(DropdownItem, {\n            children: [/*#__PURE__*/_jsxDEV(FiUser, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DropdownItem, {\n            children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), \"Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DropdownItem, {\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(FiLogOut, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"T2RcJPBjElMeTHkQnNYGBaVHeOM=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c15 = Header;\nexport default Header;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"HeaderContainer\");\n$RefreshReg$(_c2, \"LeftSection\");\n$RefreshReg$(_c3, \"MenuButton\");\n$RefreshReg$(_c4, \"PageTitle\");\n$RefreshReg$(_c5, \"RightSection\");\n$RefreshReg$(_c6, \"ThemeSelector\");\n$RefreshReg$(_c7, \"NotificationButton\");\n$RefreshReg$(_c8, \"NotificationBadge\");\n$RefreshReg$(_c9, \"UserMenu\");\n$RefreshReg$(_c0, \"UserButton\");\n$RefreshReg$(_c1, \"UserAvatar\");\n$RefreshReg$(_c10, \"UserInfo\");\n$RefreshReg$(_c11, \"UserName\");\n$RefreshReg$(_c12, \"UserRole\");\n$RefreshReg$(_c13, \"Dropdown\");\n$RefreshReg$(_c14, \"DropdownItem\");\n$RefreshReg$(_c15, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "styled", "FiMenu", "FiBell", "FiUser", "FiSettings", "FiLogOut", "FiChevronDown", "useAuth", "useTheme", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "props", "theme", "colors", "headerBg", "headerBorder", "spacing", "lg", "_c", "LeftSection", "div", "md", "_c2", "MenuButton", "button", "textSecondary", "borderRadius", "transitions", "fast", "backgroundSecondary", "textPrimary", "_c3", "Page<PERSON><PERSON>le", "h1", "fontSize", "xl", "fontWeight", "semibold", "_c4", "RightSection", "_c5", "ThemeSelector", "select", "sm", "border", "background", "primary", "_c6", "NotificationButton", "_c7", "NotificationBadge", "span", "error", "_c8", "UserMenu", "_c9", "UserButton", "_c0", "UserAvatar", "textInverse", "_c1", "UserInfo", "_c10", "UserName", "medium", "_c11", "UserRole", "xs", "_c12", "Dropdown", "shadows", "_c13", "DropdownItem", "_c14", "Header", "onToggleSidebar", "sidebarCollapsed", "_s", "user", "logout", "currentTheme", "changeTheme", "availableThemes", "showUserMenu", "setShowUserMenu", "userMenuRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleLogout", "getUserInitials", "_user$firstName", "_user$lastName", "firstName", "lastName", "toUpperCase", "getUserRole", "role", "char<PERSON>t", "slice", "children", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "map", "ref", "_c15", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/components/layout/Header.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { \n  FiMenu, \n  FiBell, \n  FiUser, \n  FiSettings, \n  FiLogOut,\n  FiChevronDown \n} from 'react-icons/fi';\nimport { useAuth } from '../../context/AuthContext';\nimport { useTheme } from '../../context/ThemeContext';\nimport Button from '../common/Button';\n\nconst HeaderContainer = styled.header`\n  height: 64px;\n  background-color: ${props => props.theme.colors.headerBg};\n  border-bottom: 1px solid ${props => props.theme.colors.headerBorder};\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 ${props => props.theme.spacing.lg};\n  position: sticky;\n  top: 0;\n  z-index: 100;\n`;\n\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n`;\n\nconst MenuButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textSecondary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n`;\n\nconst PageTitle = styled.h1`\n  font-size: ${props => props.theme.fontSize.xl};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n`;\n\nconst ThemeSelector = styled.select`\n  padding: ${props => props.theme.spacing.sm};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  background-color: ${props => props.theme.colors.background};\n  color: ${props => props.theme.colors.textPrimary};\n  font-size: ${props => props.theme.fontSize.sm};\n  cursor: pointer;\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n\nconst NotificationButton = styled.button`\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textSecondary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n`;\n\nconst NotificationBadge = styled.span`\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 8px;\n  height: 8px;\n  background-color: ${props => props.theme.colors.error};\n  border-radius: 50%;\n`;\n\nconst UserMenu = styled.div`\n  position: relative;\n`;\n\nconst UserButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n  padding: ${props => props.theme.spacing.sm};\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textPrimary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n  }\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  background-color: ${props => props.theme.colors.primary};\n  color: ${props => props.theme.colors.textInverse};\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  font-size: ${props => props.theme.fontSize.sm};\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n`;\n\nconst UserName = styled.span`\n  font-weight: ${props => props.theme.fontWeight.medium};\n  font-size: ${props => props.theme.fontSize.sm};\n`;\n\nconst UserRole = styled.span`\n  font-size: ${props => props.theme.fontSize.xs};\n  color: ${props => props.theme.colors.textSecondary};\n`;\n\nconst Dropdown = styled.div`\n  position: absolute;\n  top: 100%;\n  right: 0;\n  margin-top: ${props => props.theme.spacing.xs};\n  background-color: ${props => props.theme.colors.background};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.lg};\n  min-width: 200px;\n  z-index: 1000;\n`;\n\nconst DropdownItem = styled.button`\n  width: 100%;\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n  padding: ${props => props.theme.spacing.md};\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textPrimary};\n  cursor: pointer;\n  font-size: ${props => props.theme.fontSize.sm};\n  text-align: left;\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n  }\n\n  &:first-child {\n    border-top-left-radius: ${props => props.theme.borderRadius.lg};\n    border-top-right-radius: ${props => props.theme.borderRadius.lg};\n  }\n\n  &:last-child {\n    border-bottom-left-radius: ${props => props.theme.borderRadius.lg};\n    border-bottom-right-radius: ${props => props.theme.borderRadius.lg};\n  }\n`;\n\nconst Header = ({ onToggleSidebar, sidebarCollapsed }) => {\n  const { user, logout } = useAuth();\n  const { currentTheme, changeTheme, availableThemes } = useTheme();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const userMenuRef = useRef(null);\n\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n        setShowUserMenu(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleLogout = async () => {\n    await logout();\n    setShowUserMenu(false);\n  };\n\n  const getUserInitials = () => {\n    if (!user) return 'U';\n    return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase();\n  };\n\n  const getUserRole = () => {\n    if (!user?.role) return 'User';\n    return user.role.charAt(0).toUpperCase() + user.role.slice(1);\n  };\n\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <MenuButton onClick={onToggleSidebar}>\n          <FiMenu size={20} />\n        </MenuButton>\n        <PageTitle>Dashboard</PageTitle>\n      </LeftSection>\n\n      <RightSection>\n        <ThemeSelector\n          value={currentTheme}\n          onChange={(e) => changeTheme(e.target.value)}\n        >\n          {availableThemes.map(theme => (\n            <option key={theme} value={theme}>\n              {theme.charAt(0).toUpperCase() + theme.slice(1)} Theme\n            </option>\n          ))}\n        </ThemeSelector>\n\n        <NotificationButton>\n          <FiBell size={20} />\n          <NotificationBadge />\n        </NotificationButton>\n\n        <UserMenu ref={userMenuRef}>\n          <UserButton onClick={() => setShowUserMenu(!showUserMenu)}>\n            <UserAvatar>{getUserInitials()}</UserAvatar>\n            <UserInfo>\n              <UserName>{user?.firstName} {user?.lastName}</UserName>\n              <UserRole>{getUserRole()}</UserRole>\n            </UserInfo>\n            <FiChevronDown size={16} />\n          </UserButton>\n\n          {showUserMenu && (\n            <Dropdown>\n              <DropdownItem>\n                <FiUser size={16} />\n                Profile\n              </DropdownItem>\n              <DropdownItem>\n                <FiSettings size={16} />\n                Settings\n              </DropdownItem>\n              <DropdownItem onClick={handleLogout}>\n                <FiLogOut size={16} />\n                Logout\n              </DropdownItem>\n            </Dropdown>\n          )}\n        </UserMenu>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,QAAQ,EACRC,aAAa,QACR,gBAAgB;AACvB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAOC,MAAM,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,eAAe,GAAGZ,MAAM,CAACa,MAAM;AACrC;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,QAAQ;AAC1D,6BAA6BH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACE,YAAY;AACrE;AACA;AACA;AACA,eAAeJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACC,EAAE;AAC9C;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAXIT,eAAe;AAarB,MAAMU,WAAW,GAAGtB,MAAM,CAACuB,GAAG;AAC9B;AACA;AACA,SAAST,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACK,EAAE;AACxC,CAAC;AAACC,GAAA,GAJIH,WAAW;AAMjB,MAAMI,UAAU,GAAG1B,MAAM,CAAC2B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,aAAa;AACpD;AACA,mBAAmBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACL,EAAE;AACvD,oBAAoBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,IAAI;AACzD;AACA;AACA,wBAAwBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,mBAAmB;AACvE,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,WAAW;AACpD;AACA,CAAC;AAACC,GAAA,GAjBIR,UAAU;AAmBhB,MAAMS,SAAS,GAAGnC,MAAM,CAACoC,EAAE;AAC3B,eAAetB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsB,QAAQ,CAACC,EAAE;AAC/C,iBAAiBxB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwB,UAAU,CAACC,QAAQ;AACzD,WAAW1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,WAAW;AAClD;AACA,CAAC;AAACQ,GAAA,GALIN,SAAS;AAOf,MAAMO,YAAY,GAAG1C,MAAM,CAACuB,GAAG;AAC/B;AACA;AACA,SAAST,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACK,EAAE;AACxC,CAAC;AAACmB,GAAA,GAJID,YAAY;AAMlB,MAAME,aAAa,GAAG5C,MAAM,CAAC6C,MAAM;AACnC,aAAa/B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC2B,EAAE;AAC5C,sBAAsBhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC+B,MAAM;AACxD,mBAAmBjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACL,EAAE;AACvD,sBAAsBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgC,UAAU;AAC5D,WAAWlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,WAAW;AAClD,eAAenB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsB,QAAQ,CAACS,EAAE;AAC/C;AACA;AACA;AACA;AACA,oBAAoBhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiC,OAAO;AACvD;AACA,CAAC;AAACC,GAAA,GAbIN,aAAa;AAenB,MAAMO,kBAAkB,GAAGnD,MAAM,CAAC2B,MAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,aAAa;AACpD;AACA,mBAAmBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACL,EAAE;AACvD,oBAAoBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,IAAI;AACzD;AACA;AACA,wBAAwBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,mBAAmB;AACvE,aAAalB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,WAAW;AACpD;AACA,CAAC;AAACmB,GAAA,GAlBID,kBAAkB;AAoBxB,MAAME,iBAAiB,GAAGrD,MAAM,CAACsD,IAAI;AACrC;AACA;AACA;AACA;AACA;AACA,sBAAsBxC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACuC,KAAK;AACvD;AACA,CAAC;AAACC,GAAA,GARIH,iBAAiB;AAUvB,MAAMI,QAAQ,GAAGzD,MAAM,CAACuB,GAAG;AAC3B;AACA,CAAC;AAACmC,GAAA,GAFID,QAAQ;AAId,MAAME,UAAU,GAAG3D,MAAM,CAAC2B,MAAM;AAChC;AACA;AACA,SAASb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC2B,EAAE;AACxC,aAAahC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC2B,EAAE;AAC5C;AACA;AACA,WAAWhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,WAAW;AAClD;AACA,mBAAmBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACL,EAAE;AACvD,oBAAoBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,IAAI;AACzD;AACA;AACA,wBAAwBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,mBAAmB;AACvE;AACA,CAAC;AAAC4B,GAAA,GAfID,UAAU;AAiBhB,MAAME,UAAU,GAAG7D,MAAM,CAACuB,GAAG;AAC7B;AACA;AACA,sBAAsBT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiC,OAAO;AACzD,WAAWnC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC8C,WAAW;AAClD;AACA;AACA;AACA;AACA,iBAAiBhD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwB,UAAU,CAACC,QAAQ;AACzD,eAAe1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsB,QAAQ,CAACS,EAAE;AAC/C,CAAC;AAACiB,GAAA,GAXIF,UAAU;AAahB,MAAMG,QAAQ,GAAGhE,MAAM,CAACuB,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAAC0C,IAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGlE,MAAM,CAACsD,IAAI;AAC5B,iBAAiBxC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACwB,UAAU,CAAC4B,MAAM;AACvD,eAAerD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsB,QAAQ,CAACS,EAAE;AAC/C,CAAC;AAACsB,IAAA,GAHIF,QAAQ;AAKd,MAAMG,QAAQ,GAAGrE,MAAM,CAACsD,IAAI;AAC5B,eAAexC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsB,QAAQ,CAACiC,EAAE;AAC/C,WAAWxD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,aAAa;AACpD,CAAC;AAAC2C,IAAA,GAHIF,QAAQ;AAKd,MAAMG,QAAQ,GAAGxE,MAAM,CAACuB,GAAG;AAC3B;AACA;AACA;AACA,gBAAgBT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACmD,EAAE;AAC/C,sBAAsBxD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgC,UAAU;AAC5D,sBAAsBlC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC+B,MAAM;AACxD,mBAAmBjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACT,EAAE;AACvD,gBAAgBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC0D,OAAO,CAACrD,EAAE;AAC/C;AACA;AACA,CAAC;AAACsD,IAAA,GAXIF,QAAQ;AAad,MAAMG,YAAY,GAAG3E,MAAM,CAAC2B,MAAM;AAClC;AACA;AACA;AACA,SAASb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACK,EAAE;AACxC,aAAaV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACK,EAAE;AAC5C;AACA;AACA,WAAWV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACiB,WAAW;AAClD;AACA,eAAenB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACsB,QAAQ,CAACS,EAAE;AAC/C;AACA,oBAAoBhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,IAAI;AACzD;AACA;AACA,wBAAwBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,mBAAmB;AACvE;AACA;AACA;AACA,8BAA8BlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACT,EAAE;AAClE,+BAA+BN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACT,EAAE;AACnE;AACA;AACA;AACA,iCAAiCN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACT,EAAE;AACrE,kCAAkCN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,YAAY,CAACT,EAAE;AACtE;AACA,CAAC;AAACwD,IAAA,GA3BID,YAAY;AA6BlB,MAAME,MAAM,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAG3E,OAAO,CAAC,CAAC;EAClC,MAAM;IAAE4E,YAAY;IAAEC,WAAW;IAAEC;EAAgB,CAAC,GAAG7E,QAAQ,CAAC,CAAC;EACjE,MAAM,CAAC8E,YAAY,EAAEC,eAAe,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM2F,WAAW,GAAG1F,MAAM,CAAC,IAAI,CAAC;EAEhCC,SAAS,CAAC,MAAM;IACd,MAAM0F,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIF,WAAW,CAACG,OAAO,IAAI,CAACH,WAAW,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEN,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAEDO,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMf,MAAM,CAAC,CAAC;IACdK,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMW,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAC5B,IAAI,CAACnB,IAAI,EAAE,OAAO,GAAG;IACrB,OAAO,GAAG,EAAAkB,eAAA,GAAAlB,IAAI,CAACoB,SAAS,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,EAAE,GAAG,EAAAC,cAAA,GAAAnB,IAAI,CAACqB,QAAQ,cAAAF,cAAA,uBAAbA,cAAA,CAAgB,CAAC,CAAC,KAAI,EAAE,EAAE,CAACG,WAAW,CAAC,CAAC;EAChF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,EAACvB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwB,IAAI,GAAE,OAAO,MAAM;IAC9B,OAAOxB,IAAI,CAACwB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC,GAAGtB,IAAI,CAACwB,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,oBACEhG,OAAA,CAACC,eAAe;IAAAgG,QAAA,gBACdjG,OAAA,CAACW,WAAW;MAAAsF,QAAA,gBACVjG,OAAA,CAACe,UAAU;QAACmF,OAAO,EAAE/B,eAAgB;QAAA8B,QAAA,eACnCjG,OAAA,CAACV,MAAM;UAAC6G,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACbvG,OAAA,CAACwB,SAAS;QAAAyE,QAAA,EAAC;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEdvG,OAAA,CAAC+B,YAAY;MAAAkE,QAAA,gBACXjG,OAAA,CAACiC,aAAa;QACZuE,KAAK,EAAEhC,YAAa;QACpBiC,QAAQ,EAAGC,CAAC,IAAKjC,WAAW,CAACiC,CAAC,CAACxB,MAAM,CAACsB,KAAK,CAAE;QAAAP,QAAA,EAE5CvB,eAAe,CAACiC,GAAG,CAACvG,KAAK,iBACxBJ,OAAA;UAAoBwG,KAAK,EAAEpG,KAAM;UAAA6F,QAAA,GAC9B7F,KAAK,CAAC2F,MAAM,CAAC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC,GAAGxF,KAAK,CAAC4F,KAAK,CAAC,CAAC,CAAC,EAAC,QAClD;QAAA,GAFa5F,KAAK;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBvG,OAAA,CAACwC,kBAAkB;QAAAyD,QAAA,gBACjBjG,OAAA,CAACT,MAAM;UAAC4G,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpBvG,OAAA,CAAC0C,iBAAiB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAErBvG,OAAA,CAAC8C,QAAQ;QAAC8D,GAAG,EAAE/B,WAAY;QAAAoB,QAAA,gBACzBjG,OAAA,CAACgD,UAAU;UAACkD,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,CAACD,YAAY,CAAE;UAAAsB,QAAA,gBACxDjG,OAAA,CAACkD,UAAU;YAAA+C,QAAA,EAAEV,eAAe,CAAC;UAAC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC5CvG,OAAA,CAACqD,QAAQ;YAAA4C,QAAA,gBACPjG,OAAA,CAACuD,QAAQ;cAAA0C,QAAA,GAAE3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,SAAS,EAAC,GAAC,EAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,QAAQ;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvDvG,OAAA,CAAC0D,QAAQ;cAAAuC,QAAA,EAAEJ,WAAW,CAAC;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACXvG,OAAA,CAACL,aAAa;YAACwG,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,EAEZ5B,YAAY,iBACX3E,OAAA,CAAC6D,QAAQ;UAAAoC,QAAA,gBACPjG,OAAA,CAACgE,YAAY;YAAAiC,QAAA,gBACXjG,OAAA,CAACR,MAAM;cAAC2G,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfvG,OAAA,CAACgE,YAAY;YAAAiC,QAAA,gBACXjG,OAAA,CAACP,UAAU;cAAC0G,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE1B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfvG,OAAA,CAACgE,YAAY;YAACkC,OAAO,EAAEZ,YAAa;YAAAW,QAAA,gBAClCjG,OAAA,CAACN,QAAQ;cAACyG,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEtB,CAAC;AAAClC,EAAA,CAxFIH,MAAM;EAAA,QACetE,OAAO,EACuBC,QAAQ;AAAA;AAAAgH,IAAA,GAF3D3C,MAAM;AA0FZ,eAAeA,MAAM;AAAC,IAAAxD,EAAA,EAAAI,GAAA,EAAAS,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA4C,IAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}