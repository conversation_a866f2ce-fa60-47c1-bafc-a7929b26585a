{"ast": null, "code": "import api from './axios';\nexport const rolesAPI = {\n  getRoles: (params = {}) => {\n    return api.get('/roles', {\n      params\n    });\n  },\n  getRoleById: id => {\n    return api.get(`/roles/${id}`);\n  },\n  createRole: roleData => {\n    return api.post('/roles', roleData);\n  },\n  updateRole: (id, roleData) => {\n    return api.put(`/roles/${id}`, roleData);\n  },\n  deleteRole: id => {\n    return api.delete(`/roles/${id}`);\n  }\n};", "map": {"version": 3, "names": ["api", "rolesAPI", "getRoles", "params", "get", "getRoleById", "id", "createRole", "roleData", "post", "updateRole", "put", "deleteRole", "delete"], "sources": ["D:/Projects/qmsus/frontend/src/api/roles.js"], "sourcesContent": ["import api from './axios';\n\nexport const rolesAPI = {\n  getRoles: (params = {}) => {\n    return api.get('/roles', { params });\n  },\n\n  getRoleById: (id) => {\n    return api.get(`/roles/${id}`);\n  },\n\n  createRole: (roleData) => {\n    return api.post('/roles', roleData);\n  },\n\n  updateRole: (id, roleData) => {\n    return api.put(`/roles/${id}`, roleData);\n  },\n\n  deleteRole: (id) => {\n    return api.delete(`/roles/${id}`);\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,SAAS;AAEzB,OAAO,MAAMC,QAAQ,GAAG;EACtBC,QAAQ,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IACzB,OAAOH,GAAG,CAACI,GAAG,CAAC,QAAQ,EAAE;MAAED;IAAO,CAAC,CAAC;EACtC,CAAC;EAEDE,WAAW,EAAGC,EAAE,IAAK;IACnB,OAAON,GAAG,CAACI,GAAG,CAAC,UAAUE,EAAE,EAAE,CAAC;EAChC,CAAC;EAEDC,UAAU,EAAGC,QAAQ,IAAK;IACxB,OAAOR,GAAG,CAACS,IAAI,CAAC,QAAQ,EAAED,QAAQ,CAAC;EACrC,CAAC;EAEDE,UAAU,EAAEA,CAACJ,EAAE,EAAEE,QAAQ,KAAK;IAC5B,OAAOR,GAAG,CAACW,GAAG,CAAC,UAAUL,EAAE,EAAE,EAAEE,QAAQ,CAAC;EAC1C,CAAC;EAEDI,UAAU,EAAGN,EAAE,IAAK;IAClB,OAAON,GAAG,CAACa,MAAM,CAAC,UAAUP,EAAE,EAAE,CAAC;EACnC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}