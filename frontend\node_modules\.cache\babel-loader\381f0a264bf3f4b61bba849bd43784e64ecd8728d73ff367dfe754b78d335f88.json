{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\components\\\\common\\\\Modal.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport styled from 'styled-components';\nimport { FiX } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModalOverlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: ${props => props.theme.spacing.md};\n`;\n_c = ModalOverlay;\nconst ModalContainer = styled.div`\n  background-color: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.xl};\n  width: 100%;\n  max-width: ${props => props.maxWidth || '500px'};\n  max-height: 90vh;\n  overflow-y: auto;\n  position: relative;\n`;\n_c2 = ModalContainer;\nconst ModalHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n`;\n_c3 = ModalHeader;\nconst ModalTitle = styled.h2`\n  font-size: ${props => props.theme.fontSize.xl};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0;\n`;\n_c4 = ModalTitle;\nconst CloseButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textSecondary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n`;\n_c5 = CloseButton;\nconst ModalBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n`;\n_c6 = ModalBody;\nconst Modal = ({\n  title,\n  children,\n  onClose,\n  maxWidth,\n  closeOnOverlayClick = true,\n  ...props\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    document.addEventListener('keydown', handleEscape);\n    document.body.style.overflow = 'hidden';\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [onClose]);\n  const handleOverlayClick = e => {\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ModalOverlay, {\n    onClick: handleOverlayClick,\n    children: /*#__PURE__*/_jsxDEV(ModalContainer, {\n      maxWidth: maxWidth,\n      ...props,\n      children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n        children: [/*#__PURE__*/_jsxDEV(ModalTitle, {\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CloseButton, {\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(FiX, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModalBody, {\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(Modal, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c7 = Modal;\nexport default Modal;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ModalOverlay\");\n$RefreshReg$(_c2, \"ModalContainer\");\n$RefreshReg$(_c3, \"ModalHeader\");\n$RefreshReg$(_c4, \"ModalTitle\");\n$RefreshReg$(_c5, \"CloseButton\");\n$RefreshReg$(_c6, \"ModalBody\");\n$RefreshReg$(_c7, \"Modal\");", "map": {"version": 3, "names": ["React", "useEffect", "styled", "FiX", "jsxDEV", "_jsxDEV", "ModalOverlay", "div", "props", "theme", "spacing", "md", "_c", "ModalContainer", "colors", "background", "borderRadius", "lg", "shadows", "xl", "max<PERSON><PERSON><PERSON>", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "border", "_c3", "ModalTitle", "h2", "fontSize", "fontWeight", "semibold", "textPrimary", "_c4", "CloseButton", "button", "textSecondary", "transitions", "fast", "backgroundSecondary", "_c5", "ModalBody", "_c6", "Modal", "title", "children", "onClose", "closeOnOverlayClick", "_s", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "handleOverlayClick", "target", "currentTarget", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "_c7", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/components/common/Modal.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport styled from 'styled-components';\nimport { FiX } from 'react-icons/fi';\n\nconst ModalOverlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: ${props => props.theme.spacing.md};\n`;\n\nconst ModalContainer = styled.div`\n  background-color: ${props => props.theme.colors.background};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.shadows.xl};\n  width: 100%;\n  max-width: ${props => props.maxWidth || '500px'};\n  max-height: 90vh;\n  overflow-y: auto;\n  position: relative;\n`;\n\nconst ModalHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n`;\n\nconst ModalTitle = styled.h2`\n  font-size: ${props => props.theme.fontSize.xl};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst CloseButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border: none;\n  background: none;\n  color: ${props => props.theme.colors.textSecondary};\n  cursor: pointer;\n  border-radius: ${props => props.theme.borderRadius.md};\n  transition: all ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n`;\n\nconst ModalBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n`;\n\nconst Modal = ({ \n  title, \n  children, \n  onClose, \n  maxWidth,\n  closeOnOverlayClick = true,\n  ...props \n}) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    document.body.style.overflow = 'hidden';\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [onClose]);\n\n  const handleOverlayClick = (e) => {\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <ModalOverlay onClick={handleOverlayClick}>\n      <ModalContainer maxWidth={maxWidth} {...props}>\n        <ModalHeader>\n          <ModalTitle>{title}</ModalTitle>\n          <CloseButton onClick={onClose}>\n            <FiX size={20} />\n          </CloseButton>\n        </ModalHeader>\n        <ModalBody>\n          {children}\n        </ModalBody>\n      </ModalContainer>\n    </ModalOverlay>\n  );\n};\n\nexport default Modal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,GAAG,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,YAAY,GAAGJ,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C,CAAC;AAACC,EAAA,GAZIN,YAAY;AAclB,MAAMO,cAAc,GAAGX,MAAM,CAACK,GAAG;AACjC,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACC,UAAU;AAC5D,mBAAmBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,YAAY,CAACC,EAAE;AACvD,gBAAgBT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,OAAO,CAACC,EAAE;AAC/C;AACA,eAAeX,KAAK,IAAIA,KAAK,CAACY,QAAQ,IAAI,OAAO;AACjD;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIR,cAAc;AAWpB,MAAMS,WAAW,GAAGpB,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACO,EAAE;AAC5C,6BAA6BT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACS,MAAM;AAC/D,CAAC;AAACC,GAAA,GANIF,WAAW;AAQjB,MAAMG,UAAU,GAAGvB,MAAM,CAACwB,EAAE;AAC5B,eAAelB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACkB,QAAQ,CAACR,EAAE;AAC/C,iBAAiBX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACmB,UAAU,CAACC,QAAQ;AACzD,WAAWrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACgB,WAAW;AAClD;AACA,CAAC;AAACC,GAAA,GALIN,UAAU;AAOhB,MAAMO,WAAW,GAAG9B,MAAM,CAAC+B,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACoB,aAAa;AACpD;AACA,mBAAmB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACO,YAAY,CAACL,EAAE;AACvD,oBAAoBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC0B,WAAW,CAACC,IAAI;AACzD;AACA;AACA,wBAAwB5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACuB,mBAAmB;AACvE,aAAa7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACK,MAAM,CAACgB,WAAW;AACpD;AACA,CAAC;AAACQ,GAAA,GAjBIN,WAAW;AAmBjB,MAAMO,SAAS,GAAGrC,MAAM,CAACK,GAAG;AAC5B,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACO,EAAE;AAC5C,CAAC;AAACuB,GAAA,GAFID,SAAS;AAIf,MAAME,KAAK,GAAGA,CAAC;EACbC,KAAK;EACLC,QAAQ;EACRC,OAAO;EACPxB,QAAQ;EACRyB,mBAAmB,GAAG,IAAI;EAC1B,GAAGrC;AACL,CAAC,KAAK;EAAAsC,EAAA;EACJ7C,SAAS,CAAC,MAAM;IACd,MAAM8C,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBL,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDM,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;IAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC;EAEb,MAAMY,kBAAkB,GAAIR,CAAC,IAAK;IAChC,IAAIH,mBAAmB,IAAIG,CAAC,CAACS,MAAM,KAAKT,CAAC,CAACU,aAAa,EAAE;MACvDd,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEvC,OAAA,CAACC,YAAY;IAACqD,OAAO,EAAEH,kBAAmB;IAAAb,QAAA,eACxCtC,OAAA,CAACQ,cAAc;MAACO,QAAQ,EAAEA,QAAS;MAAA,GAAKZ,KAAK;MAAAmC,QAAA,gBAC3CtC,OAAA,CAACiB,WAAW;QAAAqB,QAAA,gBACVtC,OAAA,CAACoB,UAAU;UAAAkB,QAAA,EAAED;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAChC1D,OAAA,CAAC2B,WAAW;UAAC2B,OAAO,EAAEf,OAAQ;UAAAD,QAAA,eAC5BtC,OAAA,CAACF,GAAG;YAAC6D,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACd1D,OAAA,CAACkC,SAAS;QAAAI,QAAA,EACPA;MAAQ;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEnB,CAAC;AAACjB,EAAA,CA7CIL,KAAK;AAAAwB,GAAA,GAALxB,KAAK;AA+CX,eAAeA,KAAK;AAAC,IAAA7B,EAAA,EAAAS,GAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAAtD,EAAA;AAAAsD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}