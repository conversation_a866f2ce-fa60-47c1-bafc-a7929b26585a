{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\context\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ThemeProvider as StyledThemeProvider } from 'styled-components';\nimport { themeVariants, defaultTheme } from '../theme/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const [currentTheme, setCurrentTheme] = useState(() => {\n    const savedTheme = localStorage.getItem('qms-theme');\n    return savedTheme || 'blue';\n  });\n  useEffect(() => {\n    localStorage.setItem('qms-theme', currentTheme);\n  }, [currentTheme]);\n  const changeTheme = themeName => {\n    if (themeVariants[themeName]) {\n      setCurrentTheme(themeName);\n    }\n  };\n  const theme = themeVariants[currentTheme] || defaultTheme;\n  const value = {\n    currentTheme,\n    changeTheme,\n    availableThemes: Object.keys(themeVariants),\n    theme\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: /*#__PURE__*/_jsxDEV(StyledThemeProvider, {\n      theme: theme,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeProvider, \"PRs595XZRLLdum7PhL8ol3J7TEA=\");\n_c = ThemeProvider;\nexport const useTheme = () => {\n  _s2();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s2(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default ThemeContext;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "ThemeProvider", "StyledThemeProvider", "themeVariants", "defaultTheme", "jsxDEV", "_jsxDEV", "ThemeContext", "children", "_s", "currentTheme", "setCurrentTheme", "savedTheme", "localStorage", "getItem", "setItem", "changeTheme", "themeName", "theme", "value", "availableThemes", "Object", "keys", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTheme", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/context/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ThemeProvider as StyledThemeProvider } from 'styled-components';\nimport { themeVariants, defaultTheme } from '../theme/theme';\n\nconst ThemeContext = createContext();\n\nexport const ThemeProvider = ({ children }) => {\n  const [currentTheme, setCurrentTheme] = useState(() => {\n    const savedTheme = localStorage.getItem('qms-theme');\n    return savedTheme || 'blue';\n  });\n\n  useEffect(() => {\n    localStorage.setItem('qms-theme', currentTheme);\n  }, [currentTheme]);\n\n  const changeTheme = (themeName) => {\n    if (themeVariants[themeName]) {\n      setCurrentTheme(themeName);\n    }\n  };\n\n  const theme = themeVariants[currentTheme] || defaultTheme;\n\n  const value = {\n    currentTheme,\n    changeTheme,\n    availableThemes: Object.keys(themeVariants),\n    theme\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      <StyledThemeProvider theme={theme}>\n        {children}\n      </StyledThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport default ThemeContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,mBAAmB;AACxE,SAASC,aAAa,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,YAAY,gBAAGV,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEO;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,MAAM;IACrD,MAAMa,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,OAAOF,UAAU,IAAI,MAAM;EAC7B,CAAC,CAAC;EAEFZ,SAAS,CAAC,MAAM;IACda,YAAY,CAACE,OAAO,CAAC,WAAW,EAAEL,YAAY,CAAC;EACjD,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMM,WAAW,GAAIC,SAAS,IAAK;IACjC,IAAId,aAAa,CAACc,SAAS,CAAC,EAAE;MAC5BN,eAAe,CAACM,SAAS,CAAC;IAC5B;EACF,CAAC;EAED,MAAMC,KAAK,GAAGf,aAAa,CAACO,YAAY,CAAC,IAAIN,YAAY;EAEzD,MAAMe,KAAK,GAAG;IACZT,YAAY;IACZM,WAAW;IACXI,eAAe,EAAEC,MAAM,CAACC,IAAI,CAACnB,aAAa,CAAC;IAC3Ce;EACF,CAAC;EAED,oBACEZ,OAAA,CAACC,YAAY,CAACgB,QAAQ;IAACJ,KAAK,EAAEA,KAAM;IAAAX,QAAA,eAClCF,OAAA,CAACJ,mBAAmB;MAACgB,KAAK,EAAEA,KAAM;MAAAV,QAAA,EAC/BA;IAAQ;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE5B,CAAC;AAAClB,EAAA,CAhCWR,aAAa;AAAA2B,EAAA,GAAb3B,aAAa;AAkC1B,OAAO,MAAM4B,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,MAAMC,OAAO,GAAGjC,UAAU,CAACS,YAAY,CAAC;EACxC,IAAI,CAACwB,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,QAAQ;AAQrB,eAAetB,YAAY;AAAC,IAAAqB,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}