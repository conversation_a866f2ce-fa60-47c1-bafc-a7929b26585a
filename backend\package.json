{"name": "qms-backend", "version": "1.0.0", "description": "QMS ERP Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "test": "jest"}, "dependencies": {"express": "^4.18.2", "sequelize": "^6.32.1", "mysql2": "^3.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1"}, "devDependencies": {"nodemon": "^3.0.1", "sequelize-cli": "^6.6.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["erp", "nodejs", "express", "sequelize", "mysql"], "author": "QMS Team", "license": "MIT"}