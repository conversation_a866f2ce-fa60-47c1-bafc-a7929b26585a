import React from 'react';
import styled from 'styled-components';

const TableWrapper = styled.div`
  overflow-x: auto;
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 1px solid ${props => props.theme.colors.border};
`;

const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: ${props => props.theme.colors.background};
`;

const TableHead = styled.thead`
  background-color: ${props => props.theme.colors.backgroundSecondary};
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  &:not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.border};
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundTertiary};
  }
`;

const TableHeader = styled.th`
  padding: ${props => props.theme.spacing.md};
  text-align: left;
  font-weight: ${props => props.theme.fontWeight.semibold};
  color: ${props => props.theme.colors.textPrimary};
  font-size: ${props => props.theme.fontSize.sm};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const TableCell = styled.td`
  padding: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.textPrimary};
  font-size: ${props => props.theme.fontSize.sm};
  vertical-align: middle;
`;

const EmptyState = styled.div`
  padding: ${props => props.theme.spacing.xxl};
  text-align: center;
  color: ${props => props.theme.colors.textMuted};
`;

const LoadingState = styled.div`
  padding: ${props => props.theme.spacing.xxl};
  text-align: center;
  color: ${props => props.theme.colors.textMuted};
`;

const Table = ({ 
  columns = [], 
  data = [], 
  loading = false, 
  emptyMessage = "No data available",
  ...props 
}) => {
  if (loading) {
    return (
      <TableWrapper>
        <LoadingState>Loading...</LoadingState>
      </TableWrapper>
    );
  }

  if (data.length === 0) {
    return (
      <TableWrapper>
        <EmptyState>{emptyMessage}</EmptyState>
      </TableWrapper>
    );
  }

  return (
    <TableWrapper {...props}>
      <StyledTable>
        <TableHead>
          <TableRow>
            {columns.map((column, index) => (
              <TableHeader key={index}>
                {column.header}
              </TableHeader>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, rowIndex) => (
            <TableRow key={rowIndex}>
              {columns.map((column, colIndex) => (
                <TableCell key={colIndex}>
                  {column.render 
                    ? column.render(row[column.accessor], row, rowIndex)
                    : row[column.accessor]
                  }
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </StyledTable>
    </TableWrapper>
  );
};

Table.Wrapper = TableWrapper;
Table.Table = StyledTable;
Table.Head = TableHead;
Table.Body = TableBody;
Table.Row = TableRow;
Table.Header = TableHeader;
Table.Cell = TableCell;

export default Table;
