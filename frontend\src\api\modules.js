import api from './axios';

export const modulesAPI = {
  getModules: (params = {}) => {
    return api.get('/modules', { params });
  },

  getModuleById: (id) => {
    return api.get(`/modules/${id}`);
  },

  createModule: (moduleData) => {
    return api.post('/modules', moduleData);
  },

  updateModule: (id, moduleData) => {
    return api.put(`/modules/${id}`, moduleData);
  },

  deleteModule: (id) => {
    return api.delete(`/modules/${id}`);
  }
};
