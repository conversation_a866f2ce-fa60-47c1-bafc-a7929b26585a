{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { AuthProvider } from './context/AuthContext';\nimport { ThemeProvider } from './context/ThemeContext';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport GlobalStyles from './styles/GlobalStyles';\n\n// Pages\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport UsersPage from './pages/settings/UsersPage';\nimport RolesPage from './pages/settings/RolesPage';\nimport ModulesPage from './pages/settings/ModulesPage';\n\n// Layout\nimport DashboardLayout from './layouts/DashboardLayout';\n\n// Create a client\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: [/*#__PURE__*/_jsxDEV(GlobalStyles, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 40\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/dashboard\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(DashboardLayout, {\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/settings/users\",\n                element: /*#__PURE__*/_jsxDEV(DashboardLayout, {\n                  children: /*#__PURE__*/_jsxDEV(UsersPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 55,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/settings/roles\",\n                element: /*#__PURE__*/_jsxDEV(DashboardLayout, {\n                  children: /*#__PURE__*/_jsxDEV(RolesPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/settings/modules\",\n                element: /*#__PURE__*/_jsxDEV(DashboardLayout, {\n                  children: /*#__PURE__*/_jsxDEV(ModulesPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 65,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n          position: \"top-right\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          newestOnTop: false,\n          closeOnClick: true,\n          rtl: false,\n          pauseOnFocusLoss: true,\n          draggable: true,\n          pauseOnHover: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "QueryClient", "QueryClientProvider", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "ProtectedRoute", "GlobalStyles", "<PERSON><PERSON>", "Dashboard", "UsersPage", "RolesPage", "ModulesPage", "DashboardLayout", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "App", "client", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "_c", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nimport { AuthProvider } from './context/AuthContext';\nimport { ThemeProvider } from './context/ThemeContext';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport GlobalStyles from './styles/GlobalStyles';\n\n// Pages\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport UsersPage from './pages/settings/UsersPage';\nimport RolesPage from './pages/settings/RolesPage';\nimport ModulesPage from './pages/settings/ModulesPage';\n\n// Layout\nimport DashboardLayout from './layouts/DashboardLayout';\n\n// Create a client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <ThemeProvider>\n        <AuthProvider>\n          <GlobalStyles />\n          <Router>\n            <Routes>\n              {/* Public Routes */}\n              <Route path=\"/login\" element={<Login />} />\n              \n              {/* Protected Routes */}\n              <Route path=\"/\" element={<ProtectedRoute />}>\n                <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n                <Route path=\"/dashboard\" element={\n                  <DashboardLayout>\n                    <Dashboard />\n                  </DashboardLayout>\n                } />\n                \n                {/* Settings Routes */}\n                <Route path=\"/settings/users\" element={\n                  <DashboardLayout>\n                    <UsersPage />\n                  </DashboardLayout>\n                } />\n                <Route path=\"/settings/roles\" element={\n                  <DashboardLayout>\n                    <RolesPage />\n                  </DashboardLayout>\n                } />\n                <Route path=\"/settings/modules\" element={\n                  <DashboardLayout>\n                    <ModulesPage />\n                  </DashboardLayout>\n                } />\n              </Route>\n              \n              {/* Catch all route */}\n              <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n            </Routes>\n          </Router>\n          \n          <ToastContainer\n            position=\"top-right\"\n            autoClose={5000}\n            hideProgressBar={false}\n            newestOnTop={false}\n            closeOnClick\n            rtl={false}\n            pauseOnFocusLoss\n            draggable\n            pauseOnHover\n          />\n        </AuthProvider>\n      </ThemeProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,YAAY,MAAM,uBAAuB;;AAEhD;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,WAAW,MAAM,8BAA8B;;AAEtD;AACA,OAAOC,eAAe,MAAM,2BAA2B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIf,WAAW,CAAC;EAClCgB,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE;IACxB;EACF;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEN,OAAA,CAACb,mBAAmB;IAACoB,MAAM,EAAEN,WAAY;IAAAO,QAAA,eACvCR,OAAA,CAACV,aAAa;MAAAkB,QAAA,eACZR,OAAA,CAACX,YAAY;QAAAmB,QAAA,gBACXR,OAAA,CAACR,YAAY;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChBZ,OAAA,CAAClB,MAAM;UAAA0B,QAAA,eACLR,OAAA,CAACjB,MAAM;YAAAyB,QAAA,gBAELR,OAAA,CAAChB,KAAK;cAAC6B,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEd,OAAA,CAACP,KAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG3CZ,OAAA,CAAChB,KAAK;cAAC6B,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEd,OAAA,CAACT,cAAc;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,gBAC1CR,OAAA,CAAChB,KAAK;gBAAC6B,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAEd,OAAA,CAACf,QAAQ;kBAAC8B,EAAE,EAAC,YAAY;kBAACC,OAAO;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjEZ,OAAA,CAAChB,KAAK;gBAAC6B,IAAI,EAAC,YAAY;gBAACC,OAAO,eAC9Bd,OAAA,CAACF,eAAe;kBAAAU,QAAA,eACdR,OAAA,CAACN,SAAS;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAClB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGJZ,OAAA,CAAChB,KAAK;gBAAC6B,IAAI,EAAC,iBAAiB;gBAACC,OAAO,eACnCd,OAAA,CAACF,eAAe;kBAAAU,QAAA,eACdR,OAAA,CAACL,SAAS;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAClB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJZ,OAAA,CAAChB,KAAK;gBAAC6B,IAAI,EAAC,iBAAiB;gBAACC,OAAO,eACnCd,OAAA,CAACF,eAAe;kBAAAU,QAAA,eACdR,OAAA,CAACJ,SAAS;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAClB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACJZ,OAAA,CAAChB,KAAK;gBAAC6B,IAAI,EAAC,mBAAmB;gBAACC,OAAO,eACrCd,OAAA,CAACF,eAAe;kBAAAU,QAAA,eACdR,OAAA,CAACH,WAAW;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAClB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGRZ,OAAA,CAAChB,KAAK;cAAC6B,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEd,OAAA,CAACf,QAAQ;gBAAC8B,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAETZ,OAAA,CAACZ,cAAc;UACb6B,QAAQ,EAAC,WAAW;UACpBC,SAAS,EAAE,IAAK;UAChBC,eAAe,EAAE,KAAM;UACvBC,WAAW,EAAE,KAAM;UACnBC,YAAY;UACZC,GAAG,EAAE,KAAM;UACXC,gBAAgB;UAChBC,SAAS;UACTC,YAAY;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE1B;AAACc,EAAA,GA1DQpB,GAAG;AA4DZ,eAAeA,GAAG;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}