{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { noop, parseMutationArgs } from '../core/utils';\nimport { MutationObserver } from '../core/mutationObserver';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils'; // HOOK\n\nexport function useMutation(arg1, arg2, arg3) {\n  var mountedRef = React.useRef(false);\n  var _React$useState = React.useState(0),\n    forceUpdate = _React$useState[1];\n  var options = parseMutationArgs(arg1, arg2, arg3);\n  var queryClient = useQueryClient();\n  var obsRef = React.useRef();\n  if (!obsRef.current) {\n    obsRef.current = new MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n  var currentResult = obsRef.current.getCurrentResult();\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = React.useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(noop);\n  }, []);\n  if (currentResult.error && shouldThrowError(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n  return _extends({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "notify<PERSON><PERSON>ger", "noop", "parseMutationArgs", "MutationObserver", "useQueryClient", "shouldThrowError", "useMutation", "arg1", "arg2", "arg3", "mountedRef", "useRef", "_React$useState", "useState", "forceUpdate", "options", "queryClient", "obsRef", "current", "setOptions", "currentResult", "getCurrentResult", "useEffect", "unsubscribe", "subscribe", "batchCalls", "x", "mutate", "useCallback", "variables", "mutateOptions", "catch", "error", "undefined", "useErrorBoundary", "mutateAsync"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-query/es/react/useMutation.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { noop, parseMutationArgs } from '../core/utils';\nimport { MutationObserver } from '../core/mutationObserver';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils'; // HOOK\n\nexport function useMutation(arg1, arg2, arg3) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var options = parseMutationArgs(arg1, arg2, arg3);\n  var queryClient = useQueryClient();\n  var obsRef = React.useRef();\n\n  if (!obsRef.current) {\n    obsRef.current = new MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n\n  var currentResult = obsRef.current.getCurrentResult();\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = React.useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(noop);\n  }, []);\n\n  if (currentResult.error && shouldThrowError(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n\n  return _extends({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,IAAI,EAAEC,iBAAiB,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,gBAAgB,QAAQ,SAAS,CAAC,CAAC;;AAE5C,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC5C,IAAIC,UAAU,GAAGX,KAAK,CAACY,MAAM,CAAC,KAAK,CAAC;EAEpC,IAAIC,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;IACnCC,WAAW,GAAGF,eAAe,CAAC,CAAC,CAAC;EAEpC,IAAIG,OAAO,GAAGb,iBAAiB,CAACK,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACjD,IAAIO,WAAW,GAAGZ,cAAc,CAAC,CAAC;EAClC,IAAIa,MAAM,GAAGlB,KAAK,CAACY,MAAM,CAAC,CAAC;EAE3B,IAAI,CAACM,MAAM,CAACC,OAAO,EAAE;IACnBD,MAAM,CAACC,OAAO,GAAG,IAAIf,gBAAgB,CAACa,WAAW,EAAED,OAAO,CAAC;EAC7D,CAAC,MAAM;IACLE,MAAM,CAACC,OAAO,CAACC,UAAU,CAACJ,OAAO,CAAC;EACpC;EAEA,IAAIK,aAAa,GAAGH,MAAM,CAACC,OAAO,CAACG,gBAAgB,CAAC,CAAC;EACrDtB,KAAK,CAACuB,SAAS,CAAC,YAAY;IAC1BZ,UAAU,CAACQ,OAAO,GAAG,IAAI;IACzB,IAAIK,WAAW,GAAGN,MAAM,CAACC,OAAO,CAACM,SAAS,CAACxB,aAAa,CAACyB,UAAU,CAAC,YAAY;MAC9E,IAAIf,UAAU,CAACQ,OAAO,EAAE;QACtBJ,WAAW,CAAC,UAAUY,CAAC,EAAE;UACvB,OAAOA,CAAC,GAAG,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CAAC;IACH,OAAO,YAAY;MACjBhB,UAAU,CAACQ,OAAO,GAAG,KAAK;MAC1BK,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAII,MAAM,GAAG5B,KAAK,CAAC6B,WAAW,CAAC,UAAUC,SAAS,EAAEC,aAAa,EAAE;IACjEb,MAAM,CAACC,OAAO,CAACS,MAAM,CAACE,SAAS,EAAEC,aAAa,CAAC,CAACC,KAAK,CAAC9B,IAAI,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EAEN,IAAImB,aAAa,CAACY,KAAK,IAAI3B,gBAAgB,CAAC4B,SAAS,EAAEhB,MAAM,CAACC,OAAO,CAACH,OAAO,CAACmB,gBAAgB,EAAE,CAACd,aAAa,CAACY,KAAK,CAAC,CAAC,EAAE;IACtH,MAAMZ,aAAa,CAACY,KAAK;EAC3B;EAEA,OAAOlC,QAAQ,CAAC,CAAC,CAAC,EAAEsB,aAAa,EAAE;IACjCO,MAAM,EAAEA,MAAM;IACdQ,WAAW,EAAEf,aAAa,CAACO;EAC7B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}