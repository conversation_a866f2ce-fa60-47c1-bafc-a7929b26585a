const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken, authorize } = require('../middleware/auth');
const { validateUser, validateId, validatePagination } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// GET /api/users - Get all users with pagination and search
router.get('/', validatePagination, userController.getUsers);

// GET /api/users/:id - Get user by ID
router.get('/:id', validateId, userController.getUserById);

// POST /api/users - Create new user (requires admin permission)
router.post('/', 
  authorize(['users.create']), 
  validateUser, 
  userController.createUser
);

// PUT /api/users/:id - Update user (requires admin permission)
router.put('/:id', 
  authorize(['users.update']), 
  validateId, 
  validateUser, 
  userController.updateUser
);

// DELETE /api/users/:id - Delete user (requires admin permission)
router.delete('/:id', 
  authorize(['users.delete']), 
  validateId, 
  userController.deleteUser
);

module.exports = router;
