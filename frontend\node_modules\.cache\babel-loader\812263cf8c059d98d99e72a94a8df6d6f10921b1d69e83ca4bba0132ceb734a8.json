{"ast": null, "code": "import api from './axios';\nexport const usersAPI = {\n  getUsers: (params = {}) => {\n    return api.get('/users', {\n      params\n    });\n  },\n  getUserById: id => {\n    return api.get(`/users/${id}`);\n  },\n  createUser: userData => {\n    return api.post('/users', userData);\n  },\n  updateUser: (id, userData) => {\n    return api.put(`/users/${id}`, userData);\n  },\n  deleteUser: id => {\n    return api.delete(`/users/${id}`);\n  }\n};", "map": {"version": 3, "names": ["api", "usersAPI", "getUsers", "params", "get", "getUserById", "id", "createUser", "userData", "post", "updateUser", "put", "deleteUser", "delete"], "sources": ["D:/Projects/qmsus/frontend/src/api/users.js"], "sourcesContent": ["import api from './axios';\n\nexport const usersAPI = {\n  getUsers: (params = {}) => {\n    return api.get('/users', { params });\n  },\n\n  getUserById: (id) => {\n    return api.get(`/users/${id}`);\n  },\n\n  createUser: (userData) => {\n    return api.post('/users', userData);\n  },\n\n  updateUser: (id, userData) => {\n    return api.put(`/users/${id}`, userData);\n  },\n\n  deleteUser: (id) => {\n    return api.delete(`/users/${id}`);\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,SAAS;AAEzB,OAAO,MAAMC,QAAQ,GAAG;EACtBC,QAAQ,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IACzB,OAAOH,GAAG,CAACI,GAAG,CAAC,QAAQ,EAAE;MAAED;IAAO,CAAC,CAAC;EACtC,CAAC;EAEDE,WAAW,EAAGC,EAAE,IAAK;IACnB,OAAON,GAAG,CAACI,GAAG,CAAC,UAAUE,EAAE,EAAE,CAAC;EAChC,CAAC;EAEDC,UAAU,EAAGC,QAAQ,IAAK;IACxB,OAAOR,GAAG,CAACS,IAAI,CAAC,QAAQ,EAAED,QAAQ,CAAC;EACrC,CAAC;EAEDE,UAAU,EAAEA,CAACJ,EAAE,EAAEE,QAAQ,KAAK;IAC5B,OAAOR,GAAG,CAACW,GAAG,CAAC,UAAUL,EAAE,EAAE,EAAEE,QAAQ,CAAC;EAC1C,CAAC;EAEDI,UAAU,EAAGN,EAAE,IAAK;IAClB,OAAON,GAAG,CAACa,MAAM,CAAC,UAAUP,EAAE,EAAE,CAAC;EACnC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}