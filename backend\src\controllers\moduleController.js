const { Module } = require('../models');
const { Op } = require('sequelize');

const getModules = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = search ? {
      [Op.or]: [
        { name: { [Op.like]: `%${search}%` } },
        { code: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ]
    } : {};

    const { count, rows } = await Module.findAndCountAll({
      where: whereClause,
      include: [{
        model: Module,
        as: 'parent',
        attributes: ['id', 'name', 'code']
      }, {
        model: Module,
        as: 'children',
        attributes: ['id', 'name', 'code']
      }],
      order: [['sortOrder', 'ASC'], ['name', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        modules: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

const getModuleById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const module = await Module.findByPk(id, {
      include: [{
        model: Module,
        as: 'parent',
        attributes: ['id', 'name', 'code']
      }, {
        model: Module,
        as: 'children',
        attributes: ['id', 'name', 'code']
      }]
    });

    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }

    res.json({
      success: true,
      data: { module }
    });
  } catch (error) {
    next(error);
  }
};

const createModule = async (req, res, next) => {
  try {
    const { name, description, code, icon, route, parentId, sortOrder } = req.body;

    const module = await Module.create({
      name,
      description,
      code,
      icon,
      route,
      parentId,
      sortOrder
    });

    const createdModule = await Module.findByPk(module.id, {
      include: [{
        model: Module,
        as: 'parent',
        attributes: ['id', 'name', 'code']
      }]
    });

    res.status(201).json({
      success: true,
      message: 'Module created successfully',
      data: { module: createdModule }
    });
  } catch (error) {
    next(error);
  }
};

const updateModule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, code, icon, route, parentId, sortOrder, isActive } = req.body;

    const module = await Module.findByPk(id);
    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }

    await module.update({
      name,
      description,
      code,
      icon,
      route,
      parentId,
      sortOrder,
      isActive
    });

    const updatedModule = await Module.findByPk(id, {
      include: [{
        model: Module,
        as: 'parent',
        attributes: ['id', 'name', 'code']
      }]
    });

    res.json({
      success: true,
      message: 'Module updated successfully',
      data: { module: updatedModule }
    });
  } catch (error) {
    next(error);
  }
};

const deleteModule = async (req, res, next) => {
  try {
    const { id } = req.params;

    const module = await Module.findByPk(id);
    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }

    // Check if module has children
    const childrenCount = await Module.count({ where: { parentId: id } });
    if (childrenCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete module with child modules'
      });
    }

    await module.destroy();

    res.json({
      success: true,
      message: 'Module deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getModules,
  getModuleById,
  createModule,
  updateModule,
  deleteModule
};
