const jwt = require('jsonwebtoken');
const { User } = require('../models');
const config = require('../config/config');

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required'
      });
    }

    const decoded = jwt.verify(token, config.jwt.secret);
    
    // Get user
    const user = await User.findByPk(decoded.userId, {
      attributes: { exclude: ['password'] }
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or inactive user'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

const authorize = (permissions = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // If no specific permissions required, just check if authenticated
    if (permissions.length === 0) {
      return next();
    }

    // Check if user has required permissions based on role
    const userRole = req.user.role;

    // Simple role-based authorization
    // Admin has all permissions, manager has some, user has basic
    let hasPermission = false;

    if (userRole === 'admin') {
      hasPermission = true; // Admin has all permissions
    } else if (userRole === 'manager') {
      // Manager can read most things but limited create/update/delete
      hasPermission = permissions.some(permission =>
        permission.includes('.read') ||
        permission.includes('.update')
      );
    } else {
      // Regular user can only read
      hasPermission = permissions.some(permission =>
        permission.includes('.read')
      );
    }

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

module.exports = {
  authenticateToken,
  authorize
};
