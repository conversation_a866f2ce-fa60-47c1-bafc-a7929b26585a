{"ast": null, "code": "import React, { useMemo } from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { QueriesObserver } from '../core/queriesObserver';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useQueries(queries) {\n  var mountedRef = React.useRef(false);\n  var _React$useState = React.useState(0),\n    forceUpdate = _React$useState[1];\n  var queryClient = useQueryClient();\n  var defaultedQueries = useMemo(function () {\n    return queries.map(function (options) {\n      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options\n\n      defaultedOptions.optimisticResults = true;\n      return defaultedOptions;\n    });\n  }, [queries, queryClient]);\n  var _React$useState2 = React.useState(function () {\n      return new QueriesObserver(queryClient, defaultedQueries);\n    }),\n    observer = _React$useState2[0];\n  var result = observer.getOptimisticResult(defaultedQueries);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, {\n      listeners: false\n    });\n  }, [defaultedQueries, observer]);\n  return result;\n}", "map": {"version": 3, "names": ["React", "useMemo", "notify<PERSON><PERSON>ger", "QueriesObserver", "useQueryClient", "useQueries", "queries", "mountedRef", "useRef", "_React$useState", "useState", "forceUpdate", "queryClient", "defaultedQueries", "map", "options", "defaultedOptions", "defaultQueryObserverOptions", "optimisticResults", "_React$useState2", "observer", "result", "getOptimisticResult", "useEffect", "current", "unsubscribe", "subscribe", "batchCalls", "x", "setQueries", "listeners"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-query/es/react/useQueries.js"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { QueriesObserver } from '../core/queriesObserver';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useQueries(queries) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = useQueryClient();\n  var defaultedQueries = useMemo(function () {\n    return queries.map(function (options) {\n      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options\n\n      defaultedOptions.optimisticResults = true;\n      return defaultedOptions;\n    });\n  }, [queries, queryClient]);\n\n  var _React$useState2 = React.useState(function () {\n    return new QueriesObserver(queryClient, defaultedQueries);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedQueries);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, {\n      listeners: false\n    });\n  }, [defaultedQueries, observer]);\n  return result;\n}"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,SAASC,UAAUA,CAACC,OAAO,EAAE;EAClC,IAAIC,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAAC,KAAK,CAAC;EAEpC,IAAIC,eAAe,GAAGT,KAAK,CAACU,QAAQ,CAAC,CAAC,CAAC;IACnCC,WAAW,GAAGF,eAAe,CAAC,CAAC,CAAC;EAEpC,IAAIG,WAAW,GAAGR,cAAc,CAAC,CAAC;EAClC,IAAIS,gBAAgB,GAAGZ,OAAO,CAAC,YAAY;IACzC,OAAOK,OAAO,CAACQ,GAAG,CAAC,UAAUC,OAAO,EAAE;MACpC,IAAIC,gBAAgB,GAAGJ,WAAW,CAACK,2BAA2B,CAACF,OAAO,CAAC,CAAC,CAAC;;MAEzEC,gBAAgB,CAACE,iBAAiB,GAAG,IAAI;MACzC,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,OAAO,EAAEM,WAAW,CAAC,CAAC;EAE1B,IAAIO,gBAAgB,GAAGnB,KAAK,CAACU,QAAQ,CAAC,YAAY;MAChD,OAAO,IAAIP,eAAe,CAACS,WAAW,EAAEC,gBAAgB,CAAC;IAC3D,CAAC,CAAC;IACEO,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAElC,IAAIE,MAAM,GAAGD,QAAQ,CAACE,mBAAmB,CAACT,gBAAgB,CAAC;EAC3Db,KAAK,CAACuB,SAAS,CAAC,YAAY;IAC1BhB,UAAU,CAACiB,OAAO,GAAG,IAAI;IACzB,IAAIC,WAAW,GAAGL,QAAQ,CAACM,SAAS,CAACxB,aAAa,CAACyB,UAAU,CAAC,YAAY;MACxE,IAAIpB,UAAU,CAACiB,OAAO,EAAE;QACtBb,WAAW,CAAC,UAAUiB,CAAC,EAAE;UACvB,OAAOA,CAAC,GAAG,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CAAC;IACH,OAAO,YAAY;MACjBrB,UAAU,CAACiB,OAAO,GAAG,KAAK;MAC1BC,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EACdpB,KAAK,CAACuB,SAAS,CAAC,YAAY;IAC1B;IACA;IACAH,QAAQ,CAACS,UAAU,CAAChB,gBAAgB,EAAE;MACpCiB,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjB,gBAAgB,EAAEO,QAAQ,CAAC,CAAC;EAChC,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}