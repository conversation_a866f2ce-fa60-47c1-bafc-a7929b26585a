{"version": 3, "file": "styled-components.min.js", "sources": ["../src/constants.ts", "../../../node_modules/tslib/tslib.es6.js", "../src/utils/empties.ts", "../src/utils/setToString.ts", "../src/utils/error.ts", "../src/sheet/GroupedTag.ts", "../src/sheet/GroupIDAllocator.ts", "../src/sheet/Rehydration.ts", "../src/utils/nonce.ts", "../src/sheet/dom.ts", "../src/sheet/Tag.ts", "../src/sheet/Sheet.ts", "../../../node_modules/stylis/src/Enum.js", "../../../node_modules/stylis/src/Utility.js", "../../../node_modules/stylis/src/Tokenizer.js", "../../../node_modules/stylis/src/Parser.js", "../../../node_modules/stylis/src/Prefixer.js", "../../../node_modules/stylis/src/Serializer.js", "../../../node_modules/stylis/src/Middleware.js", "../src/utils/hash.ts", "../src/utils/stylis.ts", "../src/models/StyleSheetManager.tsx", "../../../node_modules/shallowequal/index.js", "../src/models/Keyframes.ts", "../../../node_modules/@emotion/unitless/dist/emotion-unitless.esm.js", "../src/utils/getComponentName.ts", "../src/utils/hyphenateStyleName.ts", "../src/utils/isFunction.ts", "../src/utils/isPlainObject.ts", "../src/utils/isStyledComponent.ts", "../src/utils/flatten.ts", "../src/utils/addUnitIfNeeded.ts", "../src/utils/isStatelessFunction.ts", "../src/utils/isStaticRules.ts", "../src/utils/joinStrings.ts", "../src/models/GlobalStyle.ts", "../src/models/ThemeProvider.tsx", "../src/utils/determineTheme.ts", "../src/utils/generateAlphabeticName.ts", "../src/utils/generateComponentId.ts", "../src/utils/interleave.ts", "../src/constructors/css.ts", "../src/utils/hoist.ts", "../src/models/ServerStyleSheet.tsx", "../src/secretInternals.ts", "../src/constructors/createGlobalStyle.ts", "../src/constructors/keyframes.ts", "../src/hoc/withTheme.tsx", "../src/utils/domElements.ts", "../src/utils/escape.ts", "../src/utils/isTag.ts", "../src/utils/mixinDeep.ts", "../src/models/ComponentStyle.ts", "../src/models/StyledComponent.ts", "../src/utils/generateDisplayName.ts", "../src/constructors/constructWithOptions.ts", "../src/constructors/styled.tsx", "../src/index-standalone.ts"], "sourcesContent": ["declare let SC_DISABLE_SPEEDY: boolean | null | undefined;\ndeclare let __VERSION__: string;\n\nexport const SC_ATTR: string =\n  (typeof process !== 'undefined' &&\n    typeof process.env !== 'undefined' &&\n    (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR)) ||\n  'data-styled';\n\nexport const SC_ATTR_ACTIVE = 'active';\nexport const SC_ATTR_VERSION = 'data-styled-version';\nexport const SC_VERSION = __VERSION__;\nexport const SPLITTER = '/*!sc*/\\n';\n\nexport const IS_BROWSER = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const DISABLE_SPEEDY = Boolean(\n  typeof SC_DISABLE_SPEEDY === 'boolean'\n    ? SC_DISABLE_SPEEDY\n    : typeof process !== 'undefined' &&\n        typeof process.env !== 'undefined' &&\n        typeof process.env.REACT_APP_SC_DISABLE_SPEEDY !== 'undefined' &&\n        process.env.REACT_APP_SC_DISABLE_SPEEDY !== ''\n      ? process.env.REACT_APP_SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.REACT_APP_SC_DISABLE_SPEEDY\n      : typeof process !== 'undefined' &&\n          typeof process.env !== 'undefined' &&\n          typeof process.env.SC_DISABLE_SPEEDY !== 'undefined' &&\n          process.env.SC_DISABLE_SPEEDY !== ''\n        ? process.env.SC_DISABLE_SPEEDY === 'false'\n          ? false\n          : process.env.SC_DISABLE_SPEEDY\n        : process.env.NODE_ENV !== 'production'\n);\n\n// Shared empty execution context when generating static styles\nexport const STATIC_EXECUTION_CONTEXT = {};\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "import { Dict } from '../types';\n\nexport const EMPTY_ARRAY = Object.freeze([]) as Readonly<any[]>;\nexport const EMPTY_OBJECT = Object.freeze({}) as Readonly<Dict<any>>;\n", "/**\n * If the Object prototype is frozen, the \"toString\" property is non-writable. This means that any objects which inherit this property\n * cannot have the property changed using a \"=\" assignment operator. If using strict mode, attempting that will cause an error. If not using\n * strict mode, attempting that will be silently ignored.\n *\n * If the Object prototype is frozen, inherited non-writable properties can still be shadowed using one of two mechanisms:\n *\n *  1. ES6 class methods: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes#methods\n *  2. Using the `Object.defineProperty()` static method:\n *     https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty\n *\n * However, this project uses Babel to transpile ES6 classes, and transforms ES6 class methods to use the assignment operator instead:\n * https://babeljs.io/docs/babel-plugin-transform-class-properties#options\n *\n * Therefore, the most compatible way to shadow the prototype's \"toString\" property is to define a new \"toString\" property on this object.\n */\nexport function setToString(object: object, toStringFn: () => string) {\n  Object.defineProperty(object, 'toString', { value: toStringFn });\n}\n", "import { Dict } from '../types';\nimport errorMap from './errors';\n\nconst ERRORS: Dict<any> = process.env.NODE_ENV !== 'production' ? errorMap : {};\n\n/**\n * super basic version of sprintf\n */\nfunction format(...args: [string, ...any]) {\n  let a = args[0];\n  const b = [];\n\n  for (let c = 1, len = args.length; c < len; c += 1) {\n    b.push(args[c]);\n  }\n\n  b.forEach(d => {\n    a = a.replace(/%[a-z]/, d);\n  });\n\n  return a;\n}\n\n/**\n * Create an error file out of errors.md for development and a simple web link to the full errors\n * in production mode.\n */\nexport default function throwStyledComponentsError(\n  code: string | number,\n  ...interpolations: any[]\n) {\n  if (process.env.NODE_ENV === 'production') {\n    return new Error(\n      `An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#${code} for more information.${\n        interpolations.length > 0 ? ` Args: ${interpolations.join(', ')}` : ''\n      }`\n    );\n  } else {\n    return new Error(format(ERRORS[code], ...interpolations).trim());\n  }\n}\n", "import { SPLITTER } from '../constants';\nimport styledError from '../utils/error';\nimport { GroupedTag, Tag } from './types';\n\n/** Create a GroupedTag with an underlying Tag implementation */\nexport const makeGroupedTag = (tag: Tag) => {\n  return new DefaultGroupedTag(tag);\n};\n\nconst BASE_SIZE = 1 << 9;\n\nconst DefaultGroupedTag = class DefaultGroupedTag implements GroupedTag {\n  groupSizes: Uint32Array;\n  length: number;\n  tag: Tag;\n\n  constructor(tag: Tag) {\n    this.groupSizes = new Uint32Array(BASE_SIZE);\n    this.length = BASE_SIZE;\n    this.tag = tag;\n  }\n\n  indexOfGroup(group: number) {\n    let index = 0;\n    for (let i = 0; i < group; i++) {\n      index += this.groupSizes[i];\n    }\n\n    return index;\n  }\n\n  insertRules(group: number, rules: string[]) {\n    if (group >= this.groupSizes.length) {\n      const oldBuffer = this.groupSizes;\n      const oldSize = oldBuffer.length;\n\n      let newSize = oldSize;\n      while (group >= newSize) {\n        newSize <<= 1;\n        if (newSize < 0) {\n          throw styledError(16, `${group}`);\n        }\n      }\n\n      this.groupSizes = new Uint32Array(newSize);\n      this.groupSizes.set(oldBuffer);\n      this.length = newSize;\n\n      for (let i = oldSize; i < newSize; i++) {\n        this.groupSizes[i] = 0;\n      }\n    }\n\n    let ruleIndex = this.indexOfGroup(group + 1);\n\n    for (let i = 0, l = rules.length; i < l; i++) {\n      if (this.tag.insertRule(ruleIndex, rules[i])) {\n        this.groupSizes[group]++;\n        ruleIndex++;\n      }\n    }\n  }\n\n  clearGroup(group: number) {\n    if (group < this.length) {\n      const length = this.groupSizes[group];\n      const startIndex = this.indexOfGroup(group);\n      const endIndex = startIndex + length;\n\n      this.groupSizes[group] = 0;\n\n      for (let i = startIndex; i < endIndex; i++) {\n        this.tag.deleteRule(startIndex);\n      }\n    }\n  }\n\n  getGroup(group: number) {\n    let css = '';\n    if (group >= this.length || this.groupSizes[group] === 0) {\n      return css;\n    }\n\n    const length = this.groupSizes[group];\n    const startIndex = this.indexOfGroup(group);\n    const endIndex = startIndex + length;\n\n    for (let i = startIndex; i < endIndex; i++) {\n      css += `${this.tag.getRule(i)}${SPLITTER}`;\n    }\n\n    return css;\n  }\n};\n", "import styledError from '../utils/error';\n\nconst MAX_SMI = 1 << (31 - 1);\n\nlet groupIDRegister: Map<string, number> = new Map();\nlet reverseRegister: Map<number, string> = new Map();\nlet nextFreeGroup = 1;\n\nexport const resetGroupIds = () => {\n  groupIDRegister = new Map();\n  reverseRegister = new Map();\n  nextFreeGroup = 1;\n};\n\nexport const getGroupForId = (id: string): number => {\n  if (groupIDRegister.has(id)) {\n    return groupIDRegister.get(id) as any;\n  }\n\n  while (reverseRegister.has(nextFreeGroup)) {\n    nextFreeGroup++;\n  }\n\n  const group = nextFreeGroup++;\n\n  if (process.env.NODE_ENV !== 'production' && ((group | 0) < 0 || group > MAX_SMI)) {\n    throw styledError(16, `${group}`);\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n  return group;\n};\n\nexport const getIdForGroup = (group: number): void | string => {\n  return reverseRegister.get(group);\n};\n\nexport const setGroupForId = (id: string, group: number) => {\n  // move pointer\n  nextFreeGroup = group + 1;\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n};\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION, SPLITTER } from '../constants';\nimport { getIdForGroup, setGroupForId } from './GroupIDAllocator';\nimport { Sheet } from './types';\n\nconst SELECTOR = `style[${SC_ATTR}][${SC_ATTR_VERSION}=\"${SC_VERSION}\"]`;\nconst MARKER_RE = new RegExp(`^${SC_ATTR}\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)`);\n\nexport const outputSheet = (sheet: Sheet) => {\n  const tag = sheet.getTag();\n  const { length } = tag;\n\n  let css = '';\n  for (let group = 0; group < length; group++) {\n    const id = getIdForGroup(group);\n    if (id === undefined) continue;\n\n    const names = sheet.names.get(id);\n    const rules = tag.getGroup(group);\n    if (names === undefined || !names.size || rules.length === 0) continue;\n\n    const selector = `${SC_ATTR}.g${group}[id=\"${id}\"]`;\n\n    let content = '';\n    if (names !== undefined) {\n      names.forEach(name => {\n        if (name.length > 0) {\n          content += `${name},`;\n        }\n      });\n    }\n\n    // NOTE: It's easier to collect rules and have the marker\n    // after the actual rules to simplify the rehydration\n    css += `${rules}${selector}{content:\"${content}\"}${SPLITTER}`;\n  }\n\n  return css;\n};\n\nconst rehydrateNamesFromContent = (sheet: Sheet, id: string, content: string) => {\n  const names = content.split(',');\n  let name;\n\n  for (let i = 0, l = names.length; i < l; i++) {\n    if ((name = names[i])) {\n      sheet.registerName(id, name);\n    }\n  }\n};\n\nconst rehydrateSheetFromTag = (sheet: Sheet, style: HTMLStyleElement) => {\n  const parts = (style.textContent ?? '').split(SPLITTER);\n  const rules: string[] = [];\n\n  for (let i = 0, l = parts.length; i < l; i++) {\n    const part = parts[i].trim();\n    if (!part) continue;\n\n    const marker = part.match(MARKER_RE);\n\n    if (marker) {\n      const group = parseInt(marker[1], 10) | 0;\n      const id = marker[2];\n\n      if (group !== 0) {\n        // Rehydrate componentId to group index mapping\n        setGroupForId(id, group);\n        // Rehydrate names and rules\n        // looks like: data-styled.g11[id=\"idA\"]{content:\"nameA,\"}\n        rehydrateNamesFromContent(sheet, id, marker[3]);\n        sheet.getTag().insertRules(group, rules);\n      }\n\n      rules.length = 0;\n    } else {\n      rules.push(part);\n    }\n  }\n};\n\nexport const rehydrateSheet = (sheet: Sheet) => {\n  const nodes = document.querySelectorAll(SELECTOR);\n\n  for (let i = 0, l = nodes.length; i < l; i++) {\n    const node = nodes[i] as any as HTMLStyleElement;\n    if (node && node.getAttribute(SC_ATTR) !== SC_ATTR_ACTIVE) {\n      rehydrateSheetFromTag(sheet, node);\n\n      if (node.parentNode) {\n        node.parentNode.removeChild(node);\n      }\n    }\n  }\n};\n", "declare let __webpack_nonce__: string;\n\nexport default function getNonce() {\n  return typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null;\n}\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport { InsertionTarget } from '../types';\nimport styledError from '../utils/error';\nimport getNonce from '../utils/nonce';\n\n/** Find last style element if any inside target */\nconst findLastStyleTag = (target: InsertionTarget): void | HTMLStyleElement => {\n  const arr = Array.from(target.querySelectorAll<HTMLStyleElement>(`style[${SC_ATTR}]`));\n\n  return arr[arr.length - 1];\n};\n\n/** Create a style element inside `target` or <head> after the last */\nexport const makeStyleTag = (target?: InsertionTarget | undefined): HTMLStyleElement => {\n  const head = document.head;\n  const parent = target || head;\n  const style = document.createElement('style');\n  const prevStyle = findLastStyleTag(parent);\n  const nextSibling = prevStyle !== undefined ? prevStyle.nextSibling : null;\n\n  style.setAttribute(SC_ATTR, SC_ATTR_ACTIVE);\n  style.setAttribute(SC_ATTR_VERSION, SC_VERSION);\n\n  const nonce = getNonce();\n\n  if (nonce) style.setAttribute('nonce', nonce);\n\n  parent.insertBefore(style, nextSibling);\n\n  return style;\n};\n\n/** Get the CSSStyleSheet instance for a given style element */\nexport const getSheet = (tag: HTMLStyleElement): CSSStyleSheet => {\n  if (tag.sheet) {\n    return tag.sheet as any as CSSStyleSheet;\n  }\n\n  // Avoid Firefox quirk where the style element might not have a sheet property\n  const { styleSheets } = document;\n  for (let i = 0, l = styleSheets.length; i < l; i++) {\n    const sheet = styleSheets[i];\n    if (sheet.ownerNode === tag) {\n      return sheet as any as CSSStyleSheet;\n    }\n  }\n\n  throw styledError(17);\n};\n", "import { InsertionTarget } from '../types';\nimport { getSheet, makeStyleTag } from './dom';\nimport { SheetOptions, Tag } from './types';\n\n/** Create a CSSStyleSheet-like tag depending on the environment */\nexport const makeTag = ({ isServer, useCSSOMInjection, target }: SheetOptions) => {\n  if (isServer) {\n    return new VirtualTag(target);\n  } else if (useCSSOMInjection) {\n    return new CSSOMTag(target);\n  } else {\n    return new TextTag(target);\n  }\n};\n\nexport const CSSOMTag = class CSSOMTag implements Tag {\n  element: HTMLStyleElement;\n\n  sheet: CSSStyleSheet;\n\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n\n    // Avoid Edge bug where empty style elements don't create sheets\n    this.element.appendChild(document.createTextNode(''));\n\n    this.sheet = getSheet(this.element);\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    try {\n      this.sheet.insertRule(rule, index);\n      this.length++;\n      return true;\n    } catch (_error) {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.sheet.deleteRule(index);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    const rule = this.sheet.cssRules[index];\n\n    // Avoid IE11 quirk where cssText is inaccessible on some invalid rules\n    if (rule && rule.cssText) {\n      return rule.cssText;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A Tag that emulates the CSSStyleSheet API but uses text nodes */\nexport const TextTag = class TextTag implements Tag {\n  element: HTMLStyleElement;\n  nodes: NodeListOf<Node>;\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n    this.nodes = this.element.childNodes;\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length && index >= 0) {\n      const node = document.createTextNode(rule);\n      const refNode = this.nodes[index];\n      this.element.insertBefore(node, refNode || null);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.element.removeChild(this.nodes[index]);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.nodes[index].textContent as string;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A completely virtual (server-side) Tag that doesn't manipulate the DOM */\nexport const VirtualTag = class VirtualTag implements Tag {\n  rules: string[];\n\n  length: number;\n\n  constructor(_target?: InsertionTarget | undefined) {\n    this.rules = [];\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length) {\n      this.rules.splice(index, 0, rule);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.rules.splice(index, 1);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.rules[index];\n    } else {\n      return '';\n    }\n  }\n};\n", "import { DISABLE_SPEEDY, IS_BROWSER } from '../constants';\nimport { InsertionTarget } from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport { setToString } from '../utils/setToString';\nimport { makeGroupedTag } from './GroupedTag';\nimport { getGroupForId } from './GroupIDAllocator';\nimport { outputSheet, rehydrateSheet } from './Rehydration';\nimport { makeTag } from './Tag';\nimport { GroupedTag, Sheet, SheetOptions } from './types';\n\nlet SHOULD_REHYDRATE = IS_BROWSER;\n\ntype SheetConstructorArgs = {\n  isServer?: boolean;\n  useCSSOMInjection?: boolean;\n  target?: InsertionTarget | undefined;\n};\n\ntype GlobalStylesAllocationMap = {\n  [key: string]: number;\n};\ntype NamesAllocationMap = Map<string, Set<string>>;\n\nconst defaultOptions: SheetOptions = {\n  isServer: !IS_BROWSER,\n  useCSSOMInjection: !DISABLE_SPEEDY,\n};\n\n/** Contains the main stylesheet logic for stringification and caching */\nexport default class StyleSheet implements Sheet {\n  gs: GlobalStylesAllocationMap;\n  names: NamesAllocationMap;\n  options: SheetOptions;\n  server: boolean;\n  tag?: GroupedTag | undefined;\n\n  /** Register a group ID to give it an index */\n  static registerId(id: string): number {\n    return getGroupForId(id);\n  }\n\n  constructor(\n    options: SheetConstructorArgs = EMPTY_OBJECT as Object,\n    globalStyles: GlobalStylesAllocationMap = {},\n    names?: NamesAllocationMap | undefined\n  ) {\n    this.options = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    this.gs = globalStyles;\n    this.names = new Map(names as NamesAllocationMap);\n    this.server = !!options.isServer;\n\n    // We rehydrate only once and use the sheet that is created first\n    if (!this.server && IS_BROWSER && SHOULD_REHYDRATE) {\n      SHOULD_REHYDRATE = false;\n      rehydrateSheet(this);\n    }\n\n    setToString(this, () => outputSheet(this));\n  }\n\n  rehydrate(): void {\n    if (!this.server && IS_BROWSER) {\n      rehydrateSheet(this);\n    }\n  }\n\n  reconstructWithOptions(options: SheetConstructorArgs, withNames = true) {\n    return new StyleSheet(\n      { ...this.options, ...options },\n      this.gs,\n      (withNames && this.names) || undefined\n    );\n  }\n\n  allocateGSInstance(id: string) {\n    return (this.gs[id] = (this.gs[id] || 0) + 1);\n  }\n\n  /** Lazily initialises a GroupedTag for when it's actually needed */\n  getTag() {\n    return this.tag || (this.tag = makeGroupedTag(makeTag(this.options)));\n  }\n\n  /** Check whether a name is known for caching */\n  hasNameForId(id: string, name: string): boolean {\n    return this.names.has(id) && (this.names.get(id) as any).has(name);\n  }\n\n  /** Mark a group's name as known for caching */\n  registerName(id: string, name: string) {\n    getGroupForId(id);\n\n    if (!this.names.has(id)) {\n      const groupNames = new Set<string>();\n      groupNames.add(name);\n      this.names.set(id, groupNames);\n    } else {\n      (this.names.get(id) as any).add(name);\n    }\n  }\n\n  /** Insert new rules which also marks the name as known */\n  insertRules(id: string, name: string, rules: string | string[]) {\n    this.registerName(id, name);\n    this.getTag().insertRules(getGroupForId(id), rules);\n  }\n\n  /** Clears all cached names for a given group ID */\n  clearNames(id: string) {\n    if (this.names.has(id)) {\n      (this.names.get(id) as any).clear();\n    }\n  }\n\n  /** Clears all rules for a given group ID */\n  clearRules(id: string) {\n    this.getTag().clearGroup(getGroupForId(id));\n    this.clearNames(id);\n  }\n\n  /** Clears the entire tag which deletes all rules but not its names */\n  clearTag() {\n    // NOTE: This does not clear the names, since it's only used during SSR\n    // so that we can continuously output only new rules\n    this.tag = undefined;\n  }\n}\n", "export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "import {MS, MO<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from './Enum.js'\nimport {hash, charat, strlen, indexof, replace, substr, match} from './Utility.js'\n\n/**\n * @param {string} value\n * @param {number} length\n * @param {object[]} children\n * @return {string}\n */\nexport function prefix (value, length, children) {\n\tswitch (hash(value, length)) {\n\t\t// color-adjust\n\t\tcase 5103:\n\t\t\treturn WEBKIT + 'print-' + value + value\n\t\t// animation, animation-(delay|direction|duration|fill-mode|iteration-count|name|play-state|timing-function)\n\t\tcase 5737: case 4201: case 3177: case 3433: case 1641: case 4457: case 2921:\n\t\t// text-decoration, filter, clip-path, backface-visibility, column, box-decoration-break\n\t\tcase 5572: case 6356: case 5844: case 3191: case 6645: case 3005:\n\t\t// mask, mask-image, mask-(mode|clip|size), mask-(repeat|origin), mask-position, mask-composite,\n\t\tcase 6391: case 5879: case 5623: case 6135: case 4599: case 4855:\n\t\t// background-clip, columns, column-(count|fill|gap|rule|rule-color|rule-style|rule-width|span|width)\n\t\tcase 4215: case 6389: case 5109: case 5365: case 5621: case 3829:\n\t\t\treturn WEBKIT + value + value\n\t\t// tab-size\n\t\tcase 4789:\n\t\t\treturn MOZ + value + value\n\t\t// appearance, user-select, transform, hyphens, text-size-adjust\n\t\tcase 5349: case 4246: case 4810: case 6968: case 2756:\n\t\t\treturn WEBKIT + value + MOZ + value + MS + value + value\n\t\t// writing-mode\n\t\tcase 5936:\n\t\t\tswitch (charat(value, length + 11)) {\n\t\t\t\t// vertical-l(r)\n\t\t\t\tcase 114:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb') + value\n\t\t\t\t// vertical-r(l)\n\t\t\t\tcase 108:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb-rl') + value\n\t\t\t\t// horizontal(-)tb\n\t\t\t\tcase 45:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'lr') + value\n\t\t\t\t// default: fallthrough to below\n\t\t\t}\n\t\t// flex, flex-direction, scroll-snap-type, writing-mode\n\t\tcase 6828: case 4268: case 2903:\n\t\t\treturn WEBKIT + value + MS + value + value\n\t\t// order\n\t\tcase 6165:\n\t\t\treturn WEBKIT + value + MS + 'flex-' + value + value\n\t\t// align-items\n\t\tcase 5187:\n\t\t\treturn WEBKIT + value + replace(value, /(\\w+).+(:[^]+)/, WEBKIT + 'box-$1$2' + MS + 'flex-$1$2') + value\n\t\t// align-self\n\t\tcase 5443:\n\t\t\treturn WEBKIT + value + MS + 'flex-item-' + replace(value, /flex-|-self/g, '') + (!match(value, /flex-|baseline/) ? MS + 'grid-row-' + replace(value, /flex-|-self/g, '') : '') + value\n\t\t// align-content\n\t\tcase 4675:\n\t\t\treturn WEBKIT + value + MS + 'flex-line-pack' + replace(value, /align-content|flex-|-self/g, '') + value\n\t\t// flex-shrink\n\t\tcase 5548:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'shrink', 'negative') + value\n\t\t// flex-basis\n\t\tcase 5292:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'basis', 'preferred-size') + value\n\t\t// flex-grow\n\t\tcase 6060:\n\t\t\treturn WEBKIT + 'box-' + replace(value, '-grow', '') + WEBKIT + value + MS + replace(value, 'grow', 'positive') + value\n\t\t// transition\n\t\tcase 4554:\n\t\t\treturn WEBKIT + replace(value, /([^-])(transform)/g, '$1' + WEBKIT + '$2') + value\n\t\t// cursor\n\t\tcase 6187:\n\t\t\treturn replace(replace(replace(value, /(zoom-|grab)/, WEBKIT + '$1'), /(image-set)/, WEBKIT + '$1'), value, '') + value\n\t\t// background, background-image\n\t\tcase 5495: case 3959:\n\t\t\treturn replace(value, /(image-set\\([^]*)/, WEBKIT + '$1' + '$`$1')\n\t\t// justify-content\n\t\tcase 4968:\n\t\t\treturn replace(replace(value, /(.+:)(flex-)?(.*)/, WEBKIT + 'box-pack:$3' + MS + 'flex-pack:$3'), /s.+-b[^;]+/, 'justify') + WEBKIT + value + value\n\t\t// justify-self\n\t\tcase 4200:\n\t\t\tif (!match(value, /flex-|baseline/)) return MS + 'grid-column-align' + substr(value, length) + value\n\t\t\tbreak\n\t\t// grid-template-(columns|rows)\n\t\tcase 2592: case 3360:\n\t\t\treturn MS + replace(value, 'template-', '') + value\n\t\t// grid-(row|column)-start\n\t\tcase 4384: case 3616:\n\t\t\tif (children && children.some(function (element, index) { return length = index, match(element.props, /grid-\\w+-end/) })) {\n\t\t\t\treturn ~indexof(value + (children = children[length].value), 'span', 0) ? value : (MS + replace(value, '-start', '') + value + MS + 'grid-row-span:' + (~indexof(children, 'span', 0) ? match(children, /\\d+/) : +match(children, /\\d+/) - +match(value, /\\d+/)) + ';')\n\t\t\t}\n\t\t\treturn MS + replace(value, '-start', '') + value\n\t\t// grid-(row|column)-end\n\t\tcase 4896: case 4128:\n\t\t\treturn (children && children.some(function (element) { return match(element.props, /grid-\\w+-start/) })) ? value : MS + replace(replace(value, '-end', '-span'), 'span ', '') + value\n\t\t// (margin|padding)-inline-(start|end)\n\t\tcase 4095: case 3583: case 4068: case 2532:\n\t\t\treturn replace(value, /(.+)-inline(.+)/, WEBKIT + '$1$2') + value\n\t\t// (min|max)?(width|height|inline-size|block-size)\n\t\tcase 8116: case 7059: case 5753: case 5535:\n\t\tcase 5445: case 5701: case 4933: case 4677:\n\t\tcase 5533: case 5789: case 5021: case 4765:\n\t\t\t// stretch, max-content, min-content, fill-available\n\t\t\tif (strlen(value) - 1 - length > 6)\n\t\t\t\tswitch (charat(value, length + 1)) {\n\t\t\t\t\t// (m)ax-content, (m)in-content\n\t\t\t\t\tcase 109:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (charat(value, length + 4) !== 45)\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t// (f)ill-available, (f)it-content\n\t\t\t\t\tcase 102:\n\t\t\t\t\t\treturn replace(value, /(.+:)(.+)-([^]+)/, '$1' + WEBKIT + '$2-$3' + '$1' + MOZ + (charat(value, length + 3) == 108 ? '$3' : '$2-$3')) + value\n\t\t\t\t\t// (s)tretch\n\t\t\t\t\tcase 115:\n\t\t\t\t\t\treturn ~indexof(value, 'stretch', 0) ? prefix(replace(value, 'stretch', 'fill-available'), length, children) + value : value\n\t\t\t\t}\n\t\t\tbreak\n\t\t// grid-(column|row)\n\t\tcase 5152: case 5920:\n\t\t\treturn replace(value, /(.+?):(\\d+)(\\s*\\/\\s*(span)?\\s*(\\d+))?(.*)/, function (_, a, b, c, d, e, f) { return (MS + a + ':' + b + f) + (c ? (MS + a + '-span:' + (d ? e : +e - +b)) + f : '') + value })\n\t\t// position: sticky\n\t\tcase 4949:\n\t\t\t// stick(y)?\n\t\t\tif (charat(value, length + 6) === 121)\n\t\t\t\treturn replace(value, ':', ':' + WEBKIT) + value\n\t\t\tbreak\n\t\t// display: (flex|inline-flex|grid|inline-grid)\n\t\tcase 6444:\n\t\t\tswitch (charat(value, charat(value, 14) === 45 ? 18 : 11)) {\n\t\t\t\t// (inline-)?fle(x)\n\t\t\t\tcase 120:\n\t\t\t\t\treturn replace(value, /(.+:)([^;\\s!]+)(;|(\\s+)?!.+)?/, '$1' + WEBKIT + (charat(value, 14) === 45 ? 'inline-' : '') + 'box$3' + '$1' + WEBKIT + '$2$3' + '$1' + MS + '$2box$3') + value\n\t\t\t\t// (inline-)?gri(d)\n\t\t\t\tcase 100:\n\t\t\t\t\treturn replace(value, ':', ':' + MS) + value\n\t\t\t}\n\t\t\tbreak\n\t\t// scroll-margin, scroll-margin-(top|right|bottom|left)\n\t\tcase 5719: case 2647: case 2135: case 3927: case 2391:\n\t\t\treturn replace(value, 'scroll-', 'scroll-snap-') + value\n\t}\n\n\treturn value\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n", "import {MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION} from './Enum.js'\nimport {match, charat, substr, strlen, sizeof, replace, combine, filter, assign} from './Utility.js'\nimport {copy, lift, tokenize} from './Tokenizer.js'\nimport {serialize} from './Serializer.js'\nimport {prefix} from './Prefixer.js'\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware (collection) {\n\tvar length = sizeof(collection)\n\n\treturn function (element, index, children, callback) {\n\t\tvar output = ''\n\n\t\tfor (var i = 0; i < length; i++)\n\t\t\toutput += collection[i](element, index, children, callback) || ''\n\n\t\treturn output\n\t}\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet (callback) {\n\treturn function (element) {\n\t\tif (!element.root)\n\t\t\tif (element = element.return)\n\t\t\t\tcallback(element)\n\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer (element, index, children, callback) {\n\tif (element.length > -1)\n\t\tif (!element.return)\n\t\t\tswitch (element.type) {\n\t\t\t\tcase DECLARATION: element.return = prefix(element.value, element.length, children)\n\t\t\t\t\treturn\n\t\t\t\tcase KEYFRAMES:\n\t\t\t\t\treturn serialize([copy(element, {value: replace(element.value, '@', '@' + WEBKIT)})], callback)\n\t\t\t\tcase RULESET:\n\t\t\t\t\tif (element.length)\n\t\t\t\t\t\treturn combine(children = element.props, function (value) {\n\t\t\t\t\t\t\tswitch (match(value, callback = /(::plac\\w+|:read-\\w+)/)) {\n\t\t\t\t\t\t\t\t// :read-(only|write)\n\t\t\t\t\t\t\t\tcase ':read-only': case ':read-write':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t// :placeholder\n\t\t\t\t\t\t\t\tcase '::placeholder':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treturn ''\n\t\t\t\t\t\t})\n\t\t\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace (element) {\n\tswitch (element.type) {\n\t\tcase RULESET:\n\t\t\telement.props = element.props.map(function (value) {\n\t\t\t\treturn combine(tokenize(value), function (value, index, children) {\n\t\t\t\t\tswitch (charat(value, 0)) {\n\t\t\t\t\t\t// \\f\n\t\t\t\t\t\tcase 12:\n\t\t\t\t\t\t\treturn substr(value, 1, strlen(value))\n\t\t\t\t\t\t// \\0 ( + > ~\n\t\t\t\t\t\tcase 0: case 40: case 43: case 62: case 126:\n\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t// :\n\t\t\t\t\t\tcase 58:\n\t\t\t\t\t\t\tif (children[++index] === 'global')\n\t\t\t\t\t\t\t\tchildren[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1)\n\t\t\t\t\t\t// \\s\n\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\treturn index === 1 ? '' : value\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tswitch (index) {\n\t\t\t\t\t\t\t\tcase 0: element = value\n\t\t\t\t\t\t\t\t\treturn sizeof(children) > 1 ? '' : value\n\t\t\t\t\t\t\t\tcase index = sizeof(children) - 1: case 2:\n\t\t\t\t\t\t\t\t\treturn index === 2 ? value + element + element : value + element\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t}\n}\n", "export const SEED = 5381;\n\n// When we have separate strings it's useful to run a progressive\n// version of djb2 where we pretend that we're still looping over\n// the same string\nexport const phash = (h: number, x: string) => {\n  let i = x.length;\n\n  while (i) {\n    h = (h * 33) ^ x.charCodeAt(--i);\n  }\n\n  return h;\n};\n\n// This is a djb2 hashing function\nexport const hash = (x: string) => {\n  return phash(SEED, x);\n};\n", "import * as stylis from 'stylis';\nimport { Stringifier } from '../types';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from './empties';\nimport throwStyledError from './error';\nimport { SEED, phash } from './hash';\n\nconst AMP_REGEX = /&/g;\nconst COMMENT_REGEX = /^\\s*\\/\\/.*$/gm;\n\nexport type ICreateStylisInstance = {\n  options?: { namespace?: string | undefined; prefix?: boolean | undefined } | undefined;\n  plugins?: stylis.Middleware[] | undefined;\n};\n\n/**\n * Takes an element and recurses through it's rules added the namespace to the start of each selector.\n * Takes into account media queries by recursing through child rules if they are present.\n */\nfunction recursivelySetNamepace(compiled: stylis.Element[], namespace: String): stylis.Element[] {\n  return compiled.map(rule => {\n    if (rule.type === 'rule') {\n      // add the namespace to the start\n      rule.value = `${namespace} ${rule.value}`;\n      // add the namespace after each comma for subsequent selectors.\n      rule.value = rule.value.replaceAll(',', `,${namespace} `);\n      rule.props = (rule.props as string[]).map(prop => {\n        return `${namespace} ${prop}`;\n      });\n    }\n\n    if (Array.isArray(rule.children) && rule.type !== '@keyframes') {\n      rule.children = recursivelySetNamepace(rule.children, namespace);\n    }\n    return rule;\n  });\n}\n\nexport default function createStylisInstance(\n  {\n    options = EMPTY_OBJECT as object,\n    plugins = EMPTY_ARRAY as unknown as stylis.Middleware[],\n  }: ICreateStylisInstance = EMPTY_OBJECT as object\n) {\n  let _componentId: string;\n  let _selector: string;\n  let _selectorRegexp: RegExp;\n\n  const selfReferenceReplacer = (match: string, offset: number, string: string) => {\n    if (\n      /**\n       * We only want to refer to the static class directly if the selector is part of a\n       * self-reference selector `& + & { color: red; }`\n       */\n      string.startsWith(_selector) &&\n      string.endsWith(_selector) &&\n      string.replaceAll(_selector, '').length > 0\n    ) {\n      return `.${_componentId}`;\n    }\n\n    return match;\n  };\n\n  /**\n   * When writing a style like\n   *\n   * & + & {\n   *   color: red;\n   * }\n   *\n   * The second ampersand should be a reference to the static component class. stylis\n   * has no knowledge of static class so we have to intelligently replace the base selector.\n   *\n   * https://github.com/thysultan/stylis.js/tree/v4.0.2#abstract-syntax-structure\n   */\n  const selfReferenceReplacementPlugin: stylis.Middleware = element => {\n    if (element.type === stylis.RULESET && element.value.includes('&')) {\n      (element.props as string[])[0] = element.props[0]\n        // catch any hanging references that stylis missed\n        .replace(AMP_REGEX, _selector)\n        .replace(_selectorRegexp, selfReferenceReplacer);\n    }\n  };\n\n  const middlewares = plugins.slice();\n\n  middlewares.push(selfReferenceReplacementPlugin);\n\n  /**\n   * Enables automatic vendor-prefixing for styles.\n   */\n  if (options.prefix) {\n    middlewares.push(stylis.prefixer);\n  }\n\n  middlewares.push(stylis.stringify);\n\n  const stringifyRules: Stringifier = (\n    css: string,\n    selector = '',\n    /**\n     * This \"prefix\" referes to a _selector_ prefix.\n     */\n    prefix = '',\n    componentId = '&'\n  ) => {\n    // stylis has no concept of state to be passed to plugins\n    // but since JS is single-threaded, we can rely on that to ensure\n    // these properties stay in sync with the current stylis run\n    _componentId = componentId;\n    _selector = selector;\n    _selectorRegexp = new RegExp(`\\\\${_selector}\\\\b`, 'g');\n\n    const flatCSS = css.replace(COMMENT_REGEX, '');\n    let compiled = stylis.compile(\n      prefix || selector ? `${prefix} ${selector} { ${flatCSS} }` : flatCSS\n    );\n\n    if (options.namespace) {\n      compiled = recursivelySetNamepace(compiled, options.namespace);\n    }\n\n    const stack: string[] = [];\n\n    stylis.serialize(\n      compiled,\n      stylis.middleware(middlewares.concat(stylis.rulesheet(value => stack.push(value))))\n    );\n\n    return stack;\n  };\n\n  stringifyRules.hash = plugins.length\n    ? plugins\n        .reduce((acc, plugin) => {\n          if (!plugin.name) {\n            throwStyledError(15);\n          }\n\n          return phash(acc, plugin.name);\n        }, SEED)\n        .toString()\n    : '';\n\n  return stringifyRules;\n}\n", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport shallowequal from 'shallowequal';\nimport type stylis from 'stylis';\nimport StyleSheet from '../sheet';\nimport { InsertionTarget, ShouldForwardProp, Stringifier } from '../types';\nimport createStylisInstance from '../utils/stylis';\n\nexport const mainSheet: StyleSheet = new StyleSheet();\nexport const mainStylis: Stringifier = createStylisInstance();\n\nexport type IStyleSheetContext = {\n  shouldForwardProp?: ShouldForwardProp<'web'> | undefined;\n  styleSheet: StyleSheet;\n  stylis: Stringifier;\n};\n\nexport const StyleSheetContext = React.createContext<IStyleSheetContext>({\n  shouldForwardProp: undefined,\n  styleSheet: mainSheet,\n  stylis: mainStylis,\n});\n\nexport const StyleSheetConsumer = StyleSheetContext.Consumer;\n\nexport type IStylisContext = Stringifier | void;\nexport const StylisContext = React.createContext<IStylisContext>(undefined);\nexport const StylisConsumer = StylisContext.Consumer;\n\nexport function useStyleSheetContext() {\n  return useContext(StyleSheetContext);\n}\n\nexport type IStyleSheetManager = React.PropsWithChildren<{\n  /**\n   * If desired, you can pass this prop to disable \"speedy\" insertion mode, which\n   * uses the browser [CSSOM APIs](https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet).\n   * When disabled, rules are inserted as simple text into style blocks.\n   */\n  disableCSSOMInjection?: undefined | boolean;\n  /**\n   * If you are working exclusively with modern browsers, vendor prefixes can often be omitted\n   * to reduce the weight of CSS on the page.\n   */\n  enableVendorPrefixes?: undefined | boolean;\n  /**\n   * Provide an optional selector to be prepended to all generated style rules.\n   */\n  namespace?: undefined | string;\n  /**\n   * Create and provide your own `StyleSheet` if necessary for advanced SSR scenarios.\n   */\n  sheet?: undefined | StyleSheet;\n  /**\n   * Starting in v6, styled-components no longer does its own prop validation\n   * and recommends use of transient props \"$prop\" to pass style-only props to\n   * components. If for some reason you are not able to use transient props, a\n   * prop validation function can be provided via `StyleSheetManager`, such as\n   * `@emotion/is-prop-valid`.\n   *\n   * When the return value is `true`, props will be forwarded to the DOM/underlying\n   * component. If return value is `false`, the prop will be discarded after styles\n   * are calculated.\n   *\n   * Manually composing `styled.{element}.withConfig({shouldForwardProp})` will\n   * override this default.\n   */\n  shouldForwardProp?: undefined | IStyleSheetContext['shouldForwardProp'];\n  /**\n   * An array of plugins to be run by stylis (style processor) during compilation.\n   * Check out [what's available on npm*](https://www.npmjs.com/search?q=keywords%3Astylis).\n   *\n   * \\* The plugin(s) must be compatible with stylis v4 or above.\n   */\n  stylisPlugins?: undefined | stylis.Middleware[];\n  /**\n   * Provide an alternate DOM node to host generated styles; useful for iframes.\n   */\n  target?: undefined | InsertionTarget;\n}>;\n\nexport function StyleSheetManager(props: IStyleSheetManager): React.JSX.Element {\n  const [plugins, setPlugins] = useState(props.stylisPlugins);\n  const { styleSheet } = useStyleSheetContext();\n\n  const resolvedStyleSheet = useMemo(() => {\n    let sheet = styleSheet;\n\n    if (props.sheet) {\n      sheet = props.sheet;\n    } else if (props.target) {\n      sheet = sheet.reconstructWithOptions({ target: props.target }, false);\n    }\n\n    if (props.disableCSSOMInjection) {\n      sheet = sheet.reconstructWithOptions({ useCSSOMInjection: false });\n    }\n\n    return sheet;\n  }, [props.disableCSSOMInjection, props.sheet, props.target, styleSheet]);\n\n  const stylis = useMemo(\n    () =>\n      createStylisInstance({\n        options: { namespace: props.namespace, prefix: props.enableVendorPrefixes },\n        plugins,\n      }),\n    [props.enableVendorPrefixes, props.namespace, plugins]\n  );\n\n  useEffect(() => {\n    if (!shallowequal(plugins, props.stylisPlugins)) setPlugins(props.stylisPlugins);\n  }, [props.stylisPlugins]);\n\n  const styleSheetContextValue = useMemo(\n    () => ({\n      shouldForwardProp: props.shouldForwardProp,\n      styleSheet: resolvedStyleSheet,\n      stylis,\n    }),\n    [props.shouldForwardProp, resolvedStyleSheet, stylis]\n  );\n\n  return (\n    <StyleSheetContext.Provider value={styleSheetContextValue}>\n      <StylisContext.Provider value={stylis}>{props.children}</StylisContext.Provider>\n    </StyleSheetContext.Provider>\n  );\n}\n", "//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n", "import StyleSheet from '../sheet';\nimport { Keyframes as KeyframesType, Stringifier } from '../types';\nimport styledError from '../utils/error';\nimport { setToString } from '../utils/setToString';\nimport { mainStylis } from './StyleSheetManager';\n\nexport default class Keyframes implements KeyframesType {\n  id: string;\n  name: string;\n  rules: string;\n\n  constructor(name: string, rules: string) {\n    this.name = name;\n    this.id = `sc-keyframes-${name}`;\n    this.rules = rules;\n\n    setToString(this, () => {\n      throw styledError(12, String(this.name));\n    });\n  }\n\n  inject = (styleSheet: StyleSheet, stylisInstance: Stringifier = mainStylis): void => {\n    const resolvedName = this.name + stylisInstance.hash;\n\n    if (!styleSheet.hasNameForId(this.id, resolvedName)) {\n      styleSheet.insertRules(\n        this.id,\n        resolvedName,\n        stylisInstance(this.rules, resolvedName, '@keyframes')\n      );\n    }\n  };\n\n  getName(stylisInstance: Stringifier = mainStylis): string {\n    return this.name + stylisInstance.hash;\n  }\n}\n", "var unitlessKeys = {\n  animationIterationCount: 1,\n  aspectRatio: 1,\n  borderImageOutset: 1,\n  borderImageSlice: 1,\n  borderImageWidth: 1,\n  boxFlex: 1,\n  boxFlexGroup: 1,\n  boxOrdinalGroup: 1,\n  columnCount: 1,\n  columns: 1,\n  flex: 1,\n  flexGrow: 1,\n  flexPositive: 1,\n  flexShrink: 1,\n  flexNegative: 1,\n  flexOrder: 1,\n  gridRow: 1,\n  gridRowEnd: 1,\n  gridRowSpan: 1,\n  gridRowStart: 1,\n  gridColumn: 1,\n  gridColumnEnd: 1,\n  gridColumnSpan: 1,\n  gridColumnStart: 1,\n  msGridRow: 1,\n  msGridRowSpan: 1,\n  msGridColumn: 1,\n  msGridColumnSpan: 1,\n  fontWeight: 1,\n  lineHeight: 1,\n  opacity: 1,\n  order: 1,\n  orphans: 1,\n  tabSize: 1,\n  widows: 1,\n  zIndex: 1,\n  zoom: 1,\n  WebkitLineClamp: 1,\n  // SVG-related properties\n  fillOpacity: 1,\n  floodOpacity: 1,\n  stopOpacity: 1,\n  strokeDasharray: 1,\n  strokeDashoffset: 1,\n  strokeMiterlimit: 1,\n  strokeOpacity: 1,\n  strokeWidth: 1\n};\n\nexport { unitlessKeys as default };\n", "import { StyledTarget } from '../types';\n\nexport default function getComponentName(target: StyledTarget<any>) {\n  return (\n    (process.env.NODE_ENV !== 'production' ? typeof target === 'string' && target : false) ||\n    (target as Exclude<StyledTarget<any>, string>).displayName ||\n    (target as Function).name ||\n    'Component'\n  );\n}\n", "const isUpper = (c: string) => c >= 'A' && c <= 'Z';\n\n/**\n * Hyphenates a camelcased CSS property name, for example:\n *\n *   > hyphenateStyleName('backgroundColor')\n *   < \"background-color\"\n *   > hyphenateStyleName('MozTransition')\n *   < \"-moz-transition\"\n *   > hyphenateStyleName('msTransition')\n *   < \"-ms-transition\"\n *\n * As Modernizr suggests (http://modernizr.com/docs/#prefixed), an `ms` prefix\n * is converted to `-ms-`.\n */\nexport default function hyphenateStyleName(string: string): string {\n  let output = '';\n\n  for (let i = 0; i < string.length; i++) {\n    const c = string[i];\n    // Check for CSS variable prefix\n    if (i === 1 && c === '-' && string[0] === '-') {\n      return string;\n    }\n\n    if (isUpper(c)) {\n      output += '-' + c.toLowerCase();\n    } else {\n      output += c;\n    }\n  }\n\n  return output.startsWith('ms-') ? '-' + output : output;\n}\n", "export default function isFunction(test: any): test is Function {\n  return typeof test === 'function';\n}\n", "export default function isPlainObject(x: any): x is Record<any, any> {\n  return (\n    x !== null &&\n    typeof x === 'object' &&\n    x.constructor.name === Object.name &&\n    /* check for reasonable markers that the object isn't an element for react & preact/compat */\n    !('props' in x && x.$$typeof)\n  );\n}\n", "import { StyledComponentBrand } from '../types';\n\nexport default function isStyledComponent(target: any): target is StyledComponentBrand {\n  return typeof target === 'object' && 'styledComponentId' in target;\n}\n", "import Keyframes from '../models/Keyframes';\nimport StyleSheet from '../sheet';\nimport {\n  AnyComponent,\n  Dict,\n  ExecutionContext,\n  Interpolation,\n  IStyledComponent,\n  RuleSet,\n  Stringifier,\n  StyledObject,\n} from '../types';\nimport addUnitIfNeeded from './addUnitIfNeeded';\nimport { EMPTY_ARRAY } from './empties';\nimport getComponentName from './getComponentName';\nimport hyphenate from './hyphenateStyleName';\nimport isFunction from './isFunction';\nimport isPlainObject from './isPlainObject';\nimport isStatelessFunction from './isStatelessFunction';\nimport isStyledComponent from './isStyledComponent';\n\n/**\n * It's falsish not falsy because 0 is allowed.\n */\nconst isFalsish = (chunk: any): chunk is undefined | null | false | '' =>\n  chunk === undefined || chunk === null || chunk === false || chunk === '';\n\nexport const objToCssArray = (obj: Dict<any>): string[] => {\n  const rules = [];\n\n  for (const key in obj) {\n    const val = obj[key];\n    if (!obj.hasOwnProperty(key) || isFalsish(val)) continue;\n\n    // @ts-expect-error Property 'isCss' does not exist on type 'any[]'\n    if ((Array.isArray(val) && val.isCss) || isFunction(val)) {\n      rules.push(`${hyphenate(key)}:`, val, ';');\n    } else if (isPlainObject(val)) {\n      rules.push(`${key} {`, ...objToCssArray(val), '}');\n    } else {\n      rules.push(`${hyphenate(key)}: ${addUnitIfNeeded(key, val)};`);\n    }\n  }\n\n  return rules;\n};\n\nexport default function flatten<Props extends object>(\n  chunk: Interpolation<object>,\n  executionContext?: (ExecutionContext & Props) | undefined,\n  styleSheet?: StyleSheet | undefined,\n  stylisInstance?: Stringifier | undefined\n): RuleSet<Props> {\n  if (isFalsish(chunk)) {\n    return [];\n  }\n\n  /* Handle other components */\n  if (isStyledComponent(chunk)) {\n    return [`.${(chunk as unknown as IStyledComponent<'web', any>).styledComponentId}`];\n  }\n\n  /* Either execute or defer the function */\n  if (isFunction(chunk)) {\n    if (isStatelessFunction(chunk) && executionContext) {\n      const result = chunk(executionContext);\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        typeof result === 'object' &&\n        !Array.isArray(result) &&\n        !(result instanceof Keyframes) &&\n        !isPlainObject(result) &&\n        result !== null\n      ) {\n        console.error(\n          `${getComponentName(\n            chunk as AnyComponent\n          )} is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.`\n        );\n      }\n\n      return flatten<Props>(result, executionContext, styleSheet, stylisInstance);\n    } else {\n      return [chunk as unknown as IStyledComponent<'web'>];\n    }\n  }\n\n  if (chunk instanceof Keyframes) {\n    if (styleSheet) {\n      chunk.inject(styleSheet, stylisInstance);\n      return [chunk.getName(stylisInstance)];\n    } else {\n      return [chunk];\n    }\n  }\n\n  /* Handle objects */\n  if (isPlainObject(chunk)) {\n    return objToCssArray(chunk as StyledObject<Props>);\n  }\n\n  if (!Array.isArray(chunk)) {\n    return [chunk.toString()];\n  }\n\n  return flatMap(chunk, chunklet =>\n    flatten<Props>(chunklet, executionContext, styleSheet, stylisInstance)\n  );\n}\n\nfunction flatMap<T, U>(array: T[], transform: (value: T, index: number, array: T[]) => U[]): U[] {\n  return Array.prototype.concat.apply(EMPTY_ARRAY, array.map(transform));\n}\n", "import unitless from '@emotion/unitless';\n\n// Taken from https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/shared/dangerousStyleValue.js\nexport default function addUnitIfNeeded(name: string, value: any) {\n  // https://github.com/amilajack/eslint-plugin-flowtype-errors/issues/133\n  if (value == null || typeof value === 'boolean' || value === '') {\n    return '';\n  }\n\n  if (typeof value === 'number' && value !== 0 && !(name in unitless) && !name.startsWith('--')) {\n    return `${value}px`; // Presumes implicit 'px' suffix for unitless numbers except for CSS variables\n  }\n\n  return String(value).trim();\n}\n", "import isFunction from './isFunction';\n\nexport default function isStatelessFunction(test: any): test is Function {\n  return isFunction(test) && !(test.prototype && test.prototype.isReactComponent);\n}\n", "import { RuleSet } from '../types';\nimport isFunction from './isFunction';\nimport isStyledComponent from './isStyledComponent';\n\nexport default function isStaticRules<Props extends object>(rules: RuleSet<Props>) {\n  for (let i = 0; i < rules.length; i += 1) {\n    const rule = rules[i];\n\n    if (isFunction(rule) && !isStyledComponent(rule)) {\n      // functions are allowed to be static if they're just being\n      // used to get the classname of a nested styled component\n      return false;\n    }\n  }\n\n  return true;\n}\n", "/**\n * Convenience function for joining strings to form className chains\n */\nexport function joinStrings(a?: string | undefined, b?: string | undefined): string {\n  return a && b ? `${a} ${b}` : a || b || '';\n}\n\nexport function joinStringArray(arr: string[], sep?: string | undefined): string {\n  if (arr.length === 0) {\n    return '';\n  }\n\n  let result = arr[0];\n  for (let i = 1; i < arr.length; i++) {\n    result += sep ? sep + arr[i] : arr[i];\n  }\n  return result;\n}\n", "import StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray } from '../utils/joinStrings';\n\nexport default class GlobalStyle<Props extends object> {\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<Props>;\n\n  constructor(rules: RuleSet<Props>, componentId: string) {\n    this.rules = rules;\n    this.componentId = componentId;\n    this.isStatic = isStaticRules(rules);\n\n    // pre-register the first instance to ensure global styles\n    // load before component ones\n    StyleSheet.registerId(this.componentId + 1);\n  }\n\n  createStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    const flatCSS = joinStringArray(\n      flatten(this.rules as RuleSet<object>, executionContext, styleSheet, stylis) as string[]\n    );\n    const css = stylis(flatCSS, '');\n    const id = this.componentId + instance;\n\n    // NOTE: We use the id as a name as well, since these rules never change\n    styleSheet.insertRules(id, id, css);\n  }\n\n  removeStyles(instance: number, styleSheet: StyleSheet): void {\n    styleSheet.clearRules(this.componentId + instance);\n  }\n\n  renderStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    if (instance > 2) StyleSheet.registerId(this.componentId + instance);\n\n    // NOTE: Remove old styles, then inject the new ones\n    this.removeStyles(instance, styleSheet);\n    this.createStyles(instance, executionContext, styleSheet, stylis);\n  }\n}\n", "import React, { useContext, useMemo } from 'react';\nimport styledError from '../utils/error';\nimport isFunction from '../utils/isFunction';\n\n// Helper type for the `DefaultTheme` interface that enforces an object type & exclusively allows\n// for typed keys.\ntype DefaultThemeAsObject<T = object> = Record<keyof T, any>;\n\n/**\n * Override DefaultTheme to get accurate typings for your project.\n *\n * ```\n * // create styled-components.d.ts in your project source\n * // if it isn't being picked up, check tsconfig compilerOptions.types\n * import type { CSSProp } from \"styled-components\";\n * import Theme from './theme';\n *\n * type ThemeType = typeof Theme;\n *\n * declare module \"styled-components\" {\n *  export interface DefaultTheme extends ThemeType {}\n * }\n *\n * declare module \"react\" {\n *  interface DOMAttributes<T> {\n *    css?: CSSProp;\n *  }\n * }\n * ```\n */\nexport interface DefaultTheme extends DefaultThemeAsObject {}\n\ntype ThemeFn = (outerTheme?: DefaultTheme | undefined) => DefaultTheme;\ntype ThemeArgument = DefaultTheme | ThemeFn;\n\ntype Props = {\n  children?: React.ReactNode;\n  theme: ThemeArgument;\n};\n\nexport const ThemeContext = React.createContext<DefaultTheme | undefined>(undefined);\n\nexport const ThemeConsumer = ThemeContext.Consumer;\n\nfunction mergeTheme(theme: ThemeArgument, outerTheme?: DefaultTheme | undefined): DefaultTheme {\n  if (!theme) {\n    throw styledError(14);\n  }\n\n  if (isFunction(theme)) {\n    const themeFn = theme as ThemeFn;\n    const mergedTheme = themeFn(outerTheme);\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      (mergedTheme === null || Array.isArray(mergedTheme) || typeof mergedTheme !== 'object')\n    ) {\n      throw styledError(7);\n    }\n\n    return mergedTheme;\n  }\n\n  if (Array.isArray(theme) || typeof theme !== 'object') {\n    throw styledError(8);\n  }\n\n  return outerTheme ? { ...outerTheme, ...theme } : theme;\n}\n\n/**\n * Returns the current theme (as provided by the closest ancestor `ThemeProvider`.)\n *\n * If no `ThemeProvider` is found, the function will error. If you need access to the theme in an\n * uncertain composition scenario, `React.useContext(ThemeContext)` will not emit an error if there\n * is no `ThemeProvider` ancestor.\n */\nexport function useTheme(): DefaultTheme {\n  const theme = useContext(ThemeContext);\n\n  if (!theme) {\n    throw styledError(18);\n  }\n\n  return theme;\n}\n\n/**\n * Provide a theme to an entire react component tree via context\n */\nexport default function ThemeProvider(props: Props): React.JSX.Element | null {\n  const outerTheme = React.useContext(ThemeContext);\n  const themeContext = useMemo(\n    () => mergeTheme(props.theme, outerTheme),\n    [props.theme, outerTheme]\n  );\n\n  if (!props.children) {\n    return null;\n  }\n\n  return <ThemeContext.Provider value={themeContext}>{props.children}</ThemeContext.Provider>;\n}\n", "import { DefaultTheme, ExecutionProps } from '../types';\nimport { EMPTY_OBJECT } from './empties';\n\nexport default function determineTheme(\n  props: ExecutionProps,\n  providedTheme?: DefaultTheme | undefined,\n  defaultProps: { theme?: DefaultTheme | undefined } = EMPTY_OBJECT\n): DefaultTheme | undefined {\n  return (props.theme !== defaultProps.theme && props.theme) || providedTheme || defaultProps.theme;\n}\n", "const AD_REPLACER_R = /(a)(d)/gi;\n\n/* This is the \"capacity\" of our alphabet i.e. 2x26 for all letters plus their capitalised\n * counterparts */\nconst charsLength = 52;\n\n/* start at 75 for 'a' until 'z' (25) and then start at 65 for capitalised letters */\nconst getAlphabeticChar = (code: number) => String.fromCharCode(code + (code > 25 ? 39 : 97));\n\n/* input a number, usually a hash and convert it to base-52 */\nexport default function generateAlphabeticName(code: number) {\n  let name = '';\n  let x;\n\n  /* get a char and divide by alphabet-length */\n  for (x = Math.abs(code); x > charsLength; x = (x / charsLength) | 0) {\n    name = getAlphabeticChar(x % charsLength) + name;\n  }\n\n  return (getAlphabeticChar(x % charsLength) + name).replace(AD_REPLACER_R, '$1-$2');\n}\n", "import generateAlphabeticName from './generateAlphabeticName';\nimport { hash } from './hash';\n\nexport default function generateComponentId(str: string) {\n  return generateAlphabeticName(hash(str) >>> 0);\n}\n", "import { Interpolation } from '../types';\n\nexport default function interleave<Props extends object>(\n  strings: readonly string[],\n  interpolations: Interpolation<Props>[]\n): Interpolation<Props>[] {\n  const result: Interpolation<Props>[] = [strings[0]];\n\n  for (let i = 0, len = interpolations.length; i < len; i += 1) {\n    result.push(interpolations[i], strings[i + 1]);\n  }\n\n  return result;\n}\n", "import {\n  BaseObject,\n  Interpolation,\n  NoInfer,\n  RuleSet,\n  StyledObject,\n  StyleFunction,\n  Styles,\n} from '../types';\nimport { EMPTY_ARRAY } from '../utils/empties';\nimport flatten from '../utils/flatten';\nimport interleave from '../utils/interleave';\nimport isFunction from '../utils/isFunction';\nimport isPlainObject from '../utils/isPlainObject';\n\n/**\n * Used when flattening object styles to determine if we should\n * expand an array of styles.\n */\nconst addTag = <T extends RuleSet<any>>(arg: T): T & { isCss: true } =>\n  Object.assign(arg, { isCss: true } as const);\n\nfunction css(styles: Styles<object>, ...interpolations: Interpolation<object>[]): RuleSet<object>;\nfunction css<Props extends object>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>>;\nfunction css<Props extends object = BaseObject>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>> {\n  if (isFunction(styles) || isPlainObject(styles)) {\n    const styleFunctionOrObject = styles as StyleFunction<Props> | StyledObject<Props>;\n\n    return addTag(\n      flatten<Props>(\n        interleave<Props>(EMPTY_ARRAY, [\n          styleFunctionOrObject,\n          ...interpolations,\n        ]) as Interpolation<object>\n      )\n    );\n  }\n\n  const styleStringArray = styles as TemplateStringsArray;\n\n  if (\n    interpolations.length === 0 &&\n    styleStringArray.length === 1 &&\n    typeof styleStringArray[0] === 'string'\n  ) {\n    return flatten<Props>(styleStringArray);\n  }\n\n  return addTag(\n    flatten<Props>(interleave<Props>(styleStringArray, interpolations) as Interpolation<object>)\n  );\n}\n\nexport default css;\n", "import React from 'react';\nimport { AnyComponent } from '../types';\n\nconst hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\n// copied from react-is\nconst REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nconst REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\n/**\n * Adapted from hoist-non-react-statics to avoid the react-is dependency.\n */\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n};\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n};\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n};\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n};\n\nconst TYPE_STATICS = {\n  [REACT_FORWARD_REF_TYPE]: FORWARD_REF_STATICS,\n  [REACT_MEMO_TYPE]: MEMO_STATICS,\n};\n\ntype OmniComponent = AnyComponent;\n\n// adapted from react-is\nfunction isMemo(\n  object: OmniComponent | React.MemoExoticComponent<any>\n): object is React.MemoExoticComponent<any> {\n  const $$typeofType = 'type' in object && object.type.$$typeof;\n\n  return $$typeofType === REACT_MEMO_TYPE;\n}\n\nfunction getStatics(component: OmniComponent) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS;\n  }\n\n  // React v16.12 and above\n  return '$$typeof' in component\n    ? TYPE_STATICS[component['$$typeof'] as unknown as string]\n    : REACT_STATICS;\n}\n\nconst defineProperty = Object.defineProperty;\nconst getOwnPropertyNames = Object.getOwnPropertyNames;\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols;\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nconst getPrototypeOf = Object.getPrototypeOf;\nconst objectPrototype = Object.prototype;\n\ntype ExcludeList = {\n  [key: string]: true;\n};\n\nexport type NonReactStatics<S extends OmniComponent, C extends ExcludeList = {}> = {\n  [key in Exclude<\n    keyof S,\n    S extends React.MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : S extends React.ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: S[key];\n};\n\nexport default function hoistNonReactStatics<\n  T extends OmniComponent,\n  S extends OmniComponent,\n  C extends ExcludeList = {},\n>(targetComponent: T, sourceComponent: S, excludelist?: C | undefined) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, excludelist);\n      }\n    }\n\n    let keys: (String | Symbol)[] = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    const targetStatics = getStatics(targetComponent);\n    const sourceStatics = getStatics(sourceComponent);\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i] as unknown as string;\n      if (\n        !(key in KNOWN_STATICS) &&\n        !(excludelist && excludelist[key]) &&\n        !(sourceStatics && key in sourceStatics) &&\n        !(targetStatics && key in targetStatics)\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!);\n        } catch (e) {\n          /* ignore */\n        }\n      }\n    }\n  }\n\n  return targetComponent as T & NonReactStatics<S, C>;\n}\n", "import React from 'react';\nimport type * as streamInternal from 'stream';\nimport { Readable } from 'stream';\nimport { IS_BROWSER, SC_ATTR, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport styledError from '../utils/error';\nimport { joinStringArray } from '../utils/joinStrings';\nimport getNonce from '../utils/nonce';\nimport { StyleSheetManager } from './StyleSheetManager';\n\ndeclare const __SERVER__: boolean;\n\nconst CLOSING_TAG_R = /^\\s*<\\/[a-z]/i;\n\nexport default class ServerStyleSheet {\n  instance: StyleSheet;\n  sealed: boolean;\n\n  constructor() {\n    this.instance = new StyleSheet({ isServer: true });\n    this.sealed = false;\n  }\n\n  _emitSheetCSS = (): string => {\n    const css = this.instance.toString();\n    if (!css) return '';\n    const nonce = getNonce();\n    const attrs = [\n      nonce && `nonce=\"${nonce}\"`,\n      `${SC_ATTR}=\"true\"`,\n      `${SC_ATTR_VERSION}=\"${SC_VERSION}\"`,\n    ];\n    const htmlAttr = joinStringArray(attrs.filter(Boolean) as string[], ' ');\n\n    return `<style ${htmlAttr}>${css}</style>`;\n  };\n\n  collectStyles(children: any): React.JSX.Element {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return <StyleSheetManager sheet={this.instance}>{children}</StyleSheetManager>;\n  }\n\n  getStyleTags = (): string => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return this._emitSheetCSS();\n  };\n\n  getStyleElement = () => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    const css = this.instance.toString();\n    if (!css) return [];\n\n    const props = {\n      [SC_ATTR]: '',\n      [SC_ATTR_VERSION]: SC_VERSION,\n      dangerouslySetInnerHTML: {\n        __html: css,\n      },\n    };\n\n    const nonce = getNonce();\n    if (nonce) {\n      (props as any).nonce = nonce;\n    }\n\n    // v4 returned an array for this fn, so we'll do the same for v5 for backward compat\n    return [<style {...props} key=\"sc-0-0\" />];\n  };\n\n  // @ts-expect-error alternate return types are not possible due to code transformation\n  interleaveWithNodeStream(input: Readable): streamInternal.Transform {\n    if (!__SERVER__ || IS_BROWSER) {\n      throw styledError(3);\n    } else if (this.sealed) {\n      throw styledError(2);\n    }\n\n    if (__SERVER__) {\n      this.seal();\n\n      const { Transform } = require('stream');\n\n      const readableStream: Readable = input;\n      const { instance: sheet, _emitSheetCSS } = this;\n\n      const transformer: streamInternal.Transform = new Transform({\n        transform: function appendStyleChunks(\n          chunk: string,\n          /* encoding */\n          _: string,\n          callback: Function\n        ) {\n          // Get the chunk and retrieve the sheet's CSS as an HTML chunk,\n          // then reset its rules so we get only new ones for the next chunk\n          const renderedHtml = chunk.toString();\n          const html = _emitSheetCSS();\n\n          sheet.clearTag();\n\n          // prepend style html to chunk, unless the start of the chunk is a\n          // closing tag in which case append right after that\n          if (CLOSING_TAG_R.test(renderedHtml)) {\n            const endOfClosingTag = renderedHtml.indexOf('>') + 1;\n            const before = renderedHtml.slice(0, endOfClosingTag);\n            const after = renderedHtml.slice(endOfClosingTag);\n\n            this.push(before + html + after);\n          } else {\n            this.push(html + renderedHtml);\n          }\n\n          callback();\n        },\n      });\n\n      readableStream.on('error', err => {\n        // forward the error to the transform stream\n        transformer.emit('error', err);\n      });\n\n      return readableStream.pipe(transformer);\n    }\n  }\n\n  seal = (): void => {\n    this.sealed = true;\n  };\n}\n", "import { mainSheet } from './models/StyleSheetManager';\nimport StyleSheet from './sheet';\n\nexport const __PRIVATE__ = {\n  StyleSheet,\n  mainSheet,\n};\n", "import React from 'react';\nimport { STATIC_EXECUTION_CONTEXT } from '../constants';\nimport GlobalStyle from '../models/GlobalStyle';\nimport { useStyleSheetContext } from '../models/StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from '../models/ThemeProvider';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, ExecutionProps, Interpolation, Stringifier, Styles } from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport determineTheme from '../utils/determineTheme';\nimport generateComponentId from '../utils/generateComponentId';\nimport css from './css';\n\nexport default function createGlobalStyle<Props extends object>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n) {\n  const rules = css<Props>(strings, ...interpolations);\n  const styledComponentId = `sc-global-${generateComponentId(JSON.stringify(rules))}`;\n  const globalStyle = new GlobalStyle<Props>(rules, styledComponentId);\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(styledComponentId);\n  }\n\n  const GlobalStyleComponent: React.ComponentType<ExecutionProps & Props> = props => {\n    const ssc = useStyleSheetContext();\n    const theme = React.useContext(ThemeContext);\n    const instanceRef = React.useRef(ssc.styleSheet.allocateGSInstance(styledComponentId));\n\n    const instance = instanceRef.current;\n\n    if (process.env.NODE_ENV !== 'production' && React.Children.count(props.children)) {\n      console.warn(\n        `The global style component ${styledComponentId} was given child JSX. createGlobalStyle does not render children.`\n      );\n    }\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      rules.some(rule => typeof rule === 'string' && rule.indexOf('@import') !== -1)\n    ) {\n      console.warn(\n        `Please do not use @import CSS syntax in createGlobalStyle at this time, as the CSSOM APIs we use in production do not handle it well. Instead, we recommend using a library such as react-helmet to inject a typical <link> meta tag to the stylesheet, or simply embedding it manually in your index.html <head> section for a simpler app.`\n      );\n    }\n\n    if (ssc.styleSheet.server) {\n      renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n    }\n\n    if (!__SERVER__) {\n      React.useLayoutEffect(() => {\n        if (!ssc.styleSheet.server) {\n          renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n          return () => globalStyle.removeStyles(instance, ssc.styleSheet);\n        }\n      }, [instance, props, ssc.styleSheet, theme, ssc.stylis]);\n    }\n\n    return null;\n  };\n\n  function renderStyles(\n    instance: number,\n    props: ExecutionProps,\n    styleSheet: StyleSheet,\n    theme: DefaultTheme | undefined,\n    stylis: Stringifier\n  ) {\n    if (globalStyle.isStatic) {\n      globalStyle.renderStyles(\n        instance,\n        STATIC_EXECUTION_CONTEXT as unknown as ExecutionContext & Props,\n        styleSheet,\n        stylis\n      );\n    } else {\n      const context = {\n        ...props,\n        theme: determineTheme(props, theme, GlobalStyleComponent.defaultProps),\n      } as ExecutionContext & Props;\n\n      globalStyle.renderStyles(instance, context, styleSheet, stylis);\n    }\n  }\n\n  return React.memo(GlobalStyleComponent);\n}\n", "import Keyframes from '../models/Keyframes';\nimport { Interpolation, Styles } from '../types';\nimport generateComponentId from '../utils/generateComponentId';\nimport { joinStringArray } from '../utils/joinStrings';\nimport css from './css';\n\nexport default function keyframes<Props extends object = {}>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n): Keyframes {\n  /* Warning if you've used keyframes on React Native */\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    typeof navigator !== 'undefined' &&\n    navigator.product === 'ReactNative'\n  ) {\n    console.warn(\n      '`keyframes` cannot be used on ReactNative, only on the web. To do animation in ReactNative please use Animated.'\n    );\n  }\n\n  const rules = joinStringArray(css<Props>(strings, ...interpolations) as string[]);\n  const name = generateComponentId(rules);\n  return new Keyframes(name, rules);\n}\n", "import React from 'react';\nimport { ThemeContext } from '../models/ThemeProvider';\nimport { AnyComponent, ExecutionProps } from '../types';\nimport determineTheme from '../utils/determineTheme';\nimport getComponentName from '../utils/getComponentName';\nimport hoist, { NonReactStatics } from '../utils/hoist';\n\nexport default function withTheme<T extends AnyComponent>(\n  Component: T\n): React.ForwardRefExoticComponent<\n  React.PropsWithoutRef<React.JSX.LibraryManagedAttributes<T, ExecutionProps>> &\n    React.RefAttributes<T>\n> &\n  NonReactStatics<T> {\n  const WithTheme = React.forwardRef<T, React.JSX.LibraryManagedAttributes<T, ExecutionProps>>(\n    (props, ref) => {\n      const theme = React.useContext(ThemeContext);\n      const themeProp = determineTheme(props, theme, Component.defaultProps);\n\n      if (process.env.NODE_ENV !== 'production' && themeProp === undefined) {\n        console.warn(\n          `[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"${getComponentName(\n            Component\n          )}\"`\n        );\n      }\n\n      return <Component {...props} theme={themeProp} ref={ref} />;\n    }\n  );\n\n  WithTheme.displayName = `WithTheme(${getComponentName(Component)})`;\n\n  return hoist(WithTheme, Component);\n}\n", "// Thanks to ReactDOMFactories for this handy list!\n\nconst elements = [\n  'a',\n  'abbr',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'base',\n  'bdi',\n  'bdo',\n  'big',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'data',\n  'datalist',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'map',\n  'mark',\n  'menu',\n  'menuitem',\n  'meta',\n  'meter',\n  'nav',\n  'noscript',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'param',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'section',\n  'select',\n  'small',\n  'source',\n  'span',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'u',\n  'ul',\n  'use',\n  'var',\n  'video',\n  'wbr', // SVG\n  'circle',\n  'clipPath',\n  'defs',\n  'ellipse',\n  'foreignObject',\n  'g',\n  'image',\n  'line',\n  'linearGradient',\n  'marker',\n  'mask',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialGradient',\n  'rect',\n  'stop',\n  'svg',\n  'text',\n  'tspan',\n] as const;\n\nexport default new Set(elements);\nexport type SupportedHTMLElements = (typeof elements)[number];\n", "// Source: https://www.w3.org/TR/cssom-1/#serialize-an-identifier\n// Control characters and non-letter first symbols are not supported\nconst escapeRegex = /[!\"#$%&'()*+,./:;<=>?@[\\\\\\]^`{|}~-]+/g;\n\nconst dashesAtEnds = /(^-|-$)/g;\n\n/**\n * TODO: Explore using CSS.escape when it becomes more available\n * in evergreen browsers.\n */\nexport default function escape(str: string) {\n  return str // Replace all possible CSS selectors\n    .replace(escapeRegex, '-') // Remove extraneous hyphens at the start and end\n    .replace(dashesAtEnds, '');\n}\n", "import { StyledTarget } from '../types';\n\nexport default function isTag(target: StyledTarget<'web'>): target is string {\n  return (\n    typeof target === 'string' &&\n    (process.env.NODE_ENV !== 'production'\n      ? target.charAt(0) === target.charAt(0).toLowerCase()\n      : true)\n  );\n}\n", "import isPlainObject from './isPlainObject';\n\nfunction mixinRecursively(target: any, source: any, forceMerge = false) {\n  /* only merge into POJOs, Arrays, but for top level objects only\n   * allow to merge into anything by passing forceMerge = true */\n  if (!forceMerge && !isPlainObject(target) && !Array.isArray(target)) {\n    return source;\n  }\n\n  if (Array.isArray(source)) {\n    for (let key = 0; key < source.length; key++) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  } else if (isPlainObject(source)) {\n    for (const key in source) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Arrays & POJOs merged recursively, other objects and value types are overridden\n * If target is not a POJO or an Array, it will get source properties injected via shallow merge\n * Source objects applied left to right.  Mutates & returns target.  Similar to lodash merge.\n */\nexport default function mixinDeep(target: any, ...sources: any[]) {\n  for (const source of sources) {\n    mixinRecursively(target, source, true);\n  }\n\n  return target;\n}\n", "import { SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport generateName from '../utils/generateAlphabeticName';\nimport { hash, phash } from '../utils/hash';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray, joinStrings } from '../utils/joinStrings';\n\nconst SEED = hash(SC_VERSION);\n\n/**\n * ComponentStyle is all the CSS-specific stuff, not the React-specific stuff.\n */\nexport default class ComponentStyle {\n  baseHash: number;\n  baseStyle: ComponentStyle | null | undefined;\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<any>;\n  staticRulesId: string;\n\n  constructor(rules: RuleSet<any>, componentId: string, baseStyle?: ComponentStyle | undefined) {\n    this.rules = rules;\n    this.staticRulesId = '';\n    this.isStatic =\n      process.env.NODE_ENV === 'production' &&\n      (baseStyle === undefined || baseStyle.isStatic) &&\n      isStaticRules(rules);\n    this.componentId = componentId;\n    this.baseHash = phash(SEED, componentId);\n    this.baseStyle = baseStyle;\n\n    // NOTE: This registers the componentId, which ensures a consistent order\n    // for this component's styles compared to others\n    StyleSheet.registerId(componentId);\n  }\n\n  generateAndInjectStyles(\n    executionContext: ExecutionContext,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): string {\n    let names = this.baseStyle\n      ? this.baseStyle.generateAndInjectStyles(executionContext, styleSheet, stylis)\n      : '';\n\n    // force dynamic classnames if user-supplied stylis plugins are in use\n    if (this.isStatic && !stylis.hash) {\n      if (this.staticRulesId && styleSheet.hasNameForId(this.componentId, this.staticRulesId)) {\n        names = joinStrings(names, this.staticRulesId);\n      } else {\n        const cssStatic = joinStringArray(\n          flatten(this.rules, executionContext, styleSheet, stylis) as string[]\n        );\n        const name = generateName(phash(this.baseHash, cssStatic) >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          const cssStaticFormatted = stylis(cssStatic, `.${name}`, undefined, this.componentId);\n          styleSheet.insertRules(this.componentId, name, cssStaticFormatted);\n        }\n\n        names = joinStrings(names, name);\n        this.staticRulesId = name;\n      }\n    } else {\n      let dynamicHash = phash(this.baseHash, stylis.hash);\n      let css = '';\n\n      for (let i = 0; i < this.rules.length; i++) {\n        const partRule = this.rules[i];\n\n        if (typeof partRule === 'string') {\n          css += partRule;\n\n          if (process.env.NODE_ENV !== 'production') dynamicHash = phash(dynamicHash, partRule);\n        } else if (partRule) {\n          const partString = joinStringArray(\n            flatten(partRule, executionContext, styleSheet, stylis) as string[]\n          );\n          // The same value can switch positions in the array, so we include \"i\" in the hash.\n          dynamicHash = phash(dynamicHash, partString + i);\n          css += partString;\n        }\n      }\n\n      if (css) {\n        const name = generateName(dynamicHash >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          styleSheet.insertRules(\n            this.componentId,\n            name,\n            stylis(css, `.${name}`, undefined, this.componentId)\n          );\n        }\n\n        names = joinStrings(names, name);\n      }\n    }\n\n    return names;\n  }\n}\n", "import isPropValid from '@emotion/is-prop-valid';\nimport React, { createElement, Ref, useDebugValue } from 'react';\nimport { SC_VERSION } from '../constants';\nimport type {\n  AnyComponent,\n  Attrs,\n  BaseObject,\n  Dict,\n  ExecutionContext,\n  ExecutionProps,\n  IStyledComponent,\n  IStyledComponentFactory,\n  IStyledStatics,\n  OmitNever,\n  RuleSet,\n  StyledOptions,\n  WebTarget,\n} from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport createWarnTooManyClasses from '../utils/createWarnTooManyClasses';\nimport determineTheme from '../utils/determineTheme';\nimport domElements from '../utils/domElements';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from '../utils/empties';\nimport escape from '../utils/escape';\nimport generateComponentId from '../utils/generateComponentId';\nimport generateDisplayName from '../utils/generateDisplayName';\nimport hoist from '../utils/hoist';\nimport isFunction from '../utils/isFunction';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport isTag from '../utils/isTag';\nimport { joinStrings } from '../utils/joinStrings';\nimport merge from '../utils/mixinDeep';\nimport { setToString } from '../utils/setToString';\nimport ComponentStyle from './ComponentStyle';\nimport { useStyleSheetContext } from './StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from './ThemeProvider';\n\nconst identifiers: { [key: string]: number } = {};\n\n/* We depend on components having unique IDs */\nfunction generateId(\n  displayName?: string | undefined,\n  parentComponentId?: string | undefined\n): string {\n  const name = typeof displayName !== 'string' ? 'sc' : escape(displayName);\n  // Ensure that no displayName can lead to duplicate componentIds\n  identifiers[name] = (identifiers[name] || 0) + 1;\n\n  const componentId = `${name}-${generateComponentId(\n    // SC_VERSION gives us isolation between multiple runtimes on the page at once\n    // this is improved further with use of the babel plugin \"namespace\" feature\n    SC_VERSION + name + identifiers[name]\n  )}`;\n\n  return parentComponentId ? `${parentComponentId}-${componentId}` : componentId;\n}\n\nfunction useInjectedStyle<T extends ExecutionContext>(\n  componentStyle: ComponentStyle,\n  resolvedAttrs: T\n) {\n  const ssc = useStyleSheetContext();\n\n  const className = componentStyle.generateAndInjectStyles(\n    resolvedAttrs,\n    ssc.styleSheet,\n    ssc.stylis\n  );\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(className);\n\n  return className;\n}\n\nfunction resolveContext<Props extends object>(\n  attrs: Attrs<React.HTMLAttributes<Element> & Props>[],\n  props: React.HTMLAttributes<Element> & ExecutionProps & Props,\n  theme: DefaultTheme\n) {\n  const context: React.HTMLAttributes<Element> &\n    ExecutionContext &\n    Props & { [key: string]: any; class?: string; ref?: React.Ref<any> } = {\n    ...props,\n    // unset, add `props.className` back at the end so props always \"wins\"\n    className: undefined,\n    theme,\n  };\n  let attrDef;\n\n  for (let i = 0; i < attrs.length; i += 1) {\n    attrDef = attrs[i];\n    const resolvedAttrDef = isFunction(attrDef) ? attrDef(context) : attrDef;\n\n    for (const key in resolvedAttrDef) {\n      context[key as keyof typeof context] =\n        key === 'className'\n          ? joinStrings(context[key] as string | undefined, resolvedAttrDef[key] as string)\n          : key === 'style'\n            ? { ...context[key], ...resolvedAttrDef[key] }\n            : resolvedAttrDef[key as keyof typeof resolvedAttrDef];\n    }\n  }\n\n  if (props.className) {\n    context.className = joinStrings(context.className, props.className);\n  }\n\n  return context;\n}\n\nlet seenUnknownProps = new Set();\n\nfunction useStyledComponentImpl<Props extends object>(\n  forwardedComponent: IStyledComponent<'web', Props>,\n  props: ExecutionProps & Props,\n  forwardedRef: Ref<Element>\n) {\n  const {\n    attrs: componentAttrs,\n    componentStyle,\n    defaultProps,\n    foldedComponentIds,\n    styledComponentId,\n    target,\n  } = forwardedComponent;\n\n  const contextTheme = React.useContext(ThemeContext);\n  const ssc = useStyleSheetContext();\n  const shouldForwardProp = forwardedComponent.shouldForwardProp || ssc.shouldForwardProp;\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(styledComponentId);\n\n  // NOTE: the non-hooks version only subscribes to this when !componentStyle.isStatic,\n  // but that'd be against the rules-of-hooks. We could be naughty and do it anyway as it\n  // should be an immutable value, but behave for now.\n  const theme = determineTheme(props, contextTheme, defaultProps) || EMPTY_OBJECT;\n\n  const context = resolveContext<Props>(componentAttrs, props, theme);\n  const elementToBeCreated: WebTarget = context.as || target;\n  const propsForElement: Dict<any> = {};\n\n  for (const key in context) {\n    if (context[key] === undefined) {\n      // Omit undefined values from props passed to wrapped element.\n      // This enables using .attrs() to remove props, for example.\n    } else if (key[0] === '$' || key === 'as' || (key === 'theme' && context.theme === theme)) {\n      // Omit transient props and execution props.\n    } else if (key === 'forwardedAs') {\n      propsForElement.as = context.forwardedAs;\n    } else if (!shouldForwardProp || shouldForwardProp(key, elementToBeCreated)) {\n      propsForElement[key] = context[key];\n\n      if (\n        !shouldForwardProp &&\n        process.env.NODE_ENV === 'development' &&\n        !isPropValid(key) &&\n        !seenUnknownProps.has(key) &&\n        // Only warn on DOM Element.\n        domElements.has(elementToBeCreated as any)\n      ) {\n        seenUnknownProps.add(key);\n        console.warn(\n          `styled-components: it looks like an unknown prop \"${key}\" is being sent through to the DOM, which will likely trigger a React console error. If you would like automatic filtering of unknown props, you can opt-into that behavior via \\`<StyleSheetManager shouldForwardProp={...}>\\` (connect an API like \\`@emotion/is-prop-valid\\`) or consider using transient props (\\`$\\` prefix for automatic filtering.)`\n        );\n      }\n    }\n  }\n\n  const generatedClassName = useInjectedStyle(componentStyle, context);\n\n  if (process.env.NODE_ENV !== 'production' && forwardedComponent.warnTooManyClasses) {\n    forwardedComponent.warnTooManyClasses(generatedClassName);\n  }\n\n  let classString = joinStrings(foldedComponentIds, styledComponentId);\n  if (generatedClassName) {\n    classString += ' ' + generatedClassName;\n  }\n  if (context.className) {\n    classString += ' ' + context.className;\n  }\n\n  propsForElement[\n    // handle custom elements which React doesn't properly alias\n    isTag(elementToBeCreated) &&\n    !domElements.has(elementToBeCreated as Extract<typeof domElements, string>)\n      ? 'class'\n      : 'className'\n  ] = classString;\n\n  // forwardedRef is coming from React.forwardRef.\n  // But it might not exist. Since React 19 handles `ref` like a prop, it only define it if there is a value.\n  // We don't want to inject an empty ref.\n  if (forwardedRef) {\n    propsForElement.ref = forwardedRef;\n  }\n\n  return createElement(elementToBeCreated, propsForElement);\n}\n\nfunction createStyledComponent<\n  Target extends WebTarget,\n  OuterProps extends object,\n  Statics extends object = BaseObject,\n>(\n  target: Target,\n  options: StyledOptions<'web', OuterProps>,\n  rules: RuleSet<OuterProps>\n): ReturnType<IStyledComponentFactory<'web', Target, OuterProps, Statics>> {\n  const isTargetStyledComp = isStyledComponent(target);\n  const styledComponentTarget = target as IStyledComponent<'web', OuterProps>;\n  const isCompositeComponent = !isTag(target);\n\n  const {\n    attrs = EMPTY_ARRAY,\n    componentId = generateId(options.displayName, options.parentComponentId),\n    displayName = generateDisplayName(target),\n  } = options;\n\n  const styledComponentId =\n    options.displayName && options.componentId\n      ? `${escape(options.displayName)}-${options.componentId}`\n      : options.componentId || componentId;\n\n  // fold the underlying StyledComponent attrs up (implicit extend)\n  const finalAttrs =\n    isTargetStyledComp && styledComponentTarget.attrs\n      ? styledComponentTarget.attrs.concat(attrs as unknown as Attrs<OuterProps>[]).filter(Boolean)\n      : (attrs as Attrs<OuterProps>[]);\n\n  let { shouldForwardProp } = options;\n\n  if (isTargetStyledComp && styledComponentTarget.shouldForwardProp) {\n    const shouldForwardPropFn = styledComponentTarget.shouldForwardProp;\n\n    if (options.shouldForwardProp) {\n      const passedShouldForwardPropFn = options.shouldForwardProp;\n\n      // compose nested shouldForwardProp calls\n      shouldForwardProp = (prop, elementToBeCreated) =>\n        shouldForwardPropFn(prop, elementToBeCreated) &&\n        passedShouldForwardPropFn(prop, elementToBeCreated);\n    } else {\n      shouldForwardProp = shouldForwardPropFn;\n    }\n  }\n\n  const componentStyle = new ComponentStyle(\n    rules,\n    styledComponentId,\n    isTargetStyledComp ? (styledComponentTarget.componentStyle as ComponentStyle) : undefined\n  );\n\n  function forwardRefRender(props: ExecutionProps & OuterProps, ref: Ref<Element>) {\n    return useStyledComponentImpl<OuterProps>(WrappedStyledComponent, props, ref);\n  }\n\n  forwardRefRender.displayName = displayName;\n\n  /**\n   * forwardRef creates a new interim component, which we'll take advantage of\n   * instead of extending ParentComponent to create _another_ interim class\n   */\n  let WrappedStyledComponent = React.forwardRef(forwardRefRender) as unknown as IStyledComponent<\n    'web',\n    any\n  > &\n    Statics;\n  WrappedStyledComponent.attrs = finalAttrs;\n  WrappedStyledComponent.componentStyle = componentStyle;\n  WrappedStyledComponent.displayName = displayName;\n  WrappedStyledComponent.shouldForwardProp = shouldForwardProp;\n\n  // this static is used to preserve the cascade of static classes for component selector\n  // purposes; this is especially important with usage of the css prop\n  WrappedStyledComponent.foldedComponentIds = isTargetStyledComp\n    ? joinStrings(styledComponentTarget.foldedComponentIds, styledComponentTarget.styledComponentId)\n    : '';\n\n  WrappedStyledComponent.styledComponentId = styledComponentId;\n\n  // fold the underlying StyledComponent target up since we folded the styles\n  WrappedStyledComponent.target = isTargetStyledComp ? styledComponentTarget.target : target;\n\n  Object.defineProperty(WrappedStyledComponent, 'defaultProps', {\n    get() {\n      return this._foldedDefaultProps;\n    },\n\n    set(obj) {\n      this._foldedDefaultProps = isTargetStyledComp\n        ? merge({}, styledComponentTarget.defaultProps, obj)\n        : obj;\n    },\n  });\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(displayName, styledComponentId);\n\n    WrappedStyledComponent.warnTooManyClasses = createWarnTooManyClasses(\n      displayName,\n      styledComponentId\n    );\n  }\n\n  setToString(WrappedStyledComponent, () => `.${WrappedStyledComponent.styledComponentId}`);\n\n  if (isCompositeComponent) {\n    const compositeComponentTarget = target as AnyComponent;\n\n    hoist<typeof WrappedStyledComponent, typeof compositeComponentTarget>(\n      WrappedStyledComponent,\n      compositeComponentTarget,\n      {\n        // all SC-specific things should not be hoisted\n        attrs: true,\n        componentStyle: true,\n        displayName: true,\n        foldedComponentIds: true,\n        shouldForwardProp: true,\n        styledComponentId: true,\n        target: true,\n      } as { [key in keyof OmitNever<IStyledStatics<'web', OuterProps>>]: true }\n    );\n  }\n\n  return WrappedStyledComponent;\n}\n\nexport default createStyledComponent;\n", "import { StyledTarget } from '../types';\nimport getComponentName from './getComponentName';\nimport isTag from './isTag';\n\nexport default function generateDisplayName(target: StyledTarget<any>) {\n  return isTag(target) ? `styled.${target}` : `Styled(${getComponentName(target)})`;\n}\n", "import {\n  Attrs,\n  BaseObject,\n  ExecutionProps,\n  Interpolation,\n  IStyledComponent,\n  IStyledComponentFactory,\n  KnownTarget,\n  NoInfer,\n  Runtime,\n  StyledOptions,\n  StyledTarget,\n  Styles,\n  Substitute,\n} from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport styledError from '../utils/error';\nimport css from './css';\n\ntype AttrsResult<T extends Attrs<any>> = T extends (...args: any) => infer P\n  ? P extends object\n    ? P\n    : never\n  : T extends object\n    ? T\n    : never;\n\n/**\n * Based on Attrs being a simple object or function that returns\n * a prop object, inspect the attrs result and attempt to extract\n * any \"as\" prop usage to modify the runtime target.\n */\ntype AttrsTarget<\n  R extends Runtime,\n  T extends Attrs<any>,\n  FallbackTarget extends StyledTarget<R>,\n  Result extends ExecutionProps = AttrsResult<T>,\n> = Result extends { as: infer RuntimeTarget }\n  ? RuntimeTarget extends KnownTarget\n    ? RuntimeTarget\n    : FallbackTarget\n  : FallbackTarget;\n\nexport interface Styled<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object,\n  OuterStatics extends object = BaseObject,\n> {\n  <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, NoInfer<Props>>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, NoInfer<Props>>>[]\n  ): IStyledComponent<R, Substitute<OuterProps, Props>> &\n    OuterStatics &\n    Statics &\n    (R extends 'web'\n      ? Target extends string\n        ? {}\n        : Omit<Target, keyof React.Component<any>>\n      : {});\n\n  attrs: <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) => Styled<\n    R,\n    PrivateResolvedTarget,\n    PrivateResolvedTarget extends KnownTarget\n      ? Substitute<\n          Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n          Props\n        >\n      : PrivateMergedProps,\n    OuterStatics\n  >;\n\n  withConfig: (config: StyledOptions<R, OuterProps>) => Styled<R, Target, OuterProps, OuterStatics>;\n}\n\nexport default function constructWithOptions<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object = Target extends KnownTarget\n    ? React.ComponentPropsWithRef<Target>\n    : BaseObject,\n  OuterStatics extends object = BaseObject,\n>(\n  componentConstructor: IStyledComponentFactory<R, StyledTarget<R>, object, any>,\n  tag: StyledTarget<R>,\n  options: StyledOptions<R, OuterProps> = EMPTY_OBJECT\n): Styled<R, Target, OuterProps, OuterStatics> {\n  /**\n   * We trust that the tag is a valid component as long as it isn't\n   * falsish. Typically the tag here is a string or function (i.e.\n   * class or pure function component), however a component may also be\n   * an object if it uses another utility, e.g. React.memo. React will\n   * output an appropriate warning however if the `tag` isn't valid.\n   */\n  if (!tag) {\n    throw styledError(1, tag);\n  }\n\n  /* This is callable directly as a template function */\n  const templateFunction = <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, Props>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, Props>>[]\n  ) =>\n    componentConstructor<Substitute<OuterProps, Props>, Statics>(\n      tag,\n      options as StyledOptions<R, Substitute<OuterProps, Props>>,\n      css<Substitute<OuterProps, Props>>(initialStyles, ...interpolations)\n    );\n\n  /**\n   * Attrs allows for accomplishing two goals:\n   *\n   * 1. Backfilling props at runtime more expressively than defaultProps\n   * 2. Amending the prop interface of a wrapped styled component\n   */\n  templateFunction.attrs = <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) =>\n    constructWithOptions<\n      R,\n      PrivateResolvedTarget,\n      PrivateResolvedTarget extends KnownTarget\n        ? Substitute<\n            Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n            Props\n          >\n        : PrivateMergedProps,\n      OuterStatics\n    >(componentConstructor, tag, {\n      ...options,\n      attrs: Array.prototype.concat(options.attrs, attrs).filter(Boolean),\n    });\n\n  /**\n   * If config methods are called, wrap up a new template function\n   * and merge options.\n   */\n  templateFunction.withConfig = (config: StyledOptions<R, OuterProps>) =>\n    constructWithOptions<R, Target, OuterProps, OuterStatics>(componentConstructor, tag, {\n      ...options,\n      ...config,\n    });\n\n  return templateFunction;\n}\n", "import * as React from 'react';\nimport createStyledComponent from '../models/StyledComponent';\nimport { BaseObject, KnownTarget, WebTarget } from '../types';\nimport domElements, { SupportedHTMLElements } from '../utils/domElements';\nimport constructWithOptions, { Styled as StyledInstance } from './constructWithOptions';\n\nconst baseStyled = <Target extends WebTarget, InjectedProps extends object = BaseObject>(\n  tag: Target\n) =>\n  constructWithOptions<\n    'web',\n    Target,\n    Target extends KnownTarget ? React.ComponentPropsWithRef<Target> & InjectedProps : InjectedProps\n  >(createStyledComponent, tag);\n\nconst styled = baseStyled as typeof baseStyled & {\n  [E in SupportedHTMLElements]: StyledInstance<'web', E, React.JSX.IntrinsicElements[E]>;\n};\n\n// Shorthands for all valid HTML Elements\ndomElements.forEach(domElement => {\n  // @ts-expect-error some react typing bs\n  styled[domElement] = baseStyled<typeof domElement>(domElement);\n});\n\nexport default styled;\nexport { StyledInstance };\n\n/**\n * This is the type of the `styled` HOC.\n */\nexport type Styled = typeof styled;\n\n/**\n * Use this higher-order type for scenarios where you are wrapping `styled`\n * and providing extra props as a third-party library.\n */\nexport type LibraryStyled<LibraryProps extends object = BaseObject> = <Target extends WebTarget>(\n  tag: Target\n) => typeof baseStyled<Target, LibraryProps>;\n", "import * as secondary from './base';\n/* Import singleton constructors */\nimport styled from './constructors/styled';\n\n/**\n * eliminates the need to do styled.default since the other APIs\n * are directly assigned as properties to the main function\n * */\nfor (const key in secondary) {\n  // @ts-expect-error shush\n  styled[key] = secondary[key];\n}\n\nexport default styled;\n"], "names": ["SC_ATTR", "process", "env", "REACT_APP_SC_ATTR", "SC_ATTR_ACTIVE", "SC_ATTR_VERSION", "SC_VERSION", "SPLITTER", "IS_BROWSER", "window", "document", "DISABLE_SPEEDY", "Boolean", "SC_DISABLE_SPEEDY", "REACT_APP_SC_DISABLE_SPEEDY", "STATIC_EXECUTION_CONTEXT", "__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "__spread<PERSON><PERSON>y", "to", "from", "pack", "ar", "l", "Array", "slice", "concat", "SuppressedError", "EMPTY_ARRAY", "freeze", "EMPTY_OBJECT", "setToString", "object", "toStringFn", "defineProperty", "value", "throwStyledComponentsError", "code", "interpolations", "_i", "Error", "join", "DefaultGroupedTag", "tag", "groupSizes", "Uint32Array", "indexOfGroup", "group", "index", "insertRules", "rules", "<PERSON><PERSON><PERSON><PERSON>", "oldSize", "newSize", "styledError", "set", "ruleIndex", "insertRule", "clearGroup", "length_1", "startIndex", "endIndex", "deleteRule", "getGroup", "css", "getRule", "groupIDRegister", "Map", "reverseRegister", "nextFreeGroup", "getGroupForId", "id", "has", "get", "setGroupForId", "SELECTOR", "MARKER_RE", "RegExp", "rehydrateNamesFromContent", "sheet", "content", "name", "names", "split", "registerName", "rehydrateSheetFromTag", "style", "parts", "_a", "textContent", "part", "trim", "marker", "match", "parseInt", "getTag", "push", "rehydrateSheet", "nodes", "querySelectorAll", "node", "getAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getNonce", "__webpack_nonce__", "makeStyleTag", "target", "head", "parent", "createElement", "prevStyle", "arr", "findLastStyleTag", "nextS<PERSON>ling", "undefined", "setAttribute", "nonce", "insertBefore", "CSSOMTag", "element", "append<PERSON><PERSON><PERSON>", "createTextNode", "styleSheets", "ownerNode", "getSheet", "rule", "_error", "cssRules", "cssText", "TextTag", "childNodes", "VirtualTag", "_target", "splice", "SHOULD_REHYDRATE", "defaultOptions", "isServer", "useCSSOMInjection", "StyleSheet", "options", "globalStyles", "_this", "gs", "server", "getIdForGroup", "size", "selector", "for<PERSON>ach", "outputSheet", "registerId", "rehydrate", "reconstructWithOptions", "with<PERSON><PERSON>s", "allocateGSInstance", "makeTag", "hasNameForId", "add", "groupNames", "Set", "clearNames", "clear", "clearRules", "clearTag", "MS", "MOZ", "WEBKIT", "COMMENT", "RULESET", "DECLARATION", "IMPORT", "KEYFRAMES", "LAYER", "abs", "Math", "String", "fromCharCode", "pattern", "exec", "replace", "replacement", "indexof", "search", "position", "indexOf", "charat", "charCodeAt", "substr", "begin", "end", "strlen", "sizeof", "append", "array", "filter", "line", "column", "character", "characters", "root", "type", "props", "children", "siblings", "return", "copy", "lift", "next", "peek", "caret", "token", "delimit", "delimiter", "whitespace", "escaping", "count", "commenter", "identifier", "compile", "dealloc", "parse", "alloc", "rulesets", "pseudo", "points", "declarations", "offset", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "reference", "comment", "declaration", "ruleset", "post", "j", "k", "x", "y", "z", "prefix", "hash", "some", "_", "a", "b", "c", "d", "e", "f", "serialize", "callback", "output", "stringify", "prefixer", "map", "combine", "SEED", "phash", "h", "AMP_REGEX", "COMMENT_REGEX", "recursivelySetNamepace", "compiled", "namespace", "replaceAll", "prop", "isArray", "createStylisInstance", "_componentId", "_selector", "_selectorRegexp", "_b", "_c", "_d", "plugins", "selfReferenceReplacer", "string", "startsWith", "endsWith", "middlewares", "stylis.RULESET", "includes", "stylis.prefixer", "stylis.stringify", "stringifyRules", "componentId", "flatCSS", "stylis.compile", "stack", "stylis.serialize", "collection", "stylis.middleware", "reduce", "acc", "plugin", "throwStyledError", "toString", "mainSheet", "mainStylis", "StyleSheetContext", "React", "createContext", "shouldForwardProp", "styleSheet", "stylis", "StyleSheetConsumer", "Consumer", "StylisContext", "useStyleSheetContext", "useContext", "StyleSheetManager", "useState", "stylisPlugins", "setPlugins", "resolvedStyleSheet", "useMemo", "disableCSSOMInjection", "enableVendorPrefixes", "useEffect", "objA", "objB", "compare", "compareContext", "ret", "keysA", "keys", "keysB", "bHasOwnProperty", "bind", "idx", "key", "shallowequal", "styleSheetContextValue", "Provider", "Keyframes", "inject", "stylisInstance", "resolvedName", "getName", "unitlessKeys", "animationIterationCount", "aspectRatio", "borderImageOutset", "borderImageSlice", "borderImageWidth", "boxFlex", "boxFlexGroup", "boxOrdinalGroup", "columnCount", "columns", "flex", "flexGrow", "flexPositive", "flexShrink", "flexNegative", "flexOrder", "gridRow", "gridRowEnd", "gridRowSpan", "gridRowStart", "gridColumn", "gridColumnEnd", "gridColumnSpan", "gridColumnStart", "msGridRow", "msGridRowSpan", "msGridColumn", "msGridColumnSpan", "fontWeight", "lineHeight", "opacity", "order", "orphans", "tabSize", "widows", "zIndex", "zoom", "WebkitLineClamp", "fillOpacity", "floodOpacity", "stopOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeMiterlimit", "strokeOpacity", "strokeWidth", "getComponentName", "displayName", "isUpper", "hyphenateStyleName", "toLowerCase", "isFunction", "test", "isPlainObject", "constructor", "$$typeof", "isStyledComponent", "isFalsish", "chunk", "objToCssArray", "obj", "val", "isCss", "hyphenate", "unitless", "flatten", "executionContext", "styledComponentId", "isReactComponent", "chunklet", "isStaticRules", "joinStrings", "joinStringArray", "sep", "result", "GlobalStyle", "isStatic", "createStyles", "instance", "removeStyles", "renderStyles", "ThemeContext", "ThemeConsumer", "determineTheme", "providedTheme", "defaultProps", "theme", "AD_REPLACER_R", "chars<PERSON><PERSON><PERSON>", "getAlphabeticChar", "generateAlphabeticName", "generateComponentId", "str", "interleave", "strings", "len", "addTag", "arg", "styles", "styleStringArray", "hasSymbol", "Symbol", "for", "REACT_MEMO_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "KNOWN_STATICS", "caller", "callee", "arity", "MEMO_STATICS", "TYPE_STATICS", "render", "getStatics", "component", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "excludelist", "inheritedComponent", "targetStatics", "sourceStatics", "descriptor", "ServerStyleSheet", "_emitSheetCSS", "htmlAttr", "getStyleTags", "sealed", "getStyleElement", "dangerouslySetInnerHTML", "__html", "seal", "collectStyles", "interleaveWithNodeStream", "input", "outerTheme", "themeContext", "mergeTheme", "JSON", "globalStyle", "GlobalStyleComponent", "ssc", "useRef", "current", "useLayoutEffect", "context", "memo", "Component", "WithTheme", "forwardRef", "ref", "themeProp", "hoist", "dom<PERSON><PERSON>s", "escapeRegex", "dashesAtEnds", "escape", "isTag", "mixinRecursively", "source", "forceMerge", "ComponentStyle", "baseStyle", "staticRulesId", "baseHash", "generateAndInjectStyles", "cssStatic", "name_1", "generateName", "cssStaticFormatted", "dynamicHash", "partRule", "partString", "name_2", "identifiers", "createStyledComponent", "isTargetStyledComp", "styledComponentTarget", "isCompositeComponent", "attrs", "parentComponentId", "generateId", "generateDisplayName", "finalAttrs", "shouldForwardPropFn_1", "passedShouldForwardPropFn_1", "elementToBeCreated", "componentStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedComponent", "forwardedRef", "componentAttrs", "foldedComponentIds", "contextTheme", "attrDef", "className", "resolvedAttrDef", "resolveContext", "as", "propsForElement", "forwardedAs", "generatedClassName", "resolvedAttrs", "useInjectedStyle", "classString", "useStyledComponentImpl", "WrappedStyledComponent", "_foldedDefaultProps", "sources", "sources_1", "merge", "constructWithOptions", "componentConstructor", "templateFunction", "initialStyles", "withConfig", "config", "baseStyled", "styled", "dom<PERSON>lement", "secondary"], "mappings": "8NAGO,IAAMA,EACS,oBAAZC,cACiB,IAAhBA,QAAQC,MACdD,QAAQC,IAAIC,mBAAqBF,QAAQC,IAAIF,UAChD,cAEWI,EAAiB,SACjBC,EAAkB,sBAClBC,EAAa,SACbC,EAAW,YAEXC,EAA+B,oBAAXC,QAA8C,oBAAbC,SAErDC,EAAiBC,QACC,kBAAtBC,kBACHA,kBACmB,oBAAZZ,cACkB,IAAhBA,QAAQC,UACoC,IAA5CD,QAAQC,IAAIY,6BACyB,KAA5Cb,QAAQC,IAAIY,4BACgC,UAA5Cb,QAAQC,IAAIY,6BAEVb,QAAQC,IAAIY,4BACK,oBAAZb,cACkB,IAAhBA,QAAQC,UAC0B,IAAlCD,QAAQC,IAAIW,mBACe,KAAlCZ,QAAQC,IAAIW,mBACsB,UAAlCZ,QAAQC,IAAIW,mBAEVZ,QAAQC,IAAIW,mBAKXE,EAA2B,CAAE,ECN/BC,EAAW,WAQlB,OAPAA,EAAWC,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACOJ,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,CACV,EACMH,EAASa,MAAMC,KAAMP,UAChC,EA6KO,SAASQ,EAAcC,EAAIC,EAAMC,GACpC,GAAIA,GAA6B,IAArBX,UAAUC,OAAc,IAAK,IAA4BW,EAAxBd,EAAI,EAAGe,EAAIH,EAAKT,OAAYH,EAAIe,EAAGf,KACxEc,GAAQd,KAAKY,IACRE,IAAIA,EAAKE,MAAMX,UAAUY,MAAMV,KAAKK,EAAM,EAAGZ,IAClDc,EAAGd,GAAKY,EAAKZ,IAGrB,OAAOW,EAAGO,OAAOJ,GAAME,MAAMX,UAAUY,MAAMV,KAAKK,GACtD,CA8FkD,mBAApBO,iBAAiCA,gBCzTxD,IAAMC,EAAcxB,OAAOyB,OAAO,IAC5BC,EAAe1B,OAAOyB,OAAO,ICa1B,SAAAE,EAAYC,EAAgBC,GAC1C7B,OAAO8B,eAAeF,EAAQ,WAAY,CAAEG,MAAOF,GACrD,CCSwB,SAAAG,EACtBC,OACA,IAAwBC,EAAA,GAAAC,EAAA,EAAxBA,EAAwB7B,UAAAC,OAAxB4B,IAAAD,EAAwBC,EAAA,GAAA7B,UAAA6B,GAGtB,OAAO,IAAIC,MACT,0IAAAd,OAA0IW,EAAI,0BAAAX,OAC5IY,EAAe3B,OAAS,EAAI,UAAUe,OAAAY,EAAeG,KAAK,OAAU,IAM5E,CCnCO,IAMDC,EAAiB,WAKrB,SAAAA,EAAYC,GACV1B,KAAK2B,WAAa,IAAIC,YARR,KASd5B,KAAKN,OATS,IAUdM,KAAK0B,IAAMA,CACZ,CAyEH,OAvEED,EAAY7B,UAAAiC,aAAZ,SAAaC,GAEX,IADA,IAAIC,EAAQ,EACHxC,EAAI,EAAGA,EAAIuC,EAAOvC,IACzBwC,GAAS/B,KAAK2B,WAAWpC,GAG3B,OAAOwC,GAGTN,EAAA7B,UAAAoC,YAAA,SAAYF,EAAeG,GACzB,GAAIH,GAAS9B,KAAK2B,WAAWjC,OAAQ,CAKnC,IAJA,IAAMwC,EAAYlC,KAAK2B,WACjBQ,EAAUD,EAAUxC,OAEtB0C,EAAUD,EACPL,GAASM,GAEd,IADAA,IAAY,GACE,EACZ,MAAMC,EAAY,GAAI,UAAGP,IAI7B9B,KAAK2B,WAAa,IAAIC,YAAYQ,GAClCpC,KAAK2B,WAAWW,IAAIJ,GACpBlC,KAAKN,OAAS0C,EAEd,IAAK,IAAI7C,EAAI4C,EAAS5C,EAAI6C,EAAS7C,IACjCS,KAAK2B,WAAWpC,GAAK,CAExB,CAID,IAFA,IAAIgD,EAAYvC,KAAK6B,aAAaC,EAAQ,GAE1BxB,GAAPf,EAAI,EAAO0C,EAAMvC,QAAQH,EAAIe,EAAGf,IACnCS,KAAK0B,IAAIc,WAAWD,EAAWN,EAAM1C,MACvCS,KAAK2B,WAAWG,KAChBS,MAKNd,EAAU7B,UAAA6C,WAAV,SAAWX,GACT,GAAIA,EAAQ9B,KAAKN,OAAQ,CACvB,IAAMgD,EAAS1C,KAAK2B,WAAWG,GACzBa,EAAa3C,KAAK6B,aAAaC,GAC/Bc,EAAWD,EAAaD,EAE9B1C,KAAK2B,WAAWG,GAAS,EAEzB,IAAK,IAAIvC,EAAIoD,EAAYpD,EAAIqD,EAAUrD,IACrCS,KAAK0B,IAAImB,WAAWF,EAEvB,GAGHlB,EAAQ7B,UAAAkD,SAAR,SAAShB,GACP,IAAIiB,EAAM,GACV,GAAIjB,GAAS9B,KAAKN,QAAqC,IAA3BM,KAAK2B,WAAWG,GAC1C,OAAOiB,EAOT,IAJA,IAAMrD,EAASM,KAAK2B,WAAWG,GACzBa,EAAa3C,KAAK6B,aAAaC,GAC/Bc,EAAWD,EAAajD,EAErBH,EAAIoD,EAAYpD,EAAIqD,EAAUrD,IACrCwD,GAAO,GAAAtC,OAAGT,KAAK0B,IAAIsB,QAAQzD,IAAKkB,OAAAhC,GAGlC,OAAOsE,GAEVtB,CAAD,ICzFIwB,EAAuC,IAAIC,IAC3CC,EAAuC,IAAID,IAC3CE,EAAgB,EAQPC,EAAgB,SAACC,GAC5B,GAAIL,EAAgBM,IAAID,GACtB,OAAOL,EAAgBO,IAAIF,GAG7B,KAAOH,EAAgBI,IAAIH,IACzBA,IAGF,IAAMtB,EAAQsB,IAQd,OAFAH,EAAgBX,IAAIgB,EAAIxB,GACxBqB,EAAgBb,IAAIR,EAAOwB,GACpBxB,CACT,EAMa2B,EAAgB,SAACH,EAAYxB,GAExCsB,EAAgBtB,EAAQ,EAExBmB,EAAgBX,IAAIgB,EAAIxB,GACxBqB,EAAgBb,IAAIR,EAAOwB,EAC7B,ECxCMI,EAAW,SAASjD,OAAAvC,eAAYK,EAAe,MAAAkC,OAAKjC,EAAU,MAC9DmF,EAAY,IAAIC,OAAO,IAAInD,OAAAvC,EAAqD,iDAkChF2F,EAA4B,SAACC,EAAcR,EAAYS,GAI3D,IAHA,IACIC,EADEC,EAAQF,EAAQG,MAAM,KAGnB3E,EAAI,EAAGe,EAAI2D,EAAMvE,OAAQH,EAAIe,EAAGf,KAClCyE,EAAOC,EAAM1E,KAChBuE,EAAMK,aAAab,EAAIU,EAG7B,EAEMI,EAAwB,SAACN,EAAcO,GAI3C,UAHMC,GAA8B,QAArBC,EAAAF,EAAMG,mBAAe,IAAAD,EAAAA,EAAA,IAAIL,MAAMzF,GACxCwD,EAAkB,GAEf1C,EAAI,EAAGe,EAAIgE,EAAM5E,OAAQH,EAAIe,EAAGf,IAAK,CAC5C,IAAMkF,EAAOH,EAAM/E,GAAGmF,OACtB,GAAKD,EAAL,CAEA,IAAME,EAASF,EAAKG,MAAMjB,GAE1B,GAAIgB,EAAQ,CACV,IAAM7C,EAAkC,EAA1B+C,SAASF,EAAO,GAAI,IAC5BrB,EAAKqB,EAAO,GAEJ,IAAV7C,IAEF2B,EAAcH,EAAIxB,GAGlB+B,EAA0BC,EAAOR,EAAIqB,EAAO,IAC5Cb,EAAMgB,SAAS9C,YAAYF,EAAOG,IAGpCA,EAAMvC,OAAS,CAChB,MACCuC,EAAM8C,KAAKN,EAnBO,CAqBrB,CACH,EAEaO,EAAiB,SAAClB,GAG7B,IAFA,IAAMmB,EAAQrG,SAASsG,iBAAiBxB,GAE/BnE,EAAI,EAAGe,EAAI2E,EAAMvF,OAAQH,EAAIe,EAAGf,IAAK,CAC5C,IAAM4F,EAAOF,EAAM1F,GACf4F,GAAQA,EAAKC,aAAalH,KAAaI,IACzC8F,EAAsBN,EAAOqB,GAEzBA,EAAKE,YACPF,EAAKE,WAAWC,YAAYH,GAGjC,CACH,EC3Fc,SAAUI,IACtB,MAAoC,oBAAtBC,kBAAoCA,kBAAoB,IACxE,CCEA,IAOaC,EAAe,SAACC,GAC3B,IAAMC,EAAO/G,SAAS+G,KAChBC,EAASF,GAAUC,EACnBtB,EAAQzF,SAASiH,cAAc,SAC/BC,EAXiB,SAACJ,GACxB,IAAMK,EAAMxF,MAAMJ,KAAKuF,EAAOR,iBAAmC,SAASzE,OAAAvC,EAAU,OAEpF,OAAO6H,EAAIA,EAAIrG,OAAS,EAC1B,CAOoBsG,CAAiBJ,GAC7BK,OAA4BC,IAAdJ,EAA0BA,EAAUG,YAAc,KAEtE5B,EAAM8B,aAAajI,EAASI,GAC5B+F,EAAM8B,aAAa5H,EAAiBC,GAEpC,IAAM4H,EAAQb,IAMd,OAJIa,GAAO/B,EAAM8B,aAAa,QAASC,GAEvCR,EAAOS,aAAahC,EAAO4B,GAEpB5B,CACT,ECfaiC,EAAQ,WAOnB,SAAAA,EAAYZ,GACV1F,KAAKuG,QAAUd,EAAaC,GAG5B1F,KAAKuG,QAAQC,YAAY5H,SAAS6H,eAAe,KAEjDzG,KAAK8D,MDKe,SAACpC,GACvB,GAAIA,EAAIoC,MACN,OAAOpC,EAAIoC,MAKb,IADQ,IAAA4C,EAAgB9H,SAAQ8H,YACvBnH,EAAI,EAAGe,EAAIoG,EAAYhH,OAAQH,EAAIe,EAAGf,IAAK,CAClD,IAAMuE,EAAQ4C,EAAYnH,GAC1B,GAAIuE,EAAM6C,YAAcjF,EACtB,OAAOoC,CAEV,CAED,MAAMzB,EAAY,GACpB,CCpBiBuE,CAAS5G,KAAKuG,SAC3BvG,KAAKN,OAAS,CACf,CA2BH,OAzBE4G,EAAA1G,UAAA4C,WAAA,SAAWT,EAAe8E,GACxB,IAGE,OAFA7G,KAAK8D,MAAMtB,WAAWqE,EAAM9E,GAC5B/B,KAAKN,UACE,CACR,CAAC,MAAOoH,GACP,OAAO,CACR,GAGHR,EAAU1G,UAAAiD,WAAV,SAAWd,GACT/B,KAAK8D,MAAMjB,WAAWd,GACtB/B,KAAKN,UAGP4G,EAAO1G,UAAAoD,QAAP,SAAQjB,GACN,IAAM8E,EAAO7G,KAAK8D,MAAMiD,SAAShF,GAGjC,OAAI8E,GAAQA,EAAKG,QACRH,EAAKG,QAEL,IAGZV,CAAD,IAGaW,EAAO,WAKlB,SAAAA,EAAYvB,GACV1F,KAAKuG,QAAUd,EAAaC,GAC5B1F,KAAKiF,MAAQjF,KAAKuG,QAAQW,WAC1BlH,KAAKN,OAAS,CACf,CA0BH,OAxBEuH,EAAArH,UAAA4C,WAAA,SAAWT,EAAe8E,GACxB,GAAI9E,GAAS/B,KAAKN,QAAUqC,GAAS,EAAG,CACtC,IAAMoD,EAAOvG,SAAS6H,eAAeI,GAIrC,OAFA7G,KAAKuG,QAAQF,aAAalB,EADVnF,KAAKiF,MAAMlD,IACgB,MAC3C/B,KAAKN,UACE,CACR,CACC,OAAO,GAIXuH,EAAUrH,UAAAiD,WAAV,SAAWd,GACT/B,KAAKuG,QAAQjB,YAAYtF,KAAKiF,MAAMlD,IACpC/B,KAAKN,UAGPuH,EAAOrH,UAAAoD,QAAP,SAAQjB,GACN,OAAIA,EAAQ/B,KAAKN,OACRM,KAAKiF,MAAMlD,GAAOyC,YAElB,IAGZyC,CAAD,IAGaE,EAAU,WAKrB,SAAAA,EAAYC,GACVpH,KAAKiC,MAAQ,GACbjC,KAAKN,OAAS,CACf,CAwBH,OAtBEyH,EAAAvH,UAAA4C,WAAA,SAAWT,EAAe8E,GACxB,OAAI9E,GAAS/B,KAAKN,SAChBM,KAAKiC,MAAMoF,OAAOtF,EAAO,EAAG8E,GAC5B7G,KAAKN,UACE,IAMXyH,EAAUvH,UAAAiD,WAAV,SAAWd,GACT/B,KAAKiC,MAAMoF,OAAOtF,EAAO,GACzB/B,KAAKN,UAGPyH,EAAOvH,UAAAoD,QAAP,SAAQjB,GACN,OAAIA,EAAQ/B,KAAKN,OACRM,KAAKiC,MAAMF,GAEX,IAGZoF,CAAD,ICxHIG,EAAmB5I,EAajB6I,EAA+B,CACnCC,UAAW9I,EACX+I,mBAAoB5I,GAItB6I,EAAA,WAYE,SAAAA,EACEC,EACAC,EACA3D,QAFA,IAAA0D,IAAAA,EAAgC9G,QAChC,IAAA+G,IAAAA,EAA4C,CAAA,GAF9C,IAqBCC,EAAA7H,KAhBCA,KAAK2H,QAAOzI,EAAAA,EAAA,CAAA,EACPqI,GACAI,GAGL3H,KAAK8H,GAAKF,EACV5H,KAAKiE,MAAQ,IAAIf,IAAIe,GACrBjE,KAAK+H,SAAWJ,EAAQH,UAGnBxH,KAAK+H,QAAUrJ,GAAc4I,IAChCA,GAAmB,EACnBtC,EAAehF,OAGjBc,EAAYd,KAAM,WAAM,OJtDD,SAAC8D,GAK1B,IAJA,IAAMpC,EAAMoC,EAAMgB,SACVpF,EAAWgC,EAAGhC,OAElBqD,EAAM,cACDjB,GACP,IAAMwB,EDqBmB,SAACxB,GAC5B,OAAOqB,EAAgBK,IAAI1B,EAC7B,CCvBekG,CAAclG,GACzB,QAAWoE,IAAP5C,EAA2B,MAAA,WAE/B,IAAMW,EAAQH,EAAMG,MAAMT,IAAIF,GACxBrB,EAAQP,EAAIoB,SAAShB,GAC3B,QAAcoE,IAAVjC,IAAwBA,EAAMgE,MAAyB,IAAjBhG,EAAMvC,OAAuB,MAAA,WAEvE,IAAMwI,EAAW,GAAGzH,OAAAvC,eAAY4D,EAAK,SAAArB,OAAQ6C,EAAE,MAE3CS,EAAU,QACAmC,IAAVjC,GACFA,EAAMkE,QAAQ,SAAAnE,GACRA,EAAKtE,OAAS,IAChBqE,GAAW,GAAAtD,OAAGuD,EAAI,KAEtB,GAKFjB,GAAO,GAAGtC,OAAAwB,GAAQxB,OAAAyH,uBAAqBnE,EAAO,MAAAtD,OAAKhC,IArB5CqD,EAAQ,EAAGA,EAAQpC,EAAQoC,MAA3BA,GAwBT,OAAOiB,CACT,CIwB4BqF,CAAYP,EAAK,EAC1C,CAoEH,OA7FSH,EAAUW,WAAjB,SAAkB/E,GAChB,OAAOD,EAAcC,IA0BvBoE,EAAA9H,UAAA0I,UAAA,YACOtI,KAAK+H,QAAUrJ,GAClBsG,EAAehF,OAInB0H,EAAA9H,UAAA2I,uBAAA,SAAuBZ,EAA+Ba,GACpD,YADoD,IAAAA,IAAAA,GAAgB,GAC7D,IAAId,EACJxI,EAAAA,EAAA,CAAA,EAAAc,KAAK2H,SAAYA,GACtB3H,KAAK8H,GACJU,GAAaxI,KAAKiE,YAAUiC,IAIjCwB,EAAkB9H,UAAA6I,mBAAlB,SAAmBnF,GACjB,OAAQtD,KAAK8H,GAAGxE,IAAOtD,KAAK8H,GAAGxE,IAAO,GAAK,GAI7CoE,EAAA9H,UAAAkF,OAAA,WACE,OAAO9E,KAAK0B,MAAQ1B,KAAK0B,KN/EEA,EKAR,SAAC6C,GAAE,IAAUkD,EAAiBlD,EAAAkD,kBAAE/B,EAAMnB,EAAAmB,OAC3D,kBACS,IAAIyB,EAAWzB,GACb+B,EACF,IAAInB,EAASZ,GAEb,IAAIuB,EAAQvB,EAEvB,CCuEkDgD,CAAQ1I,KAAK2H,SN9EtD,IAAIlG,EAAkBC,KADD,IAACA,GMmF7BgG,EAAA9H,UAAA+I,aAAA,SAAarF,EAAYU,GACvB,OAAOhE,KAAKiE,MAAMV,IAAID,IAAQtD,KAAKiE,MAAMT,IAAIF,GAAYC,IAAIS,IAI/D0D,EAAA9H,UAAAuE,aAAA,SAAab,EAAYU,GAGvB,GAFAX,EAAcC,GAETtD,KAAKiE,MAAMV,IAAID,GAKjBtD,KAAKiE,MAAMT,IAAIF,GAAYsF,IAAI5E,OALT,CACvB,IAAM6E,EAAa,IAAIC,IACvBD,EAAWD,IAAI5E,GACfhE,KAAKiE,MAAM3B,IAAIgB,EAAIuF,EACpB,GAMHnB,EAAA9H,UAAAoC,YAAA,SAAYsB,EAAYU,EAAc/B,GACpCjC,KAAKmE,aAAab,EAAIU,GACtBhE,KAAK8E,SAAS9C,YAAYqB,EAAcC,GAAKrB,IAI/CyF,EAAU9H,UAAAmJ,WAAV,SAAWzF,GACLtD,KAAKiE,MAAMV,IAAID,IAChBtD,KAAKiE,MAAMT,IAAIF,GAAY0F,SAKhCtB,EAAU9H,UAAAqJ,WAAV,SAAW3F,GACTtD,KAAK8E,SAASrC,WAAWY,EAAcC,IACvCtD,KAAK+I,WAAWzF,IAIlBoE,EAAA9H,UAAAsJ,SAAA,WAGElJ,KAAK0B,SAAMwE,GAEdwB,CAAD,IClIWyB,EAAK,OACLC,EAAM,QACNC,EAAS,WAETC,EAAU,OACVC,EAAU,OACVC,EAAc,OAIdC,EAAS,UAMTC,EAAY,aAIZC,EAAQ,SChBRC,EAAMC,KAAKD,IAMXzJ,EAAO2J,OAAOC,aAMd3K,EAASD,OAAOC,OAepB,SAASsF,EAAMxD,GACrB,OAAOA,EAAMwD,MAQP,CAAA,SAASE,EAAO1D,EAAO8I,GAC7B,OAAQ9I,EAAQ8I,EAAQC,KAAK/I,IAAUA,EAAM,GAAKA,CAAAA,CASnD,SAAgBgJ,EAAShJ,EAAO8I,EAASG,GACxC,OAAOjJ,EAAMgJ,QAAQF,EAASG,EAAAA,CAS/B,SAAgBC,EAASlJ,EAAOmJ,EAAQC,GACvC,OAAOpJ,EAAMqJ,QAAQF,EAAQC,EAQvB,CAAA,SAASE,EAAQtJ,EAAOa,GAC9B,OAAiC,EAA1Bb,EAAMuJ,WAAW1I,EAAS,CASlC,SAAgB2I,EAAQxJ,EAAOyJ,EAAOC,GACrC,OAAO1J,EAAMV,MAAMmK,EAAOC,EAOpB,CAAA,SAASC,EAAQ3J,GACvB,OAAOA,EAAMxB,MAOP,CAAA,SAASoL,EAAQ5J,GACvB,OAAOA,EAAMxB,MAAAA,CAQP,SAASqL,GAAQ7J,EAAO8J,GAC9B,OAAOA,EAAMjG,KAAK7D,GAAQA,CAQpB,CASA,SAAS+J,GAAQD,EAAOhB,GAC9B,OAAOgB,EAAMC,OAAO,SAAU/J,GAAS,OAAQ0D,EAAM1D,EAAO8I,KCzHnD,IAACkB,GAAO,EACPC,GAAS,EACTzL,GAAS,EACT4K,GAAW,EACXc,GAAY,EACZC,GAAa,GAYjB,SAASlG,GAAMjE,EAAOoK,EAAM1F,EAAQ2F,EAAMC,EAAOC,EAAU/L,EAAQgM,GACzE,MAAO,CAACxK,MAAOA,EAAOoK,KAAMA,EAAM1F,OAAQA,EAAQ2F,KAAMA,EAAMC,MAAOA,EAAOC,SAAUA,EAAUP,KAAMA,GAAMC,OAAQA,GAAQzL,OAAQA,EAAQiM,OAAQ,GAAID,SAAUA,EAQ5J,CAAA,SAASE,GAAMN,EAAME,GAC3B,OAAOpM,EAAO+F,GAAK,GAAI,KAAM,KAAM,GAAI,KAAM,KAAM,EAAGmG,EAAKI,UAAWJ,EAAM,CAAC5L,QAAS4L,EAAK5L,QAAS8L,EAAAA,CAM9F,SAASK,GAAMP,GACrB,KAAOA,EAAKA,MACXA,EAAOM,GAAKN,EAAKA,KAAM,CAACG,SAAU,CAACH,KAEpCP,GAAOO,EAAMA,EAAKI,SAMnB,CAmBA,SAAgBI,KAMf,OALAV,GAAYd,GAAW5K,GAAS8K,EAAOa,GAAYf,MAAc,EAE7Da,KAAwB,KAAdC,KACbD,GAAS,EAAGD,MAENE,EAMR,CAAA,SAAgBW,KACf,OAAOvB,EAAOa,GAAYf,GAAAA,CAM3B,SAAgB0B,KACf,OAAO1B,EAAAA,CAQD,SAAS9J,GAAOmK,EAAOC,GAC7B,OAAOF,EAAOW,GAAYV,EAAOC,EAO3B,CAAA,SAASqB,GAAOV,GACtB,OAAQA,GAEP,KAAK,EAAG,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GACtC,OAAO,EAER,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,IAE3D,KAAK,GAAI,KAAK,IAAK,KAAK,IACvB,OAAO,EAER,KAAK,GACJ,OAAO,EAER,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAC/B,OAAO,EAER,KAAK,GAAI,KAAK,GACb,OAAO,EAGT,OAAO,CAAA,CAuBD,SAASW,GAASX,GACxB,OAAO7G,EAAKlE,GAAM8J,GAAW,EAAG6B,GAAmB,KAATZ,EAAcA,EAAO,EAAa,KAATA,EAAcA,EAAO,EAAIA,IAOtF,CAQA,SAASa,GAAYb,GAC3B,MAAOH,GAAYW,OACdX,GAAY,IACfU,KAIF,OAAOG,GAAMV,GAAQ,GAAKU,GAAMb,IAAa,EAAI,GAAK,GAOhD,CAkBA,SAASiB,GAAUtK,EAAOuK,GAChC,OAASA,GAASR,QAEbV,GAAY,IAAMA,GAAY,KAAQA,GAAY,IAAMA,GAAY,IAAQA,GAAY,IAAMA,GAAY,MAG/G,OAAO5K,GAAMuB,EAAOiK,MAAWM,EAAQ,GAAe,IAAVP,MAA0B,IAAVD,OAOtD,SAASK,GAAWZ,GAC1B,KAAOO,aACEV,IAEP,KAAKG,EACJ,OAAOjB,GAER,KAAK,GAAI,KAAK,GACA,KAATiB,GAAwB,KAATA,GAClBY,GAAUf,IACX,MAED,KAAK,GACS,KAATG,GACHY,GAAUZ,GACX,MAED,KAAK,GACJO,KAIH,OAAOxB,EAQD,CAAA,SAASiC,GAAWhB,EAAMxJ,GAChC,KAAO+J,MAEFP,EAAOH,KAAc,KAGhBG,EAAOH,KAAc,IAAsB,KAAXW,QAG1C,MAAO,KAAOvL,GAAMuB,EAAOuI,GAAW,GAAK,IAAMnK,EAAc,KAAToL,EAAcA,EAAOO,KAOrE,CAAA,SAASU,GAAYzK,GAC3B,MAAQkK,GAAMF,OACbD,KAED,OAAOtL,GAAMuB,EAAOuI,GAAAA,CCvPd,SAASmC,GAASvL,GACxB,OD+HM,SAAkBA,GACxB,OAAOmK,GAAa,GAAInK,CAAAA,CChIjBwL,CAAQC,GAAM,GAAI,KAAM,KAAM,KAAM,CAAC,IAAKzL,EDuH3C,SAAgBA,GACtB,OAAOgK,GAAOC,GAAS,EAAGzL,GAASmL,EAAOQ,GAAanK,GAAQoJ,GAAW,EAAG,EAOvE,CC/HmDsC,CAAM1L,GAAQ,EAAG,CAAC,GAAIA,GAAAA,CAehF,SAAgByL,GAAOzL,EAAOoK,EAAM1F,EAAQiB,EAAM5E,EAAO4K,EAAUC,EAAQC,EAAQC,GAiBlF,IAhBA,IAAIjL,EAAQ,EACRkL,EAAS,EACTvN,EAASoN,EACTI,EAAS,EACTC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAY,EACZnC,EAAY,EACZG,EAAO,GACPC,EAAQvJ,EACRwJ,EAAWoB,EACXW,EAAY3G,EACZwE,EAAaE,EAEV+B,UACEF,EAAWhC,EAAWA,EAAYU,MAEzC,KAAK,GACJ,GAAgB,KAAZsB,GAAqD,IAAlC5C,EAAOa,EAAY3L,EAAS,GAAU,EACsD,GAA9G0K,EAAQiB,GAAcnB,EAAQgC,GAAQd,GAAY,IAAK,OAAQ,MAAOxB,EAAI7H,EAAQgL,EAAOhL,EAAQ,GAAK,MACzGwL,GAAa,GACd,KAGF,CAAA,KAAK,GAAI,KAAK,GAAI,KAAK,GACtBlC,GAAca,GAAQd,GACtB,MAED,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GAC9BC,GAAce,GAAWgB,GACzB,MAED,KAAK,GACJ/B,GAAcgB,GAASL,KAAU,EAAG,GACpC,SAED,KAAK,GACJ,OAAQD,MACP,KAAK,GAAI,KAAK,GACbhB,GAAO0C,GAAQlB,GAAUT,KAAQE,MAAUV,EAAM1F,EAAQoH,GAAeA,GACxE,MACD,QACC3B,GAAc,IAEhB,MAED,KAAK,IAAMgC,EACVN,EAAOhL,KAAW8I,EAAOQ,GAAckC,EAExC,KAAK,IAAMF,EAAU,KAAK,GAAI,KAAK,EAClC,OAAQjC,GAEP,KAAK,EAAG,KAAK,IAAKkC,EAAW,EAE7B,KAAK,GAAKL,GAA0B,GAAdM,IAAiBlC,EAAanB,EAAQmB,EAAY,MAAO,KAC1E8B,EAAW,GAAMtC,EAAOQ,GAAc3L,GACzCqL,GAAOoC,EAAW,GAAKO,GAAYrC,EAAa,IAAKxE,EAAMjB,EAAQlG,EAAS,EAAGsN,GAAgBU,GAAYxD,EAAQmB,EAAY,IAAK,IAAM,IAAKxE,EAAMjB,EAAQlG,EAAS,EAAGsN,GAAeA,GACzL,MAED,KAAK,GAAI3B,GAAc,IAEvB,QAGC,GAFAN,GAAOyC,EAAYG,GAAQtC,EAAYC,EAAM1F,EAAQ7D,EAAOkL,EAAQhL,EAAO8K,EAAQxB,EAAMC,EAAQ,GAAIC,EAAW,GAAI/L,EAAQmN,GAAWA,GAErH,MAAdzB,EACH,GAAe,IAAX6B,EACHN,GAAMtB,EAAYC,EAAMkC,EAAWA,EAAWhC,EAAOqB,EAAUnN,EAAQqN,EAAQtB,QAE/E,OAAmB,KAAXyB,GAA2C,MAA1B1C,EAAOa,EAAY,GAAa,IAAM6B,GAE9D,KAAK,IAAK,KAAK,IAAK,KAAK,IAAK,KAAK,IAClCP,GAAMzL,EAAOsM,EAAWA,EAAW3G,GAAQkE,GAAO4C,GAAQzM,EAAOsM,EAAWA,EAAW,EAAG,EAAGvL,EAAO8K,EAAQxB,EAAMtJ,EAAOuJ,EAAQ,GAAI9L,EAAQ+L,GAAWA,GAAWxJ,EAAOwJ,EAAU/L,EAAQqN,EAAQlG,EAAO2E,EAAQC,GACnN,MACD,QACCkB,GAAMtB,EAAYmC,EAAWA,EAAWA,EAAW,CAAC,IAAK/B,EAAU,EAAGsB,EAAQtB,IAIpF1J,EAAQkL,EAASE,EAAW,EAAGE,EAAWE,EAAY,EAAGhC,EAAOF,EAAa,GAAI3L,EAASoN,EAC1F,MAED,KAAK,GACJpN,EAAS,EAAImL,EAAOQ,GAAa8B,EAAWC,EAC7C,QACC,GAAIC,EAAW,EACd,GAAiB,KAAbjC,IACDiC,OACE,GAAiB,KAAbjC,GAAkC,GAAdiC,KAA6B,MD7D9DjC,GAAYd,GAAW,EAAIE,EAAOa,KAAcf,IAAY,EAExDa,KAAwB,KAAdC,KACbD,GAAS,EAAGD,MAENE,ICyDF,SAEF,OAAQC,GAAclL,EAAKiL,GAAYA,EAAYiC,GAElD,KAAK,GACJE,EAAYN,EAAS,EAAI,GAAK5B,GAAc,MAAO,GACnD,MAED,KAAK,GACJ0B,EAAOhL,MAAY8I,EAAOQ,GAAc,GAAKkC,EAAWA,EAAY,EACpE,MAED,KAAK,GAEW,KAAXxB,OACHV,GAAca,GAAQJ,OAEvBoB,EAASnB,KAAQkB,EAASvN,EAASmL,EAAOU,EAAOF,GAAcmB,GAAWR,OAAWZ,IACrF,MAED,KAAK,GACa,KAAbgC,GAAyC,GAAtBvC,EAAOQ,KAC7BgC,EAAW,IAIjB,OAAOR,CAkBR,CAAA,SAAgBc,GAASzM,EAAOoK,EAAM1F,EAAQ7D,EAAOkL,EAAQhL,EAAO8K,EAAQxB,EAAMC,EAAOC,EAAU/L,EAAQgM,GAK1G,IAJA,IAAIkC,EAAOX,EAAS,EAChBpG,EAAkB,IAAXoG,EAAehL,EAAQ,CAAC,IAC/BgG,EAAO6C,EAAOjE,GAETtH,EAAI,EAAGsO,EAAI,EAAGC,EAAI,EAAGvO,EAAIwC,IAASxC,EAC1C,IAAK,IAAIwO,EAAI,EAAGC,EAAItD,EAAOxJ,EAAO0M,EAAO,EAAGA,EAAOhE,EAAIiE,EAAId,EAAOxN,KAAM0O,EAAI/M,EAAO6M,EAAI9F,IAAQ8F,GAC1FE,EAAIvJ,EAAKmJ,EAAI,EAAIhH,EAAKkH,GAAK,IAAMC,EAAI9D,EAAQ8D,EAAG,OAAQnH,EAAKkH,QAChEvC,EAAMsC,KAAOG,GAEhB,OAAO9I,GAAKjE,EAAOoK,EAAM1F,EAAmB,IAAXqH,EAAe1D,EAAUgC,EAAMC,EAAOC,EAAU/L,EAAQgM,EAAAA,CAU1F,SAAgB+B,GAASvM,EAAOoK,EAAM1F,EAAQ8F,GAC7C,OAAOvG,GAAKjE,EAAOoK,EAAM1F,EAAQ0D,EAASnJ,EDtInCiL,ICsIiDV,EAAOxJ,EAAO,GAAI,GAAI,EAAGwK,EAW3E,CAAA,SAASgC,GAAaxM,EAAOoK,EAAM1F,EAAQlG,EAAQgM,GACzD,OAAOvG,GAAKjE,EAAOoK,EAAM1F,EAAQ4D,EAAakB,EAAOxJ,EAAO,EAAGxB,GAASgL,EAAOxJ,EAAOxB,EAAS,GAAI,GAAIA,EAAQgM,ECvLhH,CAAA,SAAgBwC,GAAQhN,EAAOxB,EAAQ+L,GACtC,OHaM,SAAevK,EAAOxB,GAC5B,OAA0B,GAAnB8K,EAAOtJ,EAAO,MAAiBxB,GAAU,EAAK8K,EAAOtJ,EAAO,KAAO,EAAKsJ,EAAOtJ,EAAO,KAAO,EAAKsJ,EAAOtJ,EAAO,KAAO,EAAKsJ,EAAOtJ,EAAO,GAAK,CAOhJ,CGrBEiN,CAAKjN,EAAOxB,IAEnB,KAAK,KACJ,OAAO2J,EAAS,SAAWnI,EAAQA,EAEpC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAEvE,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAE5D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAE5D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAC3D,OAAOmI,EAASnI,EAAQA,EAEzB,KAAK,KACJ,OAAOkI,EAAMlI,EAAQA,EAEtB,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAChD,OAAOmI,EAASnI,EAAQkI,EAAMlI,EAAQiI,EAAKjI,EAAQA,EAEpD,KAAK,KACJ,OAAQsJ,EAAOtJ,EAAOxB,EAAS,KAE9B,KAAK,IACJ,OAAO2J,EAASnI,EAAQiI,EAAKe,EAAQhJ,EAAO,qBAAsB,MAAQA,EAE3E,KAAK,IACJ,OAAOmI,EAASnI,EAAQiI,EAAKe,EAAQhJ,EAAO,qBAAsB,SAAWA,EAE9E,KAAK,GACJ,OAAOmI,EAASnI,EAAQiI,EAAKe,EAAQhJ,EAAO,qBAAsB,MAAQA,EAI7E,KAAK,KAAM,KAAK,KAAM,KAAK,KAC1B,OAAOmI,EAASnI,EAAQiI,EAAKjI,EAAQA,EAEtC,KAAK,KACJ,OAAOmI,EAASnI,EAAQiI,EAAK,QAAUjI,EAAQA,EAEhD,KAAK,KACJ,OAAOmI,EAASnI,EAAQgJ,EAAQhJ,EAAO,iBAAkBmI,EAAS,WAAaF,EAAK,aAAejI,EAEpG,KAAK,KACJ,OAAOmI,EAASnI,EAAQiI,EAAK,aAAee,EAAQhJ,EAAO,eAAgB,KAAQ0D,EAAM1D,EAAO,kBAA4E,GAAxDiI,EAAK,YAAce,EAAQhJ,EAAO,eAAgB,KAAYA,EAEnL,KAAK,KACJ,OAAOmI,EAASnI,EAAQiI,EAAK,iBAAmBe,EAAQhJ,EAAO,6BAA8B,IAAMA,EAEpG,KAAK,KACJ,OAAOmI,EAASnI,EAAQiI,EAAKe,EAAQhJ,EAAO,SAAU,YAAcA,EAErE,KAAK,KACJ,OAAOmI,EAASnI,EAAQiI,EAAKe,EAAQhJ,EAAO,QAAS,kBAAoBA,EAE1E,KAAK,KACJ,OAAOmI,EAAS,OAASa,EAAQhJ,EAAO,QAAS,IAAMmI,EAASnI,EAAQiI,EAAKe,EAAQhJ,EAAO,OAAQ,YAAcA,EAEnH,KAAK,KACJ,OAAOmI,EAASa,EAAQhJ,EAAO,qBAAsB,KAAOmI,EAAS,MAAQnI,EAE9E,KAAK,KACJ,OAAOgJ,EAAQA,EAAQA,EAAQhJ,EAAO,eAAgBmI,EAAS,MAAO,cAAeA,EAAS,MAAOnI,EAAO,IAAMA,EAEnH,KAAK,KAAM,KAAK,KACf,OAAOgJ,EAAQhJ,EAAO,oBAAqBmI,YAE5C,KAAK,KACJ,OAAOa,EAAQA,EAAQhJ,EAAO,oBAAqBmI,EAAS,cAAgBF,EAAK,gBAAiB,aAAc,WAAaE,EAASnI,EAAQA,EAE/I,KAAK,KACJ,IAAK0D,EAAM1D,EAAO,kBAAmB,OAAOiI,EAAK,oBAAsBuB,EAAOxJ,EAAOxB,GAAUwB,EAC/F,MAED,KAAK,KAAM,KAAK,KACf,OAAOiI,EAAKe,EAAQhJ,EAAO,YAAa,IAAMA,EAE/C,KAAK,KAAM,KAAK,KACf,OAAIuK,GAAYA,EAAS2C,KAAAA,SAAe7H,EAASxE,GAAS,OAAOrC,EAASqC,EAAO6C,EAAM2B,EAAQiF,MAAO,eAAoB,IACjHpB,EAAQlJ,GAASuK,EAAWA,EAAS/L,GAAQwB,OAAQ,OAAQ,GAAKA,EAASiI,EAAKe,EAAQhJ,EAAO,SAAU,IAAMA,EAAQiI,EAAK,mBAAqBiB,EAAQqB,EAAU,OAAQ,GAAK7G,EAAM6G,EAAU,QAAU7G,EAAM6G,EAAU,QAAU7G,EAAM1D,EAAO,QAAU,IAE7PiI,EAAKe,EAAQhJ,EAAO,SAAU,IAAMA,EAE5C,KAAK,KAAM,KAAK,KACf,OAAQuK,GAAYA,EAAS2C,cAAe7H,GAAW,OAAO3B,EAAM2B,EAAQiF,MAAO,iBAAwBtK,GAAAA,EAAQiI,EAAKe,EAAQA,EAAQhJ,EAAO,OAAQ,SAAU,QAAS,IAAMA,EAEjL,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACrC,OAAOgJ,EAAQhJ,EAAO,kBAAmBmI,EAAS,QAAUnI,EAE7D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACtC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACtC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAErC,GAAI2J,EAAO3J,GAAS,EAAIxB,EAAS,EAChC,OAAQ8K,EAAOtJ,EAAOxB,EAAS,IAE9B,KAAK,IAEJ,GAAkC,KAA9B8K,EAAOtJ,EAAOxB,EAAS,GAC1B,MAEF,KAAK,IACJ,OAAOwK,EAAQhJ,EAAO,mBAAoB,KAAOmI,EAAP,UAAiCD,GAAoC,KAA7BoB,EAAOtJ,EAAOxB,EAAS,GAAY,KAAO,UAAYwB,EAEzI,KAAK,IACJ,OAAQkJ,EAAQlJ,EAAO,UAAW,GAAKgN,GAAOhE,EAAQhJ,EAAO,UAAW,kBAAmBxB,EAAQ+L,GAAYvK,EAAQA,EAE1H,MAED,KAAK,KAAM,KAAK,KACf,OAAOgJ,EAAQhJ,EAAO,4CAA6C,SAAUmN,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GAAK,OAAQxF,EAAKmF,EAAI,IAAMC,EAAII,GAAMH,EAAKrF,EAAKmF,EAAI,UAAYG,EAAIC,GAAKA,GAAKH,GAAMI,EAAI,IAAMzN,CAE9L,GAAA,KAAK,KAEJ,GAAkC,MAA9BsJ,EAAOtJ,EAAOxB,EAAS,GAC1B,OAAOwK,EAAQhJ,EAAO,IAAK,IAAMmI,GAAUnI,EAC5C,MAED,KAAK,KACJ,OAAQsJ,EAAOtJ,EAA6B,KAAtBsJ,EAAOtJ,EAAO,IAAa,GAAK,KAErD,KAAK,IACJ,OAAOgJ,EAAQhJ,EAAO,gCAAiC,KAAOmI,GAAgC,KAAtBmB,EAAOtJ,EAAO,IAAa,UAAY,IAAxD,UAA+EmI,EAA/E,SAAwGF,EAAK,WAAajI,EAElL,KAAK,IACJ,OAAOgJ,EAAQhJ,EAAO,IAAK,IAAMiI,GAAMjI,EAEzC,MAED,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAChD,OAAOgJ,EAAQhJ,EAAO,UAAW,gBAAkBA,EAGrD,OAAOA,CCvID,CAAA,SAAS0N,GAAWnD,EAAUoD,GAGpC,IAFA,IAAIC,EAAS,GAEJvP,EAAI,EAAGA,EAAIkM,EAAS/L,OAAQH,IACpCuP,GAAUD,EAASpD,EAASlM,GAAIA,EAAGkM,EAAUoD,IAAa,GAE3D,OAAOC,CAUR,CAAA,SAAgBC,GAAWxI,EAASxE,EAAO0J,EAAUoD,GACpD,OAAQtI,EAAQgF,MACf,KAAK5B,EAAO,GAAIpD,EAAQkF,SAAS/L,OAAQ,MACzC,KAAK+J,EAAQ,KAAKD,EAAa,OAAOjD,EAAQoF,OAASpF,EAAQoF,QAAUpF,EAAQrF,MACjF,KAAKoI,EAAS,MAAO,GACrB,KAAKI,EAAW,OAAOnD,EAAQoF,OAASpF,EAAQrF,MAAQ,IAAM0N,GAAUrI,EAAQkF,SAAUoD,GAAY,IACtG,KAAKtF,EAAS,IAAKsB,EAAOtE,EAAQrF,MAAQqF,EAAQiF,MAAMhK,KAAK,MAAO,MAAO,GAG5E,OAAOqJ,EAAOY,EAAWmD,GAAUrI,EAAQkF,SAAUoD,IAAatI,EAAQoF,OAASpF,EAAQrF,MAAQ,IAAMuK,EAAW,IAAM,EAAA,CCQ3H,SAAgBuD,GAAUzI,EAASxE,EAAO0J,EAAUoD,GACnD,GAAItI,EAAQ7G,QAAU,IAChB6G,EAAQoF,OACZ,OAAQpF,EAAQgF,MACf,KAAK/B,EACJ,YADiBjD,EAAQoF,OAASuC,GAAO3H,EAAQrF,MAAOqF,EAAQ7G,OAAQ+L,IAEzE,KAAK/B,EACJ,OAAOkF,GAAU,CAAChD,GAAKrF,EAAS,CAACrF,MAAOgJ,EAAQ3D,EAAQrF,MAAO,IAAK,IAAMmI,MAAYwF,GACvF,KAAKtF,EACJ,GAAIhD,EAAQ7G,OACX,OL8DC,SAAkBsL,EAAO6D,GAC/B,OAAO7D,EAAMiE,IAAIJ,GAAUrN,KAAK,GAAA,CK/DpB0N,CAAQzD,EAAWlF,EAAQiF,MAAO,SAAUtK,GAClD,OAAQ0D,EAAM1D,EAAO2N,EAAW,0BAE/B,IAAK,aAAc,IAAK,cACvBhD,GAAKD,GAAKrF,EAAS,CAACiF,MAAO,CAACtB,EAAQhJ,EAAO,cAAe,IAAMkI,EAAM,UACtEyC,GAAKD,GAAKrF,EAAS,CAACiF,MAAO,CAACtK,MAC5B9B,EAAOmH,EAAS,CAACiF,MAAOP,GAAOQ,EAAUoD,KACzC,MAED,IAAK,gBACJhD,GAAKD,GAAKrF,EAAS,CAACiF,MAAO,CAACtB,EAAQhJ,EAAO,aAAc,IAAMmI,EAAS,gBACxEwC,GAAKD,GAAKrF,EAAS,CAACiF,MAAO,CAACtB,EAAQhJ,EAAO,aAAc,IAAMkI,EAAM,UACrEyC,GAAKD,GAAKrF,EAAS,CAACiF,MAAO,CAACtB,EAAQhJ,EAAO,aAAciI,EAAK,gBAC9D0C,GAAKD,GAAKrF,EAAS,CAACiF,MAAO,CAACtK,MAC5B9B,EAAOmH,EAAS,CAACiF,MAAOP,GAAOQ,EAAUoD,KAI3C,MAAO,EAUP,GAAA,CC/EA,IAAMM,GAAO,KAKPC,GAAQ,SAACC,EAAWtB,GAG/B,IAFA,IAAIxO,EAAIwO,EAAErO,OAEHH,GACL8P,EAAS,GAAJA,EAAUtB,EAAEtD,aAAalL,GAGhC,OAAO8P,CACT,EAGalB,GAAO,SAACJ,GACnB,OAAOqB,GAAMD,GAAMpB,EACrB,ECZMuB,GAAY,KACZC,GAAgB,gBAWtB,SAASC,GAAuBC,EAA4BC,GAC1D,OAAOD,EAASR,IAAI,SAAApI,GAclB,MAbkB,SAAdA,EAAK0E,OAEP1E,EAAK3F,MAAQ,GAAGT,OAAAiP,cAAa7I,EAAK3F,OAElC2F,EAAK3F,MAAQ2F,EAAK3F,MAAMyO,WAAW,IAAK,IAAAlP,OAAIiP,EAAS,MACrD7I,EAAK2E,MAAS3E,EAAK2E,MAAmByD,IAAI,SAAAW,GACxC,MAAO,GAAGnP,OAAAiP,EAAa,KAAAjP,OAAAmP,EACzB,IAGErP,MAAMsP,QAAQhJ,EAAK4E,WAA2B,eAAd5E,EAAK0E,OACvC1E,EAAK4E,SAAW+D,GAAuB3I,EAAK4E,SAAUiE,IAEjD7I,CACT,EACF,CAEwB,SAAAiJ,GACtBvL,GAAA,IAKIwL,EACAC,EACAC,EAPJC,OAAA,IAAA3L,EAG2B1D,EAAsB0D,EAF/C4L,EAAAD,EAAAvI,QAAAA,OAAO,IAAAwI,EAAGtP,EAAsBsP,EAChCC,EAAuDF,EAAAG,QAAvDA,OAAO,IAAAD,EAAGzP,EAA6CyP,EAOnDE,EAAwB,SAAC1L,EAAeqI,EAAgBsD,GAC5D,OAKEA,EAAOC,WAAWR,IAClBO,EAAOE,SAAST,IAChBO,EAAOZ,WAAWK,EAAW,IAAItQ,OAAS,EAEnC,IAAAe,OAAIsP,GAGNnL,CACT,EAuBM8L,EAAcL,EAAQ7P,QAE5BkQ,EAAY3L,KAX8C,SAAAwB,GACpDA,EAAQgF,OAASoF,GAAkBpK,EAAQrF,MAAM0P,SAAS,OAC3DrK,EAAQiF,MAAmB,GAAKjF,EAAQiF,MAAM,GAE5CtB,QAAQoF,GAAWU,GACnB9F,QAAQ+F,EAAiBK,GAEhC,GASI3I,EAAQuG,QACVwC,EAAY3L,KAAK8L,IAGnBH,EAAY3L,KAAK+L,IAEjB,IAAMC,EAA8B,SAClChO,EACAmF,EAIAgG,EACA8C,QALA,IAAA9I,IAAAA,EAAa,SAIb,IAAAgG,IAAAA,EAAW,SACX,IAAA8C,IAAAA,EAAiB,KAKjBjB,EAAeiB,EACfhB,EAAY9H,EACZ+H,EAAkB,IAAIrM,OAAO,KAAAnD,OAAKuP,EAAc,OAAE,KAElD,IAAMiB,EAAUlO,EAAImH,QAAQqF,GAAe,IACvCE,EAAWyB,GACbhD,GAAUhG,EAAW,UAAGgG,EAAM,KAAAzN,OAAIyH,EAAQ,OAAAzH,OAAMwQ,EAAO,MAAOA,GAG5DtJ,EAAQ+H,YACVD,EAAWD,GAAuBC,EAAU9H,EAAQ+H,YAGtD,IAAMyB,EAAkB,GAOxB,OALAC,GACE3B,EFnHC,SAAqB4B,GAC3B,IAAI3R,EAASoL,EAAOuG,GAEpB,OAAO,SAAU9K,EAASxE,EAAO0J,EAAUoD,GAG1C,IAFA,IAAIC,EAAS,GAEJvP,EAAI,EAAGA,EAAIG,EAAQH,IAC3BuP,GAAUuC,EAAW9R,GAAGgH,EAASxE,EAAO0J,EAAUoD,IAAa,GAEhE,OAAOC,CAQF,CAAA,CEmGDwC,CAAkBZ,EAAYjQ,OFlG5B,SAAU8F,GACXA,EAAQ+E,OACR/E,EAAUA,EAAQoF,SEgG4CwF,EAAMpM,KF/F9DwB,EAUb,KEwFW4K,CACT,EAcA,OAZAJ,EAAe5C,KAAOkC,EAAQ3Q,OAC1B2Q,EACGkB,OAAO,SAACC,EAAKC,GAKZ,OAJKA,EAAOzN,MACV0N,EAAiB,IAGZtC,GAAMoC,EAAKC,EAAOzN,KAC1B,EAAEmL,IACFwC,WACH,GAEGZ,CACT,CC1IO,IAAMa,GAAwB,IAAIlK,EAC5BmK,GAA0B/B,KAQ1BgC,GAAoBC,EAAMC,cAAkC,CACvEC,uBAAmB/L,EACnBgM,WAAYN,GACZO,OAAQN,KAGGO,GAAqBN,GAAkBO,SAGvCC,GAAgBP,EAAMC,mBAA8B9L,YAGjDqM,KACd,OAAOC,EAAAA,WAAWV,GACpB,CAkDM,SAAUW,GAAkBjH,GAC1B,IAAAjH,EAAwBmO,EAAAA,SAASlH,EAAMmH,eAAtCtC,EAAO9L,EAAA,GAAEqO,OACRV,EAAeK,gBAEjBM,EAAqBC,EAAAA,QAAQ,WACjC,IAAIhP,EAAQoO,EAYZ,OAVI1G,EAAM1H,MACRA,EAAQ0H,EAAM1H,MACL0H,EAAM9F,SACf5B,EAAQA,EAAMyE,uBAAuB,CAAE7C,OAAQ8F,EAAM9F,SAAU,IAG7D8F,EAAMuH,wBACRjP,EAAQA,EAAMyE,uBAAuB,CAAEd,mBAAmB,KAGrD3D,CACT,EAAG,CAAC0H,EAAMuH,sBAAuBvH,EAAM1H,MAAO0H,EAAM9F,OAAQwM,IAEtDC,EAASW,EAAAA,QACb,WACE,OAAAhD,GAAqB,CACnBnI,QAAS,CAAE+H,UAAWlE,EAAMkE,UAAWxB,OAAQ1C,EAAMwH,sBACrD3C,QAAOA,GAFT,EAIF,CAAC7E,EAAMwH,qBAAsBxH,EAAMkE,UAAWW,IAGhD4C,EAAAA,UAAU,YC3GK,SAAsBC,EAAMC,EAAMC,EAASC,GAC1D,IAAIC,OAA2D,EAE/D,QAAY,IAARA,EACF,QAASA,EAGX,GAAIJ,IAASC,EACX,OAAO,EAGT,GAAoB,iBAATD,IAAsBA,GAAwB,iBAATC,IAAsBA,EACpE,OAAO,EAGT,IAAII,EAAQpU,OAAOqU,KAAKN,GACpBO,EAAQtU,OAAOqU,KAAKL,GAExB,GAAII,EAAM7T,SAAW+T,EAAM/T,OACzB,OAAO,EAMT,IAHA,IAAIgU,EAAkBvU,OAAOS,UAAUC,eAAe8T,KAAKR,GAGlDS,EAAM,EAAGA,EAAML,EAAM7T,OAAQkU,IAAO,CAC3C,IAAIC,EAAMN,EAAMK,GAEhB,IAAKF,EAAgBG,GACnB,OAAO,EAQT,IAAY,KAFZP,OAAoE,SAEtC,IAARA,GALTJ,EAAKW,KACLV,EAAKU,GAKhB,OAAO,CAEV,CAED,OAAO,CACT,EDiESC,CAAazD,EAAS7E,EAAMmH,gBAAgBC,EAAWpH,EAAMmH,cACpE,EAAG,CAACnH,EAAMmH,gBAEV,IAAMoB,EAAyBjB,UAC7B,WAAM,MAAC,CACLb,kBAAmBzG,EAAMyG,kBACzBC,WAAYW,EACZV,OAAMA,EAHF,EAKN,CAAC3G,EAAMyG,kBAAmBY,EAAoBV,IAGhD,OACEJ,gBAACD,GAAkBkC,SAAS,CAAA9S,MAAO6S,GACjChC,EAAAlM,cAACyM,GAAc0B,SAAQ,CAAC9S,MAAOiR,GAAS3G,EAAMC,UAGpD,CEzHA,IAAAwI,GAAA,WAKE,SAAYA,EAAAjQ,EAAc/B,GAA1B,IAQC4F,EAAA7H,KAEDA,KAAAkU,OAAS,SAAChC,EAAwBiC,QAAA,IAAAA,IAAAA,EAAwCtC,IACxE,IAAMuC,EAAevM,EAAK7D,KAAOmQ,EAAehG,KAE3C+D,EAAWvJ,aAAad,EAAKvE,GAAI8Q,IACpClC,EAAWlQ,YACT6F,EAAKvE,GACL8Q,EACAD,EAAetM,EAAK5F,MAAOmS,EAAc,cAG/C,EAnBEpU,KAAKgE,KAAOA,EACZhE,KAAKsD,GAAK,gBAAgB7C,OAAAuD,GAC1BhE,KAAKiC,MAAQA,EAEbnB,EAAYd,KAAM,WAChB,MAAMqC,EAAY,GAAIyH,OAAOjC,EAAK7D,MACpC,EACD,CAiBH,OAHEiQ,EAAOrU,UAAAyU,QAAP,SAAQF,GACN,YADM,IAAAA,IAAAA,EAAwCtC,IACvC7R,KAAKgE,KAAOmQ,EAAehG,MAErC8F,CAAD,ICpCIK,GAAe,CACjBC,wBAAyB,EACzBC,YAAa,EACbC,kBAAmB,EACnBC,iBAAkB,EAClBC,iBAAkB,EAClBC,QAAS,EACTC,aAAc,EACdC,gBAAiB,EACjBC,YAAa,EACbC,QAAS,EACTC,KAAM,EACNC,SAAU,EACVC,aAAc,EACdC,WAAY,EACZC,aAAc,EACdC,UAAW,EACXC,QAAS,EACTC,WAAY,EACZC,YAAa,EACbC,aAAc,EACdC,WAAY,EACZC,cAAe,EACfC,eAAgB,EAChBC,gBAAiB,EACjBC,UAAW,EACXC,cAAe,EACfC,aAAc,EACdC,iBAAkB,EAClBC,WAAY,EACZC,WAAY,EACZC,QAAS,EACTC,MAAO,EACPC,QAAS,EACTC,QAAS,EACTC,OAAQ,EACRC,OAAQ,EACRC,KAAM,EACNC,gBAAiB,EAEjBC,YAAa,EACbC,aAAc,EACdC,YAAa,EACbC,gBAAiB,EACjBC,iBAAkB,EAClBC,iBAAkB,EAClBC,cAAe,EACfC,YAAa,GC7CS,SAAAC,GAAiB3R,GACvC,OAEGA,EAA8C4R,aAC9C5R,EAAoB1B,MACrB,WAEJ,CCTA,IAAMuT,GAAU,SAAC/I,GAAc,OAAAA,GAAK,KAAOA,GAAK,KAexB,SAAAgJ,GAAmBjH,GAGzC,IAFA,IAAIzB,EAAS,GAEJvP,EAAI,EAAGA,EAAIgR,EAAO7Q,OAAQH,IAAK,CACtC,IAAMiP,EAAI+B,EAAOhR,GAEjB,GAAU,IAANA,GAAiB,MAANiP,GAA2B,MAAd+B,EAAO,GACjC,OAAOA,EAGLgH,GAAQ/I,GACVM,GAAU,IAAMN,EAAEiJ,cAElB3I,GAAUN,CAEb,CAED,OAAOM,EAAO0B,WAAW,OAAS,IAAM1B,EAASA,CACnD,CCjCwB,SAAA4I,GAAWC,GACjC,MAAuB,mBAATA,CAChB,CCFwB,SAAAC,GAAc7J,GACpC,OACQ,OAANA,GACa,iBAANA,GACPA,EAAE8J,YAAY7T,OAAS7E,OAAO6E,QAE5B,UAAW+J,GAAKA,EAAE+J,SAExB,CCNwB,SAAAC,GAAkBrS,GACxC,MAAyB,iBAAXA,GAAuB,sBAAuBA,CAC9D,CCoBA,IAAMsS,GAAY,SAACC,GACjB,OAAAA,UAAmD,IAAVA,GAA6B,KAAVA,CAA5D,EAEWC,GAAgB,SAACC,GAC5B,ICzBsCnU,EAAc9C,EDyB9Ce,EAAQ,GAEd,IAAK,IAAM4R,KAAOsE,EAAK,CACrB,IAAMC,EAAMD,EAAItE,GACXsE,EAAItY,eAAegU,KAAQmE,GAAUI,KAGrC7X,MAAMsP,QAAQuI,IAAQA,EAAIC,OAAUX,GAAWU,GAClDnW,EAAM8C,KAAK,GAAAtE,OAAG6X,GAAUzE,GAAI,KAAKuE,EAAK,KAC7BR,GAAcQ,GACvBnW,EAAM8C,KAANhF,MAAAkC,OAAW,GAAGxB,OAAAoT,EAAO,OAAKqE,GAAcE,IAAI,GAAA,CAAE,MAAK,IAEnDnW,EAAM8C,KAAK,GAAGtE,OAAA6X,GAAUzE,GAAS,MAAApT,QCrCCuD,EDqCe6P,ECnCxC,OAFuC3S,EDqCMkX,ICnCpB,kBAAVlX,GAAiC,KAAVA,EAC1C,GAGY,iBAAVA,GAAgC,IAAVA,GAAiB8C,KAAQuU,IAAcvU,EAAKwM,WAAW,MAIjF1G,OAAO5I,GAAOwD,OAHZ,GAAGjE,OAAAS,EAAS,OD8ByC,MAE7D,CAED,OAAOe,CACT,EAEc,SAAUuW,GACtBP,EACAQ,EACAvG,EACAiC,GAEA,OAAI6D,GAAUC,GACL,GAILF,GAAkBE,GACb,CAAC,IAAKxX,OAAAwX,EAAkDS,oBAI7DhB,GAAWO,IE5DRP,GADmCC,EF8DhBM,IE7DGN,EAAK/X,WAAa+X,EAAK/X,UAAU+Y,mBF6D1BF,EAoBzB,CAACR,GAFDO,GAjBQP,EAAMQ,GAiBSA,EAAkBvG,EAAYiC,GAM5D8D,aAAiBhE,GACf/B,GACF+F,EAAM/D,OAAOhC,EAAYiC,GAClB,CAAC8D,EAAM5D,QAAQF,KAEf,CAAC8D,GAKRL,GAAcK,GACTC,GAAcD,GAGlB1X,MAAMsP,QAAQoI,GAUZ1X,MAAMX,UAAUa,OAAOV,MAAMY,EANrBsX,EAMwChJ,IANjC,SAAA2J,GACpB,OAAAJ,GAAeI,EAAUH,EAAkBvG,EAAYiC,EAAvD,IAJO,CAAC8D,EAAMtG,YAtCZ,IE/DsCgG,CF2G5C,CGzGwB,SAAAkB,GAAoC5W,GAC1D,IAAK,IAAI1C,EAAI,EAAGA,EAAI0C,EAAMvC,OAAQH,GAAK,EAAG,CACxC,IAAMsH,EAAO5E,EAAM1C,GAEnB,GAAImY,GAAW7Q,KAAUkR,GAAkBlR,GAGzC,OAAO,CAEV,CAED,OAAO,CACT,CCbgB,SAAAiS,GAAYxK,EAAwBC,GAClD,OAAOD,GAAKC,EAAI,UAAGD,EAAC,KAAA7N,OAAI8N,GAAMD,GAAKC,GAAK,EAC1C,CAEgB,SAAAwK,GAAgBhT,EAAeiT,GAC7C,GAAmB,IAAfjT,EAAIrG,OACN,MAAO,GAIT,IADA,IAAIuZ,EAASlT,EAAI,GACRxG,EAAI,EAAGA,EAAIwG,EAAIrG,OAAQH,IAC9B0Z,GAAUD,EAAMA,EAAMjT,EAAIxG,GAAKwG,EAAIxG,GAErC,OAAO0Z,CACT,CCXA,IAAAC,GAAA,WAKE,SAAYA,EAAAjX,EAAuB+O,GACjChR,KAAKiC,MAAQA,EACbjC,KAAKgR,YAAcA,EACnBhR,KAAKmZ,SAAWN,GAAc5W,GAI9ByF,EAAWW,WAAWrI,KAAKgR,YAAc,EAC1C,CAkCH,OAhCEkI,EAAYtZ,UAAAwZ,aAAZ,SACEC,EACAZ,EACAvG,EACAC,GAEA,IAGMpP,EAAMoP,EAHI4G,GACdP,GAAQxY,KAAKiC,MAA0BwW,EAAkBvG,EAAYC,IAE3C,IACtB7O,EAAKtD,KAAKgR,YAAcqI,EAG9BnH,EAAWlQ,YAAYsB,EAAIA,EAAIP,IAGjCmW,EAAAtZ,UAAA0Z,aAAA,SAAaD,EAAkBnH,GAC7BA,EAAWjJ,WAAWjJ,KAAKgR,YAAcqI,IAG3CH,EAAYtZ,UAAA2Z,aAAZ,SACEF,EACAZ,EACAvG,EACAC,GAEIkH,EAAW,GAAG3R,EAAWW,WAAWrI,KAAKgR,YAAcqI,GAG3DrZ,KAAKsZ,aAAaD,EAAUnH,GAC5BlS,KAAKoZ,aAAaC,EAAUZ,EAAkBvG,EAAYC,IAE7D+G,CAAD,ICbaM,GAAezH,EAAMC,mBAAwC9L,GAE7DuT,GAAgBD,GAAanH,SCvClB,SAAAqH,GACtBlO,EACAmO,EACAC,GAEA,YAFA,IAAAA,IAAAA,EAAiE/Y,GAEzD2K,EAAMqO,QAAUD,EAAaC,OAASrO,EAAMqO,OAAUF,GAAiBC,EAAaC,KAC9F,CCTA,IAAMC,GAAgB,WAIhBC,GAAc,GAGdC,GAAoB,SAAC5Y,GAAiB,OAAA0I,OAAOC,aAAa3I,GAAQA,EAAO,GAAK,GAAK,IAA7C,EAGpB,SAAA6Y,GAAuB7Y,GAC7C,IACI2M,EADA/J,EAAO,GAIX,IAAK+J,EAAIlE,KAAKD,IAAIxI,GAAO2M,EAAIgM,GAAahM,EAAKA,EAAIgM,GAAe,EAChE/V,EAAOgW,GAAkBjM,EAAIgM,IAAe/V,EAG9C,OAAQgW,GAAkBjM,EAAIgM,IAAe/V,GAAMkG,QAAQ4P,GAAe,QAC5E,CCjBwB,SAAAI,GAAoBC,GAC1C,OAAOF,GAAuB9L,GAAKgM,KAAS,EAC9C,CCHc,SAAUC,GACtBC,EACAhZ,GAIA,IAFA,IAAM4X,EAAiC,CAACoB,EAAQ,IAEvC9a,EAAI,EAAG+a,EAAMjZ,EAAe3B,OAAQH,EAAI+a,EAAK/a,GAAK,EACzD0Z,EAAOlU,KAAK1D,EAAe9B,GAAI8a,EAAQ9a,EAAI,IAG7C,OAAO0Z,CACT,CCMA,OAAMsB,GAAS,SAAyBC,GACtC,OAAArb,OAAOC,OAAOob,EAAK,CAAEnC,OAAO,GAA5B,EAOF,SAAStV,GACP0X,OACA,IAAkDpZ,EAAA,GAAAC,EAAA,EAAlDA,EAAkD7B,UAAAC,OAAlD4B,IAAAD,EAAkDC,EAAA,GAAA7B,UAAA6B,GAElD,GAAIoW,GAAW+C,IAAW7C,GAAc6C,GAGtC,OAAOF,GACL/B,GACE4B,GAAkBzZ,EAAWV,EAAA,CAJHwa,GAMrBpZ,GAAc,MAMzB,IAAMqZ,EAAmBD,EAEzB,OAC4B,IAA1BpZ,EAAe3B,QACa,IAA5Bgb,EAAiBhb,QACc,iBAAxBgb,EAAiB,GAEjBlC,GAAekC,GAGjBH,GACL/B,GAAe4B,GAAkBM,EAAkBrZ,IAEvD,CCtDA,IAAMsZ,GAA8B,mBAAXC,QAAyBA,OAAOC,IAGnDC,GAAkBH,GAAYC,OAAOC,IAAI,cAAgB,MACzDE,GAAyBJ,GAAYC,OAAOC,IAAI,qBAAuB,MAKvEG,GAAgB,CACpBC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdvB,cAAc,EACdtC,aAAa,EACb8D,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,QAAQ,EACRC,WAAW,EACXjQ,MAAM,GAGFkQ,GAAgB,CACpBzX,MAAM,EACNtE,QAAQ,EACRE,WAAW,EACX8b,QAAQ,EACRC,QAAQ,EACRlc,WAAW,EACXmc,OAAO,GAWHC,GAAe,CACnB/D,UAAU,EACV1E,SAAS,EACTwG,cAAc,EACdtC,aAAa,EACbkE,WAAW,EACXjQ,MAAM,GAGFuQ,KAAYvX,GAAA,CAAA,GACfwW,IAlByB,CAC1BjD,UAAU,EACViE,QAAQ,EACRnC,cAAc,EACdtC,aAAa,EACbkE,WAAW,GAcXjX,GAACuW,IAAkBe,OAcrB,SAASG,GAAWC,GAElB,OAPqB,SAFrBlb,EASWkb,IAP8Blb,EAAOwK,KAAKuM,YAE7BgD,GAMfe,GAIF,aAAcI,EACjBH,GAAaG,EAAoB,UACjCjB,GAjBN,IACEja,CAiBF,CAEA,IAAME,GAAiB9B,OAAO8B,eACxBib,GAAsB/c,OAAO+c,oBAC7BC,GAAwBhd,OAAOgd,sBAC/BC,GAA2Bjd,OAAOid,yBAClCC,GAAiBld,OAAOkd,eACxBC,GAAkBnd,OAAOS,UAiBP,SAAA2c,GAItBC,EAAoBC,EAAoBC,GACxC,GAA+B,iBAApBD,EAA8B,CAGvC,GAAIH,GAAiB,CACnB,IAAMK,EAAqBN,GAAeI,GACtCE,GAAsBA,IAAuBL,IAC/CC,GAAqBC,EAAiBG,EAAoBD,EAE7D,CAED,IAAIlJ,EAA4B0I,GAAoBO,GAEhDN,KACF3I,EAAOA,EAAK/S,OAAO0b,GAAsBM,KAM3C,IAHA,IAAMG,EAAgBZ,GAAWQ,GAC3BK,EAAgBb,GAAWS,GAExBld,EAAI,EAAGA,EAAIiU,EAAK9T,SAAUH,EAAG,CACpC,IAAMsU,EAAML,EAAKjU,GACjB,KACIsU,KAAO4H,IACPiB,GAAeA,EAAY7I,IAC3BgJ,GAAiBhJ,KAAOgJ,GACxBD,GAAiB/I,KAAO+I,GAC1B,CACA,IAAME,EAAaV,GAAyBK,EAAiB5I,GAE7D,IAEE5S,GAAeub,EAAiB3I,EAAKiJ,EACtC,CAAC,MAAOpO,GAER,CACF,CACF,CACF,CAED,OAAO8N,CACT,CCtIA,IAAAO,GAAA,WAIE,SAAAA,IAAA,IAGClV,EAAA7H,KAEDA,KAAAgd,cAAgB,WACd,IAAMja,EAAM8E,EAAKwR,SAAS1H,WAC1B,IAAK5O,EAAK,MAAO,GACjB,IAAMqD,EAAQb,IAMR0X,EAAWlE,GALH,CACZ3S,GAAS,UAAU3F,OAAA2F,EAAQ,KAC3B,GAAA3F,OAAGvC,EAAgB,WACnB,GAAGuC,OAAAlC,EAAoB,MAAAkC,OAAAjC,EAAa,MAECyM,OAAOnM,SAAsB,KAEpE,MAAO,UAAU2B,OAAAwc,EAAY,KAAAxc,OAAAsC,aAC/B,EAUA/C,KAAAkd,aAAe,WACb,GAAIrV,EAAKsV,OACP,MAAM9a,EAAY,GAGpB,OAAOwF,EAAKmV,eACd,EAEAhd,KAAAod,gBAAkB,iBAChB,GAAIvV,EAAKsV,OACP,MAAM9a,EAAY,GAGpB,IAAMU,EAAM8E,EAAKwR,SAAS1H,WAC1B,IAAK5O,EAAK,MAAO,GAEjB,IAAMyI,IAAKjH,EAAA,CAAA,GACRrG,GAAU,GACXqG,EAAChG,GAAkBC,EACnB+F,EAAA8Y,wBAAyB,CACvBC,OAAQva,MAINqD,EAAQb,IAMd,OALIa,IACDoF,EAAcpF,MAAQA,GAIlB,CAAC2L,6BAAWvG,EAAK,CAAEqI,IAAI,YAChC,EAyDA7T,KAAAud,KAAO,WACL1V,EAAKsV,QAAS,CAChB,EApHEnd,KAAKqZ,SAAW,IAAI3R,EAAW,CAAEF,UAAU,IAC3CxH,KAAKmd,QAAS,CACf,CAmHH,OAnGEJ,EAAand,UAAA4d,cAAb,SAAc/R,GACZ,GAAIzL,KAAKmd,OACP,MAAM9a,EAAY,GAGpB,OAAO0P,EAAAlM,cAAC4M,GAAiB,CAAC3O,MAAO9D,KAAKqZ,UAAW5N,IAqCnDsR,EAAwBnd,UAAA6d,yBAAxB,SAAyBC,GAErB,MAAMrb,EAAY,IAuDvB0a,CAAD,qLP9CwB,SAAcvR,GACpC,IAAMmS,EAAa5L,EAAMS,WAAWgH,IAC9BoE,EAAe9K,EAAOA,QAC1B,WAAM,OAjDV,SAAoB+G,EAAsB8D,GACxC,IAAK9D,EACH,MAAMxX,EAAY,IAGpB,GAAIqV,GAAWmC,GAWb,OAVgBA,EACY8D,GAY9B,GAAIpd,MAAMsP,QAAQgK,IAA2B,iBAAVA,EACjC,MAAMxX,EAAY,GAGpB,OAAOsb,EAAkBze,EAAAA,EAAA,CAAA,EAAAye,GAAe9D,GAAUA,CACpD,CAyBUgE,CAAWrS,EAAMqO,MAAO8D,EAAW,EACzC,CAACnS,EAAMqO,MAAO8D,IAGhB,OAAKnS,EAAMC,SAIJsG,EAAClM,cAAA2T,GAAaxF,SAAS,CAAA9S,MAAO0c,GAAepS,EAAMC,UAHjD,IAIX,cQnG2B,CACzB/D,WAAUA,EACVkK,UAASA,sBCOa,SACtByI,OACA,IAA8ChZ,EAAA,GAAAC,EAAA,EAA9CA,EAA8C7B,UAAAC,OAA9C4B,IAAAD,EAA8CC,EAAA,GAAA7B,UAAA6B,GAE9C,IAAMW,EAAQc,GAAGhD,WAAA,EAAAE,EAAA,CAAQoa,GAAYhZ,OAC/BqX,EAAoB,aAAajY,OAAAyZ,GAAoB4D,KAAK/O,UAAU9M,KACpE8b,EAAc,IAAI7E,GAAmBjX,EAAOyW,GAM5CsF,EAAoE,SAAAxS,GACxE,IAAMyS,EAAM1L,KACNsH,EAAQ9H,EAAMS,WAAWgH,IAGzBH,EAFctH,EAAMmM,OAAOD,EAAI/L,WAAWzJ,mBAAmBiQ,IAEtCyF,QA8B7B,OAbIF,EAAI/L,WAAWnK,QACjBwR,EAAaF,EAAU7N,EAAOyS,EAAI/L,WAAY2H,EAAOoE,EAAI9L,QAIzDJ,EAAMqM,gBAAgB,WACpB,IAAKH,EAAI/L,WAAWnK,OAElB,OADAwR,EAAaF,EAAU7N,EAAOyS,EAAI/L,WAAY2H,EAAOoE,EAAI9L,QAClD,WAAM,OAAA4L,EAAYzE,aAAaD,EAAU4E,EAAI/L,YAExD,EAAG,CAACmH,EAAU7N,EAAOyS,EAAI/L,WAAY2H,EAAOoE,EAAI9L,SAG3C,IACT,EAEA,SAASoH,EACPF,EACA7N,EACA0G,EACA2H,EACA1H,GAEA,GAAI4L,EAAY5E,SACd4E,EAAYxE,aACVF,EACApa,EACAiT,EACAC,OAEG,CACL,IAAMkM,EAAUnf,EAAAA,EAAA,CAAA,EACXsM,GACH,CAAAqO,MAAOH,GAAelO,EAAOqO,EAAOmE,EAAqBpE,gBAG3DmE,EAAYxE,aAAaF,EAAUgF,EAASnM,EAAYC,EACzD,CACF,CAED,OAAOJ,EAAMuM,KAAKN,EACpB,wCCjFwB,SACtB3D,OACA,IAA8ChZ,EAAA,GAAAC,EAAA,EAA9CA,EAA8C7B,UAAAC,OAA9C4B,IAAAD,EAA8CC,EAAA,GAAA7B,UAAA6B,GAa9C,IAAMW,EAAQ8W,GAAgBhW,GAAWhD,WAAA,EAAAE,EAAA,CAAAoa,GAAYhZ,GAA2B,KAC1E2C,EAAOkW,GAAoBjY,GACjC,OAAO,IAAIgS,GAAUjQ,EAAM/B,EAC7B,sBVsDE,IAAM4X,EAAQrH,aAAWgH,IAEzB,IAAKK,EACH,MAAMxX,EAAY,IAGpB,OAAOwX,CACT,sBW9EwB,SACtB0E,GAMA,IAAMC,EAAYzM,EAAM0M,WACtB,SAACjT,EAAOkT,GACN,IACMC,EAAYjF,GAAelO,EADnBuG,EAAMS,WAAWgH,IACgB+E,EAAU3E,cAUzD,OAAO7H,EAAClM,cAAA0Y,EAAcrf,EAAA,CAAA,EAAAsM,EAAO,CAAAqO,MAAO8E,EAAWD,IAAKA,IACtD,GAKF,OAFAF,EAAUlH,YAAc,aAAA7W,OAAa4W,GAAiBkH,GAAU,KAEzDK,GAAMJ,EAAWD,EAC1B,ICwGAM,GAAe,IAAI/V,IAxIF,CACf,IACA,OACA,UACA,OACA,UACA,QACA,QACA,IACA,OACA,MACA,MACA,MACA,aACA,OACA,KACA,SACA,SACA,UACA,OACA,OACA,MACA,WACA,OACA,WACA,KACA,MACA,UACA,MACA,SACA,MACA,KACA,KACA,KACA,QACA,WACA,aACA,SACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,SACA,SACA,KACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,SACA,QACA,SACA,KACA,OACA,OACA,MACA,OACA,OACA,WACA,OACA,QACA,MACA,WACA,SACA,KACA,WACA,SACA,SACA,IACA,QACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,SACA,UACA,SACA,QACA,SACA,OACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,IACA,KACA,MACA,MACA,QACA,MACA,SACA,WACA,OACA,UACA,gBACA,IACA,QACA,OACA,iBACA,SACA,OACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,MACA,OACA,UCrIIgW,GAAc,wCAEdC,GAAe,WAMG,SAAAC,GAAO7E,GAC7B,OAAOA,EACJjQ,QAAQ4U,GAAa,KACrB5U,QAAQ6U,GAAc,GAC3B,CCZwB,SAAAE,GAAMvZ,GAC5B,MACoB,iBAAXA,IAGH,CAER,CCPA,SAASwZ,GAAiBxZ,EAAayZ,EAAaC,GAGlD,QAHkD,IAAAA,IAAAA,GAAkB,IAG/DA,IAAexH,GAAclS,KAAYnF,MAAMsP,QAAQnK,GAC1D,OAAOyZ,EAGT,GAAI5e,MAAMsP,QAAQsP,GAChB,IAAK,IAAItL,EAAM,EAAGA,EAAMsL,EAAOzf,OAAQmU,IACrCnO,EAAOmO,GAAOqL,GAAiBxZ,EAAOmO,GAAMsL,EAAOtL,SAEhD,GAAI+D,GAAcuH,GACvB,IAAK,IAAMtL,KAAOsL,EAChBzZ,EAAOmO,GAAOqL,GAAiBxZ,EAAOmO,GAAMsL,EAAOtL,IAIvD,OAAOnO,CACT,CCXA,IAAMyJ,GAAOhB,GAAK3P,GAKlB6gB,GAAA,WAQE,SAAAA,EAAYpd,EAAqB+O,EAAqBsO,GACpDtf,KAAKiC,MAAQA,EACbjC,KAAKuf,cAAgB,GACrBvf,KAAKmZ,eAEYjT,IAAdoZ,GAA2BA,EAAUnG,WACtCN,GAAc5W,GAChBjC,KAAKgR,YAAcA,EACnBhR,KAAKwf,SAAWpQ,GAAMD,GAAM6B,GAC5BhR,KAAKsf,UAAYA,EAIjB5X,EAAWW,WAAW2I,EACvB,CAmEH,OAjEEqO,EAAAzf,UAAA6f,wBAAA,SACEhH,EACAvG,EACAC,GAEA,IAAIlO,EAAQjE,KAAKsf,UACbtf,KAAKsf,UAAUG,wBAAwBhH,EAAkBvG,EAAYC,GACrE,GAGJ,GAAInS,KAAKmZ,WAAahH,EAAOhE,KAC3B,GAAInO,KAAKuf,eAAiBrN,EAAWvJ,aAAa3I,KAAKgR,YAAahR,KAAKuf,eACvEtb,EAAQ6U,GAAY7U,EAAOjE,KAAKuf,mBAC3B,CACL,IAAMG,EAAY3G,GAChBP,GAAQxY,KAAKiC,MAAOwW,EAAkBvG,EAAYC,IAE9CwN,EAAOC,GAAaxQ,GAAMpP,KAAKwf,SAAUE,KAAe,GAE9D,IAAKxN,EAAWvJ,aAAa3I,KAAKgR,YAAa2O,GAAO,CACpD,IAAME,EAAqB1N,EAAOuN,EAAW,IAAIjf,OAAAkf,QAAQzZ,EAAWlG,KAAKgR,aACzEkB,EAAWlQ,YAAYhC,KAAKgR,YAAa2O,EAAME,EAChD,CAED5b,EAAQ6U,GAAY7U,EAAO0b,GAC3B3f,KAAKuf,cAAgBI,CACtB,KACI,CAIL,IAHA,IAAIG,EAAc1Q,GAAMpP,KAAKwf,SAAUrN,EAAOhE,MAC1CpL,EAAM,GAEDxD,EAAI,EAAGA,EAAIS,KAAKiC,MAAMvC,OAAQH,IAAK,CAC1C,IAAMwgB,EAAW/f,KAAKiC,MAAM1C,GAE5B,GAAwB,iBAAbwgB,EACThd,GAAOgd,OAGF,GAAIA,EAAU,CACnB,IAAMC,EAAajH,GACjBP,GAAQuH,EAAUtH,EAAkBvG,EAAYC,IAGlD2N,EAAc1Q,GAAM0Q,EAAaE,EAAazgB,GAC9CwD,GAAOid,CACR,CACF,CAED,GAAIjd,EAAK,CACP,IAAMkd,EAAOL,GAAaE,IAAgB,GAErC5N,EAAWvJ,aAAa3I,KAAKgR,YAAaiP,IAC7C/N,EAAWlQ,YACThC,KAAKgR,YACLiP,EACA9N,EAAOpP,EAAK,IAAItC,OAAAwf,QAAQ/Z,EAAWlG,KAAKgR,cAI5C/M,EAAQ6U,GAAY7U,EAAOgc,EAC5B,CACF,CAED,OAAOhc,GAEVob,CAAD,IClEMa,GAAyC,CAAA,EAmK/C,SAASC,GAKPza,EACAiC,EACA1F,GAEA,IAAMme,EAAqBrI,GAAkBrS,GACvC2a,EAAwB3a,EACxB4a,GAAwBrB,GAAMvZ,GAGlCnB,EAGEoD,EAAO4Y,MAHTA,aAAQ5f,EAAW4D,EACnB2L,EAEEvI,EAFsEqJ,YAAxEA,OAAc,IAAAd,EA/KlB,SACEoH,EACAkJ,GAEA,IAAMxc,EAA8B,iBAAhBsT,EAA2B,KAAO0H,GAAO1H,GAE7D4I,GAAYlc,IAASkc,GAAYlc,IAAS,GAAK,EAE/C,IAAMgN,EAAc,GAAGvQ,OAAAuD,cAAQkW,GAG7B1b,EAAawF,EAAOkc,GAAYlc,KAGlC,OAAOwc,EAAoB,GAAG/f,OAAA+f,EAAqB,KAAA/f,OAAAuQ,GAAgBA,CACrE,CAgKkByP,CAAW9Y,EAAQ2P,YAAa3P,EAAQ6Y,mBAAkBtQ,EACxEC,EACExI,EADuC2P,YAAzCA,OAAc,IAAAnH,ECpNM,SAAoBzK,GAC1C,OAAOuZ,GAAMvZ,GAAU,UAAUjF,OAAAiF,GAAW,UAAUjF,OAAA4W,GAAiB3R,OACzE,CDkNkBgb,CAAoBhb,KAG9BgT,EACJ/Q,EAAQ2P,aAAe3P,EAAQqJ,YAC3B,GAAAvQ,OAAGue,GAAOrX,EAAQ2P,aAAgB,KAAA7W,OAAAkH,EAAQqJ,aAC1CrJ,EAAQqJ,aAAeA,EAGvB2P,EACJP,GAAsBC,EAAsBE,MACxCF,EAAsBE,MAAM9f,OAAO8f,GAAyCtV,OAAOnM,SAClFyhB,EAEDtO,EAAsBtK,EAAOsK,kBAEnC,GAAImO,GAAsBC,EAAsBpO,kBAAmB,CACjE,IAAM2O,EAAsBP,EAAsBpO,kBAElD,GAAItK,EAAQsK,kBAAmB,CAC7B,IAAM4O,EAA4BlZ,EAAQsK,kBAG1CA,EAAoB,SAACrC,EAAMkR,GACzB,OAAAF,EAAoBhR,EAAMkR,IAC1BD,EAA0BjR,EAAMkR,EADhC,CAEH,MACC7O,EAAoB2O,CAEvB,CAED,IAAMG,EAAiB,IAAI1B,GACzBpd,EACAyW,EACA0H,EAAsBC,EAAsBU,oBAAoC7a,GAGlF,SAAS8a,EAAiBxV,EAAoCkT,GAC5D,OA9IJ,SACEuC,EACAzV,EACA0V,GAGE,IAAOC,EAMLF,EAAkBV,MALpBQ,EAKEE,EALYF,eACdnH,EAIEqH,EAAkBrH,aAHpBwH,EAGEH,EAHgBG,mBAClB1I,EAEEuI,EAAkBvI,kBADpBhT,EACEub,SAEEI,EAAetP,EAAMS,WAAWgH,IAChCyE,EAAM1L,KACNN,EAAoBgP,EAAmBhP,mBAAqBgM,EAAIhM,kBAOhE4H,EAAQH,GAAelO,EAAO6V,EAAczH,IAAiB/Y,EAE7Dwd,EA/DR,SACEkC,EACA/U,EACAqO,GAYA,IAVA,IAQIyH,EAREjD,SAGD7S,GAAK,CAER+V,eAAWrb,EACX2T,MAAKA,IAIEta,EAAI,EAAGA,EAAIghB,EAAM7gB,OAAQH,GAAK,EAAG,CAExC,IAAMiiB,EAAkB9J,GADxB4J,EAAUf,EAAMhhB,IAC8B+hB,EAAQjD,GAAWiD,EAEjE,IAAK,IAAMzN,KAAO2N,EAChBnD,EAAQxK,GACE,cAARA,EACIiF,GAAYuF,EAAQxK,GAA4B2N,EAAgB3N,IACxD,UAARA,SACOwK,EAAQxK,IAAS2N,EAAgB3N,IACtC2N,EAAgB3N,EAE3B,CAMD,OAJIrI,EAAM+V,YACRlD,EAAQkD,UAAYzI,GAAYuF,EAAQkD,UAAW/V,EAAM+V,YAGpDlD,CACT,CA6BkBoD,CAAsBN,EAAgB3V,EAAOqO,GACvDiH,EAAgCzC,EAAQqD,IAAMhc,EAC9Cic,EAA6B,CAAA,EAEnC,IAAK,IAAM9N,KAAOwK,OACKnY,IAAjBmY,EAAQxK,IAGU,MAAXA,EAAI,IAAsB,OAARA,GAAyB,UAARA,GAAmBwK,EAAQxE,QAAUA,IAEhE,gBAARhG,EACT8N,EAAgBD,GAAKrD,EAAQuD,YACnB3P,IAAqBA,EAAkB4B,EAAKiN,KACtDa,EAAgB9N,GAAOwK,EAAQxK,KAkBnC,IAAMgO,EA/GR,SACEd,EACAe,GAEA,IAAM7D,EAAM1L,KAUZ,OARkBwO,EAAetB,wBAC/BqC,EACA7D,EAAI/L,WACJ+L,EAAI9L,OAMR,CAgG6B4P,CAAiBhB,EAAgB1C,GAMxD2D,EAAclJ,GAAYsI,EAAoB1I,GAuBlD,OAtBImJ,IACFG,GAAe,IAAMH,GAEnBxD,EAAQkD,YACVS,GAAe,IAAM3D,EAAQkD,WAG/BI,EAEE1C,GAAM6B,KACLjC,GAAYtb,IAAIud,GACb,QACA,aACFkB,EAKAd,IACFS,EAAgBjD,IAAMwC,GAGjBrb,EAAaA,cAACib,EAAoBa,EAC3C,CAwDWM,CAAmCC,EAAwB1W,EAAOkT,EAC1E,CAEDsC,EAAiB1J,YAAcA,EAM/B,IAAI4K,EAAyBnQ,EAAM0M,WAAWuC,GA+D9C,OA1DAkB,EAAuB3B,MAAQI,EAC/BuB,EAAuBnB,eAAiBA,EACxCmB,EAAuB5K,YAAcA,EACrC4K,EAAuBjQ,kBAAoBA,EAI3CiQ,EAAuBd,mBAAqBhB,EACxCtH,GAAYuH,EAAsBe,mBAAoBf,EAAsB3H,mBAC5E,GAEJwJ,EAAuBxJ,kBAAoBA,EAG3CwJ,EAAuBxc,OAAS0a,EAAqBC,EAAsB3a,OAASA,EAEpFvG,OAAO8B,eAAeihB,EAAwB,eAAgB,CAC5D1e,IAAG,WACD,OAAOxD,KAAKmiB,mBACb,EAED7f,aAAI6V,GACFnY,KAAKmiB,oBAAsB/B,EFvQT,SAAU1a,OAAa,IAAiB0c,EAAA,GAAA9gB,EAAA,EAAjBA,EAAiB7B,UAAAC,OAAjB4B,IAAA8gB,EAAiB9gB,EAAA,GAAA7B,UAAA6B,GAC9D,IAAqB,IAAAiD,EAAA,EAAA8d,EAAOD,EAAP7d,WAAAA,IACnB2a,GAAiBxZ,EADF2c,EAAA9d,IACkB,GAGnC,OAAOmB,CACT,CEkQU4c,CAAM,CAAE,EAAEjC,EAAsBzG,aAAczB,GAC9CA,CACL,IAYHrX,EAAYohB,EAAwB,WAAM,MAAA,IAAAzhB,OAAIyhB,EAAuBxJ,kBAA3B,GAEtC4H,GAGF1B,GACEsD,EAH+Bxc,EAK/B,CAEE6a,OAAO,EACPQ,gBAAgB,EAChBzJ,aAAa,EACb8J,oBAAoB,EACpBnP,mBAAmB,EACnByG,mBAAmB,EACnBhT,QAAQ,IAKPwc,CACT,CEpPwB,SAAAK,GAQtBC,EACA9gB,EACAiG,GASA,QATA,IAAAA,IAAAA,EAAoD9G,IAS/Ca,EACH,MAAMW,EAAY,EAAGX,GAIvB,IAAM+gB,EAAmB,SACvBC,OACA,IAAiErhB,EAAA,GAAAC,EAAA,EAAjEA,EAAiE7B,UAAAC,OAAjE4B,IAAAD,EAAiEC,EAAA,GAAA7B,UAAA6B,GAEjE,OAAAkhB,EACE9gB,EACAiG,EACA5E,GAAmChD,WAAA,EAAAE,EAAA,CAAAyiB,GAAkBrhB,GACtD,IAJD,EA6CF,OAjCAohB,EAAiBlC,MAAQ,SAMvBA,GAEA,OAAAgC,GAUEC,EAAsB9gB,EACnBxC,EAAAA,EAAA,CAAA,EAAAyI,GACH,CAAA4Y,MAAOhgB,MAAMX,UAAUa,OAAOkH,EAAQ4Y,MAAOA,GAAOtV,OAAOnM,WAZ7D,EAmBF2jB,EAAiBE,WAAa,SAACC,GAC7B,OAAAL,GAA0DC,EAAsB9gB,EAC3ExC,EAAAA,EAAA,CAAA,EAAAyI,GACAib,GAFL,EAKKH,CACT,CF/CuB,IAAI3Z,IGxG3B,IAAM+Z,GAAa,SACjBnhB,GAEA,OAAA6gB,GAIEpC,GAAuBze,EAJzB,EAMIohB,GAASD,GCPf,IAAK,IAAMhP,MDYXgL,GAAY1W,QAAQ,SAAA4a,GAElBD,GAAOC,GAAcF,GAA8BE,EACrD,GCfkBC,GAEhBF,GAAOjP,IAAOmP,GAAUnP", "x_google_ignoreList": [1, 12, 13, 14, 15, 16, 17, 18, 22, 24]}