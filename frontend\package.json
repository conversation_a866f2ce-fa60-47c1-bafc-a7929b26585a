{"name": "qms-frontend", "version": "1.0.0", "description": "QMS ERP Frontend Application", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "react-hook-form": "^7.45.4", "react-query": "^3.39.3", "styled-components": "^6.0.7", "react-icons": "^4.10.1", "react-toastify": "^9.1.3", "date-fns": "^2.30.0", "lodash": "^4.17.21"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}