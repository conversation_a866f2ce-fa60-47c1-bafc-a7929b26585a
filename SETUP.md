# QMS ERP System - Setup Instructions

## Prerequisites

Before setting up the QMS ERP system, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **MySQL Server** (v8.0 or higher) - [Download here](https://dev.mysql.com/downloads/mysql/)
- **npm** or **yarn** package manager
- **Git** (optional, for version control)

## Database Setup

1. **Create MySQL Database**
   ```sql
   CREATE DATABASE qmsdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Create Database User (Optional but recommended)**
   ```sql
   CREATE USER 'qmsuser'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON qmsdb.* TO 'qmsuser'@'localhost';
   FLUSH PRIVILEGES;
   ```

## Backend Setup

1. **Navigate to Backend Directory**
   ```bash
   cd backend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit the `.env` file with your database credentials:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=your_mysql_password
   DB_NAME=qmsdb
   DB_PORT=3306
   
   JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
   JWT_EXPIRES_IN=24h
   
   PORT=5000
   NODE_ENV=development
   
   FRONTEND_URL=http://localhost:3000
   ```

4. **Run Database Migrations**
   ```bash
   npm run migrate
   ```

5. **Seed Initial Data**
   ```bash
   npm run seed
   ```

6. **Start Backend Server**
   ```bash
   npm run dev
   ```

   The backend server will start on `http://localhost:5000`

## Frontend Setup

1. **Navigate to Frontend Directory** (in a new terminal)
   ```bash
   cd frontend
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit the `.env` file:
   ```env
   REACT_APP_API_URL=http://localhost:5000/api
   REACT_APP_APP_NAME=QMS ERP System
   REACT_APP_VERSION=1.0.0
   ```

4. **Start Frontend Development Server**
   ```bash
   npm start
   ```

   The frontend will start on `http://localhost:3000`

## Default Login Credentials

After seeding the database, you can log in with:

- **Email:** `<EMAIL>`
- **Password:** `admin123`

## Available Scripts

### Backend Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run migrate` - Run database migrations
- `npm run migrate:undo` - Undo last migration
- `npm run seed` - Run database seeders
- `npm test` - Run tests

### Frontend Scripts
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run eject` - Eject from Create React App

## Project Structure

```
qmsus/
├── backend/                 # Node.js + Express backend
│   ├── src/
│   │   ├── config/         # Database and app configuration
│   │   ├── controllers/    # Request handlers
│   │   ├── middleware/     # Auth, validation, error handling
│   │   ├── migrations/     # Database migrations
│   │   ├── models/         # Sequelize models
│   │   ├── routes/         # API routes
│   │   ├── seeders/        # Database seeders
│   │   └── app.js          # Express app entry point
│   ├── package.json
│   └── .env.example
├── frontend/               # React frontend
│   ├── public/
│   ├── src/
│   │   ├── api/           # API service calls
│   │   ├── components/    # Reusable components
│   │   ├── context/       # React contexts
│   │   ├── layouts/       # Layout components
│   │   ├── pages/         # Page components
│   │   ├── styles/        # Global styles
│   │   ├── theme/         # Theme configuration
│   │   └── App.js         # Main app component
│   ├── package.json
│   └── .env.example
└── README.md
```

## Features

### Authentication
- JWT-based login/logout
- Protected routes
- User session management

### User Management
- Create, read, update, delete users
- Assign roles to users
- User search and pagination

### Role Management
- Create and manage roles
- Permission-based access control
- Role assignment to users

### Module Management
- System module configuration
- Hierarchical module structure
- Module activation/deactivation

### UI Features
- Responsive design
- Theme customization (Blue, Green, Purple, Gray)
- Clean, minimal ERP-style interface
- Reusable components (tables, forms, modals)
- Toast notifications
- Loading states and error handling

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - User logout

### Users
- `GET /api/users` - Get users list
- `POST /api/users` - Create user
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Roles
- `GET /api/roles` - Get roles list
- `POST /api/roles` - Create role
- `GET /api/roles/:id` - Get role by ID
- `PUT /api/roles/:id` - Update role
- `DELETE /api/roles/:id` - Delete role

### Modules
- `GET /api/modules` - Get modules list
- `POST /api/modules` - Create module
- `GET /api/modules/:id` - Get module by ID
- `PUT /api/modules/:id` - Update module
- `DELETE /api/modules/:id` - Delete module

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database `qmsdb` exists

2. **Port Already in Use**
   - Change PORT in backend `.env` file
   - Update REACT_APP_API_URL in frontend `.env`

3. **CORS Issues**
   - Verify FRONTEND_URL in backend `.env`
   - Check API URL in frontend `.env`

4. **Migration Errors**
   - Ensure database exists and is accessible
   - Check database user permissions
   - Run migrations one by one if needed

## Production Deployment

### Backend
1. Set `NODE_ENV=production` in `.env`
2. Use a process manager like PM2
3. Set up reverse proxy with Nginx
4. Use environment variables for sensitive data

### Frontend
1. Run `npm run build`
2. Serve the `build` folder with a web server
3. Configure proper routing for SPA

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the console logs for errors
3. Ensure all dependencies are installed correctly
4. Verify database connectivity and permissions
