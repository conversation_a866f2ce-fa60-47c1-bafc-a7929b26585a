# QMS ERP System

A modern ERP-style application built with React frontend and Node.js backend.

## Project Structure

```
qmsus/
├── backend/          # Node.js + Express + Sequelize backend
├── frontend/         # React frontend application
└── README.md         # This file
```

## Technology Stack

### Backend
- Node.js with Express.js
- Sequelize ORM for MySQL
- JWT Authentication
- Input validation with express-validator
- CORS enabled

### Frontend
- React with functional components and hooks
- React Router for navigation
- Context API for state management
- Axios for API calls
- Material-UI or custom components for UI

### Database
- MySQL
- Database name: qmsdb

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MySQL Server
- npm or yarn

### Backend Setup
```bash
cd backend
npm install
npm run dev
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## Features

- JWT-based authentication
- Dashboard with navigation
- Settings management (Modu<PERSON>, <PERSON>s, Users, Masters)
- Responsive design
- Theme customization
- Search and pagination
- Form validations

## API Endpoints

- `POST /api/auth/login` - User login
- `GET /api/modules` - Get modules list
- `POST /api/modules` - Create new module
- `PUT /api/modules/:id` - Update module
- `DELETE /api/modules/:id` - Delete module
- Similar endpoints for roles and users

## Environment Variables

### Backend (.env)
```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=qmsdb
JWT_SECRET=your_jwt_secret
PORT=5000
```

### Frontend (.env)
```
REACT_APP_API_URL=http://localhost:5000/api
```
