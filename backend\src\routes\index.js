const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const userRoutes = require('./users');
const roleRoutes = require('./roles');
const moduleRoutes = require('./modules');

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'QMS API is running',
    timestamp: new Date().toISOString()
  });
});

// Mount routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/roles', roleRoutes);
router.use('/modules', moduleRoutes);

module.exports = router;
