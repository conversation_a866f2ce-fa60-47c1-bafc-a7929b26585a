{"ast": null, "code": "import api from './axios';\nexport const modulesAPI = {\n  getModules: (params = {}) => {\n    return api.get('/modules', {\n      params\n    });\n  },\n  getModuleById: id => {\n    return api.get(`/modules/${id}`);\n  },\n  createModule: moduleData => {\n    return api.post('/modules', moduleData);\n  },\n  updateModule: (id, moduleData) => {\n    return api.put(`/modules/${id}`, moduleData);\n  },\n  deleteModule: id => {\n    return api.delete(`/modules/${id}`);\n  }\n};", "map": {"version": 3, "names": ["api", "modulesAPI", "getModules", "params", "get", "getModuleById", "id", "createModule", "moduleData", "post", "updateModule", "put", "deleteModule", "delete"], "sources": ["D:/Projects/qmsus/frontend/src/api/modules.js"], "sourcesContent": ["import api from './axios';\n\nexport const modulesAPI = {\n  getModules: (params = {}) => {\n    return api.get('/modules', { params });\n  },\n\n  getModuleById: (id) => {\n    return api.get(`/modules/${id}`);\n  },\n\n  createModule: (moduleData) => {\n    return api.post('/modules', moduleData);\n  },\n\n  updateModule: (id, moduleData) => {\n    return api.put(`/modules/${id}`, moduleData);\n  },\n\n  deleteModule: (id) => {\n    return api.delete(`/modules/${id}`);\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,SAAS;AAEzB,OAAO,MAAMC,UAAU,GAAG;EACxBC,UAAU,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC3B,OAAOH,GAAG,CAACI,GAAG,CAAC,UAAU,EAAE;MAAED;IAAO,CAAC,CAAC;EACxC,CAAC;EAEDE,aAAa,EAAGC,EAAE,IAAK;IACrB,OAAON,GAAG,CAACI,GAAG,CAAC,YAAYE,EAAE,EAAE,CAAC;EAClC,CAAC;EAEDC,YAAY,EAAGC,UAAU,IAAK;IAC5B,OAAOR,GAAG,CAACS,IAAI,CAAC,UAAU,EAAED,UAAU,CAAC;EACzC,CAAC;EAEDE,YAAY,EAAEA,CAACJ,EAAE,EAAEE,UAAU,KAAK;IAChC,OAAOR,GAAG,CAACW,GAAG,CAAC,YAAYL,EAAE,EAAE,EAAEE,UAAU,CAAC;EAC9C,CAAC;EAEDI,YAAY,EAAGN,EAAE,IAAK;IACpB,OAAON,GAAG,CAACa,MAAM,CAAC,YAAYP,EAAE,EAAE,CAAC;EACrC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}