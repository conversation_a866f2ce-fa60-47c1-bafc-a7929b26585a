{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\layouts\\\\DashboardLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Sidebar from '../components/layout/Sidebar';\nimport Header from '../components/layout/Header';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n`;\n_c = LayoutContainer;\nconst SidebarContainer = styled.div`\n  width: ${props => props.collapsed ? '60px' : '250px'};\n  transition: width ${props => props.theme.transitions.normal};\n  flex-shrink: 0;\n`;\n_c2 = SidebarContainer;\nconst MainContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n`;\n_c3 = MainContainer;\nconst HeaderContainer = styled.div`\n  height: 64px;\n  flex-shrink: 0;\n`;\n_c4 = HeaderContainer;\nconst ContentContainer = styled.main`\n  flex: 1;\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n`;\n_c5 = ContentContainer;\nconst DashboardLayout = ({\n  children\n}) => {\n  _s();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n  return /*#__PURE__*/_jsxDEV(LayoutContainer, {\n    children: [/*#__PURE__*/_jsxDEV(SidebarContainer, {\n      collapsed: sidebarCollapsed,\n      children: /*#__PURE__*/_jsxDEV(Sidebar, {\n        collapsed: sidebarCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContainer, {\n      children: [/*#__PURE__*/_jsxDEV(HeaderContainer, {\n        children: /*#__PURE__*/_jsxDEV(Header, {\n          onToggleSidebar: toggleSidebar,\n          sidebarCollapsed: sidebarCollapsed\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContentContainer, {\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardLayout, \"RBWGVlQtqnwMoScoawcFwyZtGjQ=\");\n_c6 = DashboardLayout;\nexport default DashboardLayout;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"LayoutContainer\");\n$RefreshReg$(_c2, \"SidebarContainer\");\n$RefreshReg$(_c3, \"MainContainer\");\n$RefreshReg$(_c4, \"HeaderContainer\");\n$RefreshReg$(_c5, \"ContentContainer\");\n$RefreshReg$(_c6, \"DashboardLayout\");", "map": {"version": 3, "names": ["React", "useState", "styled", "Sidebar", "Header", "jsxDEV", "_jsxDEV", "LayoutContainer", "div", "props", "theme", "colors", "backgroundSecondary", "_c", "SidebarContainer", "collapsed", "transitions", "normal", "_c2", "MainContainer", "_c3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c4", "ContentContainer", "main", "spacing", "lg", "_c5", "DashboardLayout", "children", "_s", "sidebarCollapsed", "setSidebarCollapsed", "toggleSidebar", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onToggleSidebar", "_c6", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/layouts/DashboardLayout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport Sidebar from '../components/layout/Sidebar';\nimport Header from '../components/layout/Header';\n\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n`;\n\nconst SidebarContainer = styled.div`\n  width: ${props => props.collapsed ? '60px' : '250px'};\n  transition: width ${props => props.theme.transitions.normal};\n  flex-shrink: 0;\n`;\n\nconst MainContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n`;\n\nconst HeaderContainer = styled.div`\n  height: 64px;\n  flex-shrink: 0;\n`;\n\nconst ContentContainer = styled.main`\n  flex: 1;\n  padding: ${props => props.theme.spacing.lg};\n  overflow-y: auto;\n`;\n\nconst DashboardLayout = ({ children }) => {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  return (\n    <LayoutContainer>\n      <SidebarContainer collapsed={sidebarCollapsed}>\n        <Sidebar collapsed={sidebarCollapsed} />\n      </SidebarContainer>\n      \n      <MainContainer>\n        <HeaderContainer>\n          <Header \n            onToggleSidebar={toggleSidebar}\n            sidebarCollapsed={sidebarCollapsed}\n          />\n        </HeaderContainer>\n        \n        <ContentContainer>\n          {children}\n        </ContentContainer>\n      </MainContainer>\n    </LayoutContainer>\n  );\n};\n\nexport default DashboardLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,GAAGL,MAAM,CAACM,GAAG;AAClC;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,mBAAmB;AACrE,CAAC;AAACC,EAAA,GAJIN,eAAe;AAMrB,MAAMO,gBAAgB,GAAGZ,MAAM,CAACM,GAAG;AACnC,WAAWC,KAAK,IAAIA,KAAK,CAACM,SAAS,GAAG,MAAM,GAAG,OAAO;AACtD,sBAAsBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACM,WAAW,CAACC,MAAM;AAC7D;AACA,CAAC;AAACC,GAAA,GAJIJ,gBAAgB;AAMtB,MAAMK,aAAa,GAAGjB,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GALID,aAAa;AAOnB,MAAME,eAAe,GAAGnB,MAAM,CAACM,GAAG;AAClC;AACA;AACA,CAAC;AAACc,GAAA,GAHID,eAAe;AAKrB,MAAME,gBAAgB,GAAGrB,MAAM,CAACsB,IAAI;AACpC;AACA,aAAaf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACC,EAAE;AAC5C;AACA,CAAC;AAACC,GAAA,GAJIJ,gBAAgB;AAMtB,MAAMK,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMgC,aAAa,GAAGA,CAAA,KAAM;IAC1BD,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,oBACEzB,OAAA,CAACC,eAAe;IAAAsB,QAAA,gBACdvB,OAAA,CAACQ,gBAAgB;MAACC,SAAS,EAAEgB,gBAAiB;MAAAF,QAAA,eAC5CvB,OAAA,CAACH,OAAO;QAACY,SAAS,EAAEgB;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEnB/B,OAAA,CAACa,aAAa;MAAAU,QAAA,gBACZvB,OAAA,CAACe,eAAe;QAAAQ,QAAA,eACdvB,OAAA,CAACF,MAAM;UACLkC,eAAe,EAAEL,aAAc;UAC/BF,gBAAgB,EAAEA;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC,eAElB/B,OAAA,CAACiB,gBAAgB;QAAAM,QAAA,EACdA;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACP,EAAA,CA3BIF,eAAe;AAAAW,GAAA,GAAfX,eAAe;AA6BrB,eAAeA,eAAe;AAAC,IAAAf,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAY,GAAA;AAAAC,YAAA,CAAA3B,EAAA;AAAA2B,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}