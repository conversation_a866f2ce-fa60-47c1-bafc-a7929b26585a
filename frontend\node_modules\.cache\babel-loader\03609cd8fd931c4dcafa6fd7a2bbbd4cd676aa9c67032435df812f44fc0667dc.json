{"ast": null, "code": "import { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { sleep } from './utils';\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\nexport function isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nexport var CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nexport function isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nexport var Retryer = function Retryer(config) {\n  var _this = this;\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!focusManager.isFocused() || !onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n  run();\n};", "map": {"version": 3, "names": ["focusManager", "onlineManager", "sleep", "defaultRetryDelay", "failureCount", "Math", "min", "pow", "isCancelable", "value", "cancel", "CancelledError", "options", "revert", "silent", "isCancelledError", "<PERSON><PERSON><PERSON>", "config", "_this", "cancelRetry", "cancelFn", "continueFn", "promiseResolve", "promiseReject", "abort", "cancelOptions", "continueRetry", "continue", "isPaused", "isResolved", "isTransportCancelable", "promise", "Promise", "outerResolve", "outerReject", "resolve", "onSuccess", "reject", "onError", "pause", "continueResolve", "onPause", "then", "undefined", "onContinue", "run", "promiseOrValue", "fn", "error", "_unused", "catch", "_config$retry", "_config$retryDelay", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "isFocused", "isOnline"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-query/es/core/retryer.js"], "sourcesContent": ["import { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { sleep } from './utils';\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\n\nexport function isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nexport var CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nexport function isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nexport var Retryer = function Retryer(config) {\n  var _this = this;\n\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!focusManager.isFocused() || !onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  run();\n};"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,KAAK,QAAQ,SAAS;AAE/B,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EACvC,OAAOC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,YAAY,CAAC,EAAE,KAAK,CAAC;AAC1D;AAEA,OAAO,SAASI,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,QAAQA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,MAAM,CAAC,KAAK,UAAU;AACtE;AACA,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,OAAO,EAAE;EAC3D,IAAI,CAACC,MAAM,GAAGD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,MAAM;EACvD,IAAI,CAACC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,MAAM;AACzD,CAAC;AACD,OAAO,SAASC,gBAAgBA,CAACN,KAAK,EAAE;EACtC,OAAOA,KAAK,YAAYE,cAAc;AACxC,CAAC,CAAC;;AAEF,OAAO,IAAIK,OAAO,GAAG,SAASA,OAAOA,CAACC,MAAM,EAAE;EAC5C,IAAIC,KAAK,GAAG,IAAI;EAEhB,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,QAAQ;EACZ,IAAIC,UAAU;EACd,IAAIC,cAAc;EAClB,IAAIC,aAAa;EACjB,IAAI,CAACC,KAAK,GAAGP,MAAM,CAACO,KAAK;EAEzB,IAAI,CAACd,MAAM,GAAG,UAAUe,aAAa,EAAE;IACrC,OAAOL,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACK,aAAa,CAAC;EAC5D,CAAC;EAED,IAAI,CAACN,WAAW,GAAG,YAAY;IAC7BA,WAAW,GAAG,IAAI;EACpB,CAAC;EAED,IAAI,CAACO,aAAa,GAAG,YAAY;IAC/BP,WAAW,GAAG,KAAK;EACrB,CAAC;EAED,IAAI,CAACQ,QAAQ,GAAG,YAAY;IAC1B,OAAON,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC;EACnD,CAAC;EAED,IAAI,CAACjB,YAAY,GAAG,CAAC;EACrB,IAAI,CAACwB,QAAQ,GAAG,KAAK;EACrB,IAAI,CAACC,UAAU,GAAG,KAAK;EACvB,IAAI,CAACC,qBAAqB,GAAG,KAAK;EAClC,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,YAAY,EAAEC,WAAW,EAAE;IAC9DZ,cAAc,GAAGW,YAAY;IAC7BV,aAAa,GAAGW,WAAW;EAC7B,CAAC,CAAC;EAEF,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAC1B,KAAK,EAAE;IACpC,IAAI,CAACS,KAAK,CAACW,UAAU,EAAE;MACrBX,KAAK,CAACW,UAAU,GAAG,IAAI;MACvBZ,MAAM,CAACmB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnB,MAAM,CAACmB,SAAS,CAAC3B,KAAK,CAAC;MAC3DY,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC;MAC1CC,cAAc,CAACb,KAAK,CAAC;IACvB;EACF,CAAC;EAED,IAAI4B,MAAM,GAAG,SAASA,MAAMA,CAAC5B,KAAK,EAAE;IAClC,IAAI,CAACS,KAAK,CAACW,UAAU,EAAE;MACrBX,KAAK,CAACW,UAAU,GAAG,IAAI;MACvBZ,MAAM,CAACqB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrB,MAAM,CAACqB,OAAO,CAAC7B,KAAK,CAAC;MACvDY,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC;MAC1CE,aAAa,CAACd,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAI8B,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,OAAO,IAAIP,OAAO,CAAC,UAAUQ,eAAe,EAAE;MAC5CnB,UAAU,GAAGmB,eAAe;MAC5BtB,KAAK,CAACU,QAAQ,GAAG,IAAI;MACrBX,MAAM,CAACwB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxB,MAAM,CAACwB,OAAO,CAAC,CAAC;IACpD,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;MAClBrB,UAAU,GAAGsB,SAAS;MACtBzB,KAAK,CAACU,QAAQ,GAAG,KAAK;MACtBX,MAAM,CAAC2B,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG3B,MAAM,CAAC2B,UAAU,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGH,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAG;IACvB;IACA,IAAI3B,KAAK,CAACW,UAAU,EAAE;MACpB;IACF;IAEA,IAAIiB,cAAc,CAAC,CAAC;;IAEpB,IAAI;MACFA,cAAc,GAAG7B,MAAM,CAAC8B,EAAE,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,cAAc,GAAGd,OAAO,CAACK,MAAM,CAACW,KAAK,CAAC;IACxC,CAAC,CAAC;;IAGF5B,QAAQ,GAAG,SAASA,QAAQA,CAACK,aAAa,EAAE;MAC1C,IAAI,CAACP,KAAK,CAACW,UAAU,EAAE;QACrBQ,MAAM,CAAC,IAAI1B,cAAc,CAACc,aAAa,CAAC,CAAC;QACzCP,KAAK,CAACM,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGN,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC;;QAE9C,IAAIhB,YAAY,CAACsC,cAAc,CAAC,EAAE;UAChC,IAAI;YACFA,cAAc,CAACpC,MAAM,CAAC,CAAC;UACzB,CAAC,CAAC,OAAOuC,OAAO,EAAE,CAAC;QACrB;MACF;IACF,CAAC,CAAC,CAAC;;IAGH/B,KAAK,CAACY,qBAAqB,GAAGtB,YAAY,CAACsC,cAAc,CAAC;IAC1Dd,OAAO,CAACG,OAAO,CAACW,cAAc,CAAC,CAACJ,IAAI,CAACP,OAAO,CAAC,CAACe,KAAK,CAAC,UAAUF,KAAK,EAAE;MACnE,IAAIG,aAAa,EAAEC,kBAAkB;;MAErC;MACA,IAAIlC,KAAK,CAACW,UAAU,EAAE;QACpB;MACF,CAAC,CAAC;;MAGF,IAAIwB,KAAK,GAAG,CAACF,aAAa,GAAGlC,MAAM,CAACoC,KAAK,KAAK,IAAI,GAAGF,aAAa,GAAG,CAAC;MACtE,IAAIG,UAAU,GAAG,CAACF,kBAAkB,GAAGnC,MAAM,CAACqC,UAAU,KAAK,IAAI,GAAGF,kBAAkB,GAAGjD,iBAAiB;MAC1G,IAAIoD,KAAK,GAAG,OAAOD,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACpC,KAAK,CAACd,YAAY,EAAE4C,KAAK,CAAC,GAAGM,UAAU;MACjG,IAAIE,WAAW,GAAGH,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAInC,KAAK,CAACd,YAAY,GAAGiD,KAAK,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAACnC,KAAK,CAACd,YAAY,EAAE4C,KAAK,CAAC;MAE9J,IAAI7B,WAAW,IAAI,CAACqC,WAAW,EAAE;QAC/B;QACAnB,MAAM,CAACW,KAAK,CAAC;QACb;MACF;MAEA9B,KAAK,CAACd,YAAY,EAAE,CAAC,CAAC;;MAEtBa,MAAM,CAACwC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxC,MAAM,CAACwC,MAAM,CAACvC,KAAK,CAACd,YAAY,EAAE4C,KAAK,CAAC,CAAC,CAAC;;MAE3E9C,KAAK,CAACqD,KAAK,CAAC,CAAC;MAAA,CACZb,IAAI,CAAC,YAAY;QAChB,IAAI,CAAC1C,YAAY,CAAC0D,SAAS,CAAC,CAAC,IAAI,CAACzD,aAAa,CAAC0D,QAAQ,CAAC,CAAC,EAAE;UAC1D,OAAOpB,KAAK,CAAC,CAAC;QAChB;MACF,CAAC,CAAC,CAACG,IAAI,CAAC,YAAY;QAClB,IAAIvB,WAAW,EAAE;UACfkB,MAAM,CAACW,KAAK,CAAC;QACf,CAAC,MAAM;UACLH,GAAG,CAAC,CAAC;QACP;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGHA,GAAG,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}