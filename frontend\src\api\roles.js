import api from './axios';

export const rolesAPI = {
  getRoles: (params = {}) => {
    return api.get('/roles', { params });
  },

  getRoleById: (id) => {
    return api.get(`/roles/${id}`);
  },

  createRole: (roleData) => {
    return api.post('/roles', roleData);
  },

  updateRole: (id, roleData) => {
    return api.put(`/roles/${id}`, roleData);
  },

  deleteRole: (id) => {
    return api.delete(`/roles/${id}`);
  }
};
