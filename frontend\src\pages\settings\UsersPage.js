import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { FiPlus, FiEdit, FiTrash2, FiSearch } from 'react-icons/fi';
import { usersAPI } from '../../api/users';
import { rolesAPI } from '../../api/roles';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Table from '../../components/common/Table';
import Modal from '../../components/common/Modal';
import UserForm from '../../components/forms/UserForm';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  flex-wrap: wrap;
`;

const PageTitle = styled.h1`
  font-size: ${props => props.theme.fontSize['2xl']};
  font-weight: ${props => props.theme.fontWeight.bold};
  color: ${props => props.theme.colors.textPrimary};
  margin: 0;
  flex: 1;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 300px;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${props => props.theme.spacing.md};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textMuted};
`;

const SearchInput = styled(Input)`
  input {
    padding-left: ${props => props.theme.spacing.xl};
  }
`;

const TableContainer = styled(Card)`
  overflow: hidden;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const StatusBadge = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.medium};
  
  ${props => props.active ? `
    background-color: ${props.theme.colors.success}20;
    color: ${props.theme.colors.success};
  ` : `
    background-color: ${props.theme.colors.error}20;
    color: ${props.theme.colors.error};
  `}
`;

const RoleBadge = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.primary}20;
  color: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.medium};
  margin-right: ${props => props.theme.spacing.xs};
`;

const UsersPage = () => {
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [currentPage, searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await usersAPI.getUsers({
        page: currentPage,
        limit: 10,
        search: searchTerm
      });
      setUsers(response.data.users);
      setTotalPages(response.data.pagination.totalPages);
    } catch (error) {
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await rolesAPI.getRoles({ limit: 100 });
      setRoles(response.data.roles);
    } catch (error) {
      console.error('Failed to fetch roles:', error);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleAddUser = () => {
    setEditingUser(null);
    setShowModal(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setShowModal(true);
  };

  const handleDeleteUser = async (userId) => {
    try {
      await usersAPI.deleteUser(userId);
      toast.success('User deleted successfully');
      fetchUsers();
      setDeleteConfirm(null);
    } catch (error) {
      toast.error('Failed to delete user');
    }
  };

  const handleFormSubmit = async (userData) => {
    try {
      if (editingUser) {
        await usersAPI.updateUser(editingUser.id, userData);
        toast.success('User updated successfully');
      } else {
        await usersAPI.createUser(userData);
        toast.success('User created successfully');
      }
      setShowModal(false);
      fetchUsers();
    } catch (error) {
      toast.error(editingUser ? 'Failed to update user' : 'Failed to create user');
    }
  };

  const columns = [
    {
      header: 'Name',
      accessor: 'name',
      render: (_, user) => `${user.firstName} ${user.lastName}`
    },
    {
      header: 'Email',
      accessor: 'email'
    },
    {
      header: 'Roles',
      accessor: 'roles',
      render: (roles) => (
        <div>
          {roles?.map(role => (
            <RoleBadge key={role.id}>{role.name}</RoleBadge>
          ))}
        </div>
      )
    },
    {
      header: 'Status',
      accessor: 'isActive',
      render: (isActive) => (
        <StatusBadge active={isActive}>
          {isActive ? 'Active' : 'Inactive'}
        </StatusBadge>
      )
    },
    {
      header: 'Actions',
      accessor: 'actions',
      render: (_, user) => (
        <ActionButtons>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditUser(user)}
          >
            <FiEdit size={16} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeleteConfirm(user)}
          >
            <FiTrash2 size={16} />
          </Button>
        </ActionButtons>
      )
    }
  ];

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>User Management</PageTitle>
        <HeaderActions>
          <SearchContainer>
            <SearchIcon>
              <FiSearch size={16} />
            </SearchIcon>
            <SearchInput
              placeholder="Search users..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </SearchContainer>
          <Button
            variant="primary"
            onClick={handleAddUser}
          >
            <FiPlus size={16} />
            Add User
          </Button>
        </HeaderActions>
      </PageHeader>

      <TableContainer>
        <Table
          columns={columns}
          data={users}
          loading={loading}
          emptyMessage="No users found"
        />
      </TableContainer>

      {showModal && (
        <Modal
          title={editingUser ? 'Edit User' : 'Add New User'}
          onClose={() => setShowModal(false)}
        >
          <UserForm
            user={editingUser}
            roles={roles}
            onSubmit={handleFormSubmit}
            onCancel={() => setShowModal(false)}
          />
        </Modal>
      )}

      {deleteConfirm && (
        <Modal
          title="Confirm Delete"
          onClose={() => setDeleteConfirm(null)}
        >
          <div style={{ padding: '1rem 0' }}>
            <p>Are you sure you want to delete user "{deleteConfirm.firstName} {deleteConfirm.lastName}"?</p>
            <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem', justifyContent: 'flex-end' }}>
              <Button variant="secondary" onClick={() => setDeleteConfirm(null)}>
                Cancel
              </Button>
              <Button variant="danger" onClick={() => handleDeleteUser(deleteConfirm.id)}>
                Delete
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </PageContainer>
  );
};

export default UsersPage;
