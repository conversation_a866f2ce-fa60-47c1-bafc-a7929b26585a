const express = require('express');
const router = express.Router();
const roleController = require('../controllers/roleController');
const { authenticateToken, authorize } = require('../middleware/auth');
const { validateRole, validateId, validatePagination } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// GET /api/roles - Get all roles with pagination and search
router.get('/', validatePagination, roleController.getRoles);

// GET /api/roles/:id - Get role by ID
router.get('/:id', validateId, roleController.getRoleById);

// POST /api/roles - Create new role (requires admin permission)
router.post('/', 
  authorize(['roles.create']), 
  validateRole, 
  roleController.createRole
);

// PUT /api/roles/:id - Update role (requires admin permission)
router.put('/:id', 
  authorize(['roles.update']), 
  validateId, 
  validateRole, 
  roleController.updateRole
);

// DELETE /api/roles/:id - Delete role (requires admin permission)
router.delete('/:id', 
  authorize(['roles.delete']), 
  validateId, 
  roleController.deleteRole
);

module.exports = router;
