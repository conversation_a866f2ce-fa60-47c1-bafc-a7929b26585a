import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';
import Card from '../components/common/Card';
import { FiUsers, FiShield, FiGrid, FiActivity } from 'react-icons/fi';

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const WelcomeSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const WelcomeTitle = styled.h1`
  font-size: ${props => props.theme.fontSize['2xl']};
  font-weight: ${props => props.theme.fontWeight.bold};
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const WelcomeSubtitle = styled.p`
  font-size: ${props => props.theme.fontSize.lg};
  color: ${props => props.theme.colors.textSecondary};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const StatCard = styled(Card)`
  cursor: pointer;
  transition: all ${props => props.theme.transitions.fast};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.lg};
  }
`;

const StatCardContent = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};
  padding: ${props => props.theme.spacing.lg};
`;

const StatIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: ${props => props.theme.borderRadius.lg};
  background-color: ${props => props.color}20;
  color: ${props => props.color};
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`;

const StatInfo = styled.div`
  flex: 1;
`;

const StatValue = styled.div`
  font-size: ${props => props.theme.fontSize['2xl']};
  font-weight: ${props => props.theme.fontWeight.bold};
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const StatLabel = styled.div`
  font-size: ${props => props.theme.fontSize.sm};
  color: ${props => props.theme.colors.textSecondary};
`;

const QuickActionsSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSize.xl};
  font-weight: ${props => props.theme.fontWeight.semibold};
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.lg};
`;

const QuickActionCard = styled(Card)`
  cursor: pointer;
  transition: all ${props => props.theme.transitions.fast};

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const QuickActionContent = styled.div`
  padding: ${props => props.theme.spacing.lg};
`;

const QuickActionTitle = styled.h3`
  font-size: ${props => props.theme.fontSize.lg};
  font-weight: ${props => props.theme.fontWeight.semibold};
  color: ${props => props.theme.colors.textPrimary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const QuickActionDescription = styled.p`
  font-size: ${props => props.theme.fontSize.sm};
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.5;
`;

const Dashboard = () => {
  const { user } = useAuth();

  const stats = [
    {
      icon: FiUsers,
      value: '24',
      label: 'Total Users',
      color: '#3b82f6'
    },
    {
      icon: FiShield,
      value: '3',
      label: 'Active Roles',
      color: '#10b981'
    },
    {
      icon: FiGrid,
      value: '5',
      label: 'System Modules',
      color: '#f59e0b'
    },
    {
      icon: FiActivity,
      value: '98%',
      label: 'System Health',
      color: '#ef4444'
    }
  ];

  const quickActions = [
    {
      title: 'User Management',
      description: 'Add, edit, or manage user accounts and their permissions.',
      path: '/settings/users'
    },
    {
      title: 'Role Management',
      description: 'Configure roles and assign permissions to control access.',
      path: '/settings/roles'
    },
    {
      title: 'Module Management',
      description: 'Manage system modules and their configurations.',
      path: '/settings/modules'
    },
    {
      title: 'System Settings',
      description: 'Configure global system settings and preferences.',
      path: '/settings'
    }
  ];

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <DashboardContainer>
      <WelcomeSection>
        <WelcomeTitle>
          {getGreeting()}, {user?.firstName}!
        </WelcomeTitle>
        <WelcomeSubtitle>
          Welcome to your QMS ERP dashboard. Here's an overview of your system.
        </WelcomeSubtitle>
      </WelcomeSection>

      <StatsGrid>
        {stats.map((stat, index) => (
          <StatCard key={index} hover>
            <StatCardContent>
              <StatIcon color={stat.color}>
                <stat.icon size={24} />
              </StatIcon>
              <StatInfo>
                <StatValue>{stat.value}</StatValue>
                <StatLabel>{stat.label}</StatLabel>
              </StatInfo>
            </StatCardContent>
          </StatCard>
        ))}
      </StatsGrid>

      <QuickActionsSection>
        <SectionTitle>Quick Actions</SectionTitle>
        <QuickActionsGrid>
          {quickActions.map((action, index) => (
            <QuickActionCard key={index} hover>
              <QuickActionContent>
                <QuickActionTitle>{action.title}</QuickActionTitle>
                <QuickActionDescription>{action.description}</QuickActionDescription>
              </QuickActionContent>
            </QuickActionCard>
          ))}
        </QuickActionsGrid>
      </QuickActionsSection>
    </DashboardContainer>
  );
};

export default Dashboard;
