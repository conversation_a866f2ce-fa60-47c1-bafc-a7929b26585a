{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\components\\\\common\\\\Table.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TableWrapper = styled.div`\n  overflow-x: auto;\n  border-radius: ${props => props.theme.borderRadius.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n`;\n_c = TableWrapper;\nconst StyledTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  background-color: ${props => props.theme.colors.background};\n`;\n_c2 = StyledTable;\nconst TableHead = styled.thead`\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n`;\n_c3 = TableHead;\nconst TableBody = styled.tbody``;\n_c4 = TableBody;\nconst TableRow = styled.tr`\n  &:not(:last-child) {\n    border-bottom: 1px solid ${props => props.theme.colors.border};\n  }\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundTertiary};\n  }\n`;\n_c5 = TableRow;\nconst TableHeader = styled.th`\n  padding: ${props => props.theme.spacing.md};\n  text-align: left;\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  font-size: ${props => props.theme.fontSize.sm};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n`;\n_c6 = TableHeader;\nconst TableCell = styled.td`\n  padding: ${props => props.theme.spacing.md};\n  color: ${props => props.theme.colors.textPrimary};\n  font-size: ${props => props.theme.fontSize.sm};\n  vertical-align: middle;\n`;\n_c7 = TableCell;\nconst EmptyState = styled.div`\n  padding: ${props => props.theme.spacing.xxl};\n  text-align: center;\n  color: ${props => props.theme.colors.textMuted};\n`;\n_c8 = EmptyState;\nconst LoadingState = styled.div`\n  padding: ${props => props.theme.spacing.xxl};\n  text-align: center;\n  color: ${props => props.theme.colors.textMuted};\n`;\n_c9 = LoadingState;\nconst Table = ({\n  columns = [],\n  data = [],\n  loading = false,\n  emptyMessage = \"No data available\",\n  ...props\n}) => {\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(TableWrapper, {\n      children: /*#__PURE__*/_jsxDEV(LoadingState, {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  }\n  if (data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(TableWrapper, {\n      children: /*#__PURE__*/_jsxDEV(EmptyState, {\n        children: emptyMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(TableWrapper, {\n    ...props,\n    children: /*#__PURE__*/_jsxDEV(StyledTable, {\n      children: [/*#__PURE__*/_jsxDEV(TableHead, {\n        children: /*#__PURE__*/_jsxDEV(TableRow, {\n          children: columns.map((column, index) => /*#__PURE__*/_jsxDEV(TableHeader, {\n            children: column.header\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n        children: data.map((row, rowIndex) => /*#__PURE__*/_jsxDEV(TableRow, {\n          children: columns.map((column, colIndex) => /*#__PURE__*/_jsxDEV(TableCell, {\n            children: column.render ? column.render(row[column.accessor], row, rowIndex) : row[column.accessor]\n          }, colIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 17\n          }, this))\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_c0 = Table;\nTable.Wrapper = TableWrapper;\nTable.Table = StyledTable;\nTable.Head = TableHead;\nTable.Body = TableBody;\nTable.Row = TableRow;\nTable.Header = TableHeader;\nTable.Cell = TableCell;\nexport default Table;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"TableWrapper\");\n$RefreshReg$(_c2, \"StyledTable\");\n$RefreshReg$(_c3, \"TableHead\");\n$RefreshReg$(_c4, \"TableBody\");\n$RefreshReg$(_c5, \"TableRow\");\n$RefreshReg$(_c6, \"TableHeader\");\n$RefreshReg$(_c7, \"TableCell\");\n$RefreshReg$(_c8, \"EmptyState\");\n$RefreshReg$(_c9, \"LoadingState\");\n$RefreshReg$(_c0, \"Table\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "TableWrapper", "div", "props", "theme", "borderRadius", "lg", "colors", "border", "_c", "StyledTable", "table", "background", "_c2", "TableHead", "thead", "backgroundSecondary", "_c3", "TableBody", "tbody", "_c4", "TableRow", "tr", "backgroundTertiary", "_c5", "TableHeader", "th", "spacing", "md", "fontWeight", "semibold", "textPrimary", "fontSize", "sm", "_c6", "TableCell", "td", "_c7", "EmptyState", "xxl", "textMuted", "_c8", "LoadingState", "_c9", "Table", "columns", "data", "loading", "emptyMessage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "column", "index", "header", "row", "rowIndex", "colIndex", "render", "accessor", "_c0", "Wrapper", "Head", "Body", "Row", "Header", "Cell", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/components/common/Table.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst TableWrapper = styled.div`\n  overflow-x: auto;\n  border-radius: ${props => props.theme.borderRadius.lg};\n  border: 1px solid ${props => props.theme.colors.border};\n`;\n\nconst StyledTable = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  background-color: ${props => props.theme.colors.background};\n`;\n\nconst TableHead = styled.thead`\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n`;\n\nconst TableBody = styled.tbody``;\n\nconst TableRow = styled.tr`\n  &:not(:last-child) {\n    border-bottom: 1px solid ${props => props.theme.colors.border};\n  }\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundTertiary};\n  }\n`;\n\nconst TableHeader = styled.th`\n  padding: ${props => props.theme.spacing.md};\n  text-align: left;\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  font-size: ${props => props.theme.fontSize.sm};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n`;\n\nconst TableCell = styled.td`\n  padding: ${props => props.theme.spacing.md};\n  color: ${props => props.theme.colors.textPrimary};\n  font-size: ${props => props.theme.fontSize.sm};\n  vertical-align: middle;\n`;\n\nconst EmptyState = styled.div`\n  padding: ${props => props.theme.spacing.xxl};\n  text-align: center;\n  color: ${props => props.theme.colors.textMuted};\n`;\n\nconst LoadingState = styled.div`\n  padding: ${props => props.theme.spacing.xxl};\n  text-align: center;\n  color: ${props => props.theme.colors.textMuted};\n`;\n\nconst Table = ({ \n  columns = [], \n  data = [], \n  loading = false, \n  emptyMessage = \"No data available\",\n  ...props \n}) => {\n  if (loading) {\n    return (\n      <TableWrapper>\n        <LoadingState>Loading...</LoadingState>\n      </TableWrapper>\n    );\n  }\n\n  if (data.length === 0) {\n    return (\n      <TableWrapper>\n        <EmptyState>{emptyMessage}</EmptyState>\n      </TableWrapper>\n    );\n  }\n\n  return (\n    <TableWrapper {...props}>\n      <StyledTable>\n        <TableHead>\n          <TableRow>\n            {columns.map((column, index) => (\n              <TableHeader key={index}>\n                {column.header}\n              </TableHeader>\n            ))}\n          </TableRow>\n        </TableHead>\n        <TableBody>\n          {data.map((row, rowIndex) => (\n            <TableRow key={rowIndex}>\n              {columns.map((column, colIndex) => (\n                <TableCell key={colIndex}>\n                  {column.render \n                    ? column.render(row[column.accessor], row, rowIndex)\n                    : row[column.accessor]\n                  }\n                </TableCell>\n              ))}\n            </TableRow>\n          ))}\n        </TableBody>\n      </StyledTable>\n    </TableWrapper>\n  );\n};\n\nTable.Wrapper = TableWrapper;\nTable.Table = StyledTable;\nTable.Head = TableHead;\nTable.Body = TableBody;\nTable.Row = TableRow;\nTable.Header = TableHeader;\nTable.Cell = TableCell;\n\nexport default Table;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGH,MAAM,CAACI,GAAG;AAC/B;AACA,mBAAmBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,YAAY,CAACC,EAAE;AACvD,sBAAsBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACC,MAAM;AACxD,CAAC;AAACC,EAAA,GAJIR,YAAY;AAMlB,MAAMS,WAAW,GAAGZ,MAAM,CAACa,KAAK;AAChC;AACA;AACA,sBAAsBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACK,UAAU;AAC5D,CAAC;AAACC,GAAA,GAJIH,WAAW;AAMjB,MAAMI,SAAS,GAAGhB,MAAM,CAACiB,KAAK;AAC9B,sBAAsBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACS,mBAAmB;AACrE,CAAC;AAACC,GAAA,GAFIH,SAAS;AAIf,MAAMI,SAAS,GAAGpB,MAAM,CAACqB,KAAK,EAAE;AAACC,GAAA,GAA3BF,SAAS;AAEf,MAAMG,QAAQ,GAAGvB,MAAM,CAACwB,EAAE;AAC1B;AACA,+BAA+BnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACC,MAAM;AACjE;AACA;AACA;AACA,wBAAwBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACgB,kBAAkB;AACtE;AACA,CAAC;AAACC,GAAA,GARIH,QAAQ;AAUd,MAAMI,WAAW,GAAG3B,MAAM,CAAC4B,EAAE;AAC7B,aAAavB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuB,OAAO,CAACC,EAAE;AAC5C;AACA,iBAAiBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACyB,UAAU,CAACC,QAAQ;AACzD,WAAW3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACwB,WAAW;AAClD,eAAe5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC4B,QAAQ,CAACC,EAAE;AAC/C,6BAA6B9B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACC,MAAM;AAC/D,CAAC;AAAC0B,GAAA,GAPIT,WAAW;AASjB,MAAMU,SAAS,GAAGrC,MAAM,CAACsC,EAAE;AAC3B,aAAajC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuB,OAAO,CAACC,EAAE;AAC5C,WAAWzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACwB,WAAW;AAClD,eAAe5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC4B,QAAQ,CAACC,EAAE;AAC/C;AACA,CAAC;AAACI,GAAA,GALIF,SAAS;AAOf,MAAMG,UAAU,GAAGxC,MAAM,CAACI,GAAG;AAC7B,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuB,OAAO,CAACY,GAAG;AAC7C;AACA,WAAWpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACiC,SAAS;AAChD,CAAC;AAACC,GAAA,GAJIH,UAAU;AAMhB,MAAMI,YAAY,GAAG5C,MAAM,CAACI,GAAG;AAC/B,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuB,OAAO,CAACY,GAAG;AAC7C;AACA,WAAWpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACG,MAAM,CAACiC,SAAS;AAChD,CAAC;AAACG,GAAA,GAJID,YAAY;AAMlB,MAAME,KAAK,GAAGA,CAAC;EACbC,OAAO,GAAG,EAAE;EACZC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG,KAAK;EACfC,YAAY,GAAG,mBAAmB;EAClC,GAAG7C;AACL,CAAC,KAAK;EACJ,IAAI4C,OAAO,EAAE;IACX,oBACE/C,OAAA,CAACC,YAAY;MAAAgD,QAAA,eACXjD,OAAA,CAAC0C,YAAY;QAAAO,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEnB;EAEA,IAAIP,IAAI,CAACQ,MAAM,KAAK,CAAC,EAAE;IACrB,oBACEtD,OAAA,CAACC,YAAY;MAAAgD,QAAA,eACXjD,OAAA,CAACsC,UAAU;QAAAW,QAAA,EAAED;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEnB;EAEA,oBACErD,OAAA,CAACC,YAAY;IAAA,GAAKE,KAAK;IAAA8C,QAAA,eACrBjD,OAAA,CAACU,WAAW;MAAAuC,QAAA,gBACVjD,OAAA,CAACc,SAAS;QAAAmC,QAAA,eACRjD,OAAA,CAACqB,QAAQ;UAAA4B,QAAA,EACNJ,OAAO,CAACU,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBzD,OAAA,CAACyB,WAAW;YAAAwB,QAAA,EACTO,MAAM,CAACE;UAAM,GADED,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACZrD,OAAA,CAACkB,SAAS;QAAA+B,QAAA,EACPH,IAAI,CAACS,GAAG,CAAC,CAACI,GAAG,EAAEC,QAAQ,kBACtB5D,OAAA,CAACqB,QAAQ;UAAA4B,QAAA,EACNJ,OAAO,CAACU,GAAG,CAAC,CAACC,MAAM,EAAEK,QAAQ,kBAC5B7D,OAAA,CAACmC,SAAS;YAAAc,QAAA,EACPO,MAAM,CAACM,MAAM,GACVN,MAAM,CAACM,MAAM,CAACH,GAAG,CAACH,MAAM,CAACO,QAAQ,CAAC,EAAEJ,GAAG,EAAEC,QAAQ,CAAC,GAClDD,GAAG,CAACH,MAAM,CAACO,QAAQ;UAAC,GAHVF,QAAQ;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKb,CACZ;QAAC,GARWO,QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASb,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB,CAAC;AAACW,GAAA,GApDIpB,KAAK;AAsDXA,KAAK,CAACqB,OAAO,GAAGhE,YAAY;AAC5B2C,KAAK,CAACA,KAAK,GAAGlC,WAAW;AACzBkC,KAAK,CAACsB,IAAI,GAAGpD,SAAS;AACtB8B,KAAK,CAACuB,IAAI,GAAGjD,SAAS;AACtB0B,KAAK,CAACwB,GAAG,GAAG/C,QAAQ;AACpBuB,KAAK,CAACyB,MAAM,GAAG5C,WAAW;AAC1BmB,KAAK,CAAC0B,IAAI,GAAGnC,SAAS;AAEtB,eAAeS,KAAK;AAAC,IAAAnC,EAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAqB,GAAA;AAAAO,YAAA,CAAA9D,EAAA;AAAA8D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAP,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}