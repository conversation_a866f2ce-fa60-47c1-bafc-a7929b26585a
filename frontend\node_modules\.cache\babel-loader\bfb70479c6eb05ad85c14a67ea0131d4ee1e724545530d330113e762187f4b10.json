{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, Outlet } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n`;\n_c = LoadingContainer;\nconst LoadingSpinner = styled.div`\n  width: 40px;\n  height: 40px;\n  border: 4px solid ${props => props.theme.colors.border};\n  border-top: 4px solid ${props => props.theme.colors.primary};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n_c2 = LoadingSpinner;\nconst ProtectedRoute = () => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(LoadingContainer, {\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this);\n  }\n  return isAuthenticated ? /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 28\n  }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 41\n  }, this);\n};\n_s(ProtectedRoute, \"yb/FJYAIXt7wZoU4a4YvGQ4Nlsc=\", false, function () {\n  return [useAuth];\n});\n_c3 = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"LoadingContainer\");\n$RefreshReg$(_c2, \"LoadingSpinner\");\n$RefreshReg$(_c3, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "Outlet", "styled", "useAuth", "jsxDEV", "_jsxDEV", "LoadingContainer", "div", "props", "theme", "colors", "backgroundSecondary", "_c", "LoadingSpinner", "border", "primary", "_c2", "ProtectedRoute", "_s", "isAuthenticated", "isLoading", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c3", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, Outlet } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../../context/AuthContext';\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 40px;\n  height: 40px;\n  border: 4px solid ${props => props.theme.colors.border};\n  border-top: 4px solid ${props => props.theme.colors.primary};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst ProtectedRoute = () => {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return (\n      <LoadingContainer>\n        <LoadingSpinner />\n      </LoadingContainer>\n    );\n  }\n\n  return isAuthenticated ? <Outlet /> : <Navigate to=\"/login\" replace />;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,MAAM,QAAQ,kBAAkB;AACnD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,gBAAgB,GAAGJ,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,mBAAmB;AACrE,CAAC;AAACC,EAAA,GANIN,gBAAgB;AAQtB,MAAMO,cAAc,GAAGX,MAAM,CAACK,GAAG;AACjC;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,MAAM;AACxD,0BAA0BN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,OAAO;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,cAAc;AAcpB,MAAMI,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAEhD,IAAIiB,SAAS,EAAE;IACb,oBACEf,OAAA,CAACC,gBAAgB;MAAAe,QAAA,eACfhB,OAAA,CAACQ,cAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEvB;EAEA,OAAON,eAAe,gBAAGd,OAAA,CAACJ,MAAM;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACL,QAAQ;IAAC0B,EAAE,EAAC,QAAQ;IAACC,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACxE,CAAC;AAACP,EAAA,CAZID,cAAc;EAAA,QACqBd,OAAO;AAAA;AAAAyB,GAAA,GAD1CX,cAAc;AAcpB,eAAeA,cAAc;AAAC,IAAAL,EAAA,EAAAI,GAAA,EAAAY,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}