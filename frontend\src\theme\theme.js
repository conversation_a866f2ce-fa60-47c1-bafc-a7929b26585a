export const lightTheme = {
  colors: {
    primary: '#2563eb', // Blue
    primaryHover: '#1d4ed8',
    secondary: '#64748b',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    
    // Background colors
    background: '#ffffff',
    backgroundSecondary: '#f8fafc',
    backgroundTertiary: '#f1f5f9',
    
    // Text colors
    textPrimary: '#1e293b',
    textSecondary: '#64748b',
    textMuted: '#94a3b8',
    textInverse: '#ffffff',
    
    // Border colors
    border: '#e2e8f0',
    borderLight: '#f1f5f9',
    borderDark: '#cbd5e1',
    
    // Sidebar
    sidebarBg: '#1e293b',
    sidebarText: '#cbd5e1',
    sidebarTextActive: '#ffffff',
    sidebarHover: '#334155',
    
    // Header
    headerBg: '#ffffff',
    headerBorder: '#e2e8f0',
    
    // Cards
    cardBg: '#ffffff',
    cardBorder: '#e2e8f0',
    cardShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
  },
  
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    xxl: '3rem'
  },
  
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem'
  },
  
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem'
  },
  
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  },
  
  transitions: {
    fast: '150ms ease-in-out',
    normal: '300ms ease-in-out',
    slow: '500ms ease-in-out'
  }
};

export const themeVariants = {
  blue: {
    ...lightTheme,
    colors: {
      ...lightTheme.colors,
      primary: '#2563eb',
      primaryHover: '#1d4ed8'
    }
  },
  green: {
    ...lightTheme,
    colors: {
      ...lightTheme.colors,
      primary: '#059669',
      primaryHover: '#047857'
    }
  },
  purple: {
    ...lightTheme,
    colors: {
      ...lightTheme.colors,
      primary: '#7c3aed',
      primaryHover: '#6d28d9'
    }
  },
  gray: {
    ...lightTheme,
    colors: {
      ...lightTheme.colors,
      primary: '#374151',
      primaryHover: '#1f2937'
    }
  }
};

export const defaultTheme = themeVariants.blue;
