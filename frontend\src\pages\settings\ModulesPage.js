import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { FiPlus, FiEdit, FiTrash2, FiSearch } from 'react-icons/fi';
import { modulesAPI } from '../../api/modules';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Table from '../../components/common/Table';
import Modal from '../../components/common/Modal';
import ModuleForm from '../../components/forms/ModuleForm';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  flex-wrap: wrap;
`;

const PageTitle = styled.h1`
  font-size: ${props => props.theme.fontSize['2xl']};
  font-weight: ${props => props.theme.fontWeight.bold};
  color: ${props => props.theme.colors.textPrimary};
  margin: 0;
  flex: 1;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 300px;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${props => props.theme.spacing.md};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textMuted};
`;

const SearchInput = styled(Input)`
  input {
    padding-left: ${props => props.theme.spacing.xl};
  }
`;

const TableContainer = styled(Card)`
  overflow: hidden;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const StatusBadge = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.medium};
  
  ${props => props.active ? `
    background-color: ${props.theme.colors.success}20;
    color: ${props.theme.colors.success};
  ` : `
    background-color: ${props.theme.colors.error}20;
    color: ${props.theme.colors.error};
  `}
`;

const CodeBadge = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.backgroundSecondary};
  color: ${props => props.theme.colors.textSecondary};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.medium};
  font-family: monospace;
`;

const ModulesPage = () => {
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [editingModule, setEditingModule] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    fetchModules();
  }, [currentPage, searchTerm]);

  const fetchModules = async () => {
    try {
      setLoading(true);
      const response = await modulesAPI.getModules({
        page: currentPage,
        limit: 10,
        search: searchTerm
      });
      setModules(response.data.modules);
      setTotalPages(response.data.pagination.totalPages);
    } catch (error) {
      toast.error('Failed to fetch modules');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleAddModule = () => {
    setEditingModule(null);
    setShowModal(true);
  };

  const handleEditModule = (module) => {
    setEditingModule(module);
    setShowModal(true);
  };

  const handleDeleteModule = async (moduleId) => {
    try {
      await modulesAPI.deleteModule(moduleId);
      toast.success('Module deleted successfully');
      fetchModules();
      setDeleteConfirm(null);
    } catch (error) {
      toast.error('Failed to delete module');
    }
  };

  const handleFormSubmit = async (moduleData) => {
    try {
      if (editingModule) {
        await modulesAPI.updateModule(editingModule.id, moduleData);
        toast.success('Module updated successfully');
      } else {
        await modulesAPI.createModule(moduleData);
        toast.success('Module created successfully');
      }
      setShowModal(false);
      fetchModules();
    } catch (error) {
      toast.error(editingModule ? 'Failed to update module' : 'Failed to create module');
    }
  };

  const columns = [
    {
      header: 'Name',
      accessor: 'name'
    },
    {
      header: 'Code',
      accessor: 'code',
      render: (code) => <CodeBadge>{code}</CodeBadge>
    },
    {
      header: 'Description',
      accessor: 'description',
      render: (description) => description || '-'
    },
    {
      header: 'Parent',
      accessor: 'parent',
      render: (parent) => parent?.name || '-'
    },
    {
      header: 'Order',
      accessor: 'sortOrder'
    },
    {
      header: 'Status',
      accessor: 'isActive',
      render: (isActive) => (
        <StatusBadge active={isActive}>
          {isActive ? 'Active' : 'Inactive'}
        </StatusBadge>
      )
    },
    {
      header: 'Actions',
      accessor: 'actions',
      render: (_, module) => (
        <ActionButtons>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditModule(module)}
          >
            <FiEdit size={16} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeleteConfirm(module)}
          >
            <FiTrash2 size={16} />
          </Button>
        </ActionButtons>
      )
    }
  ];

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Module Management</PageTitle>
        <HeaderActions>
          <SearchContainer>
            <SearchIcon>
              <FiSearch size={16} />
            </SearchIcon>
            <SearchInput
              placeholder="Search modules..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </SearchContainer>
          <Button
            variant="primary"
            onClick={handleAddModule}
          >
            <FiPlus size={16} />
            Add Module
          </Button>
        </HeaderActions>
      </PageHeader>

      <TableContainer>
        <Table
          columns={columns}
          data={modules}
          loading={loading}
          emptyMessage="No modules found"
        />
      </TableContainer>

      {showModal && (
        <Modal
          title={editingModule ? 'Edit Module' : 'Add New Module'}
          onClose={() => setShowModal(false)}
        >
          <ModuleForm
            module={editingModule}
            modules={modules}
            onSubmit={handleFormSubmit}
            onCancel={() => setShowModal(false)}
          />
        </Modal>
      )}

      {deleteConfirm && (
        <Modal
          title="Confirm Delete"
          onClose={() => setDeleteConfirm(null)}
        >
          <div style={{ padding: '1rem 0' }}>
            <p>Are you sure you want to delete module "{deleteConfirm.name}"?</p>
            <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem', justifyContent: 'flex-end' }}>
              <Button variant="secondary" onClick={() => setDeleteConfirm(null)}>
                Cancel
              </Button>
              <Button variant="danger" onClick={() => handleDeleteModule(deleteConfirm.id)}>
                Delete
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </PageContainer>
  );
};

export default ModulesPage;
