require('dotenv').config();
const bcrypt = require('bcryptjs');
const db = require('./src/models');

const createAdminUser = async () => {
  try {
    // Ensure database connection
    await db.sequelize.authenticate();
    console.log('Database connected successfully.');

    // Sync tables (create if they don't exist)
    await db.sequelize.sync({ force: false });
    console.log('Tables synchronized.');

    // Check if admin user already exists
    const existingAdmin = await db.User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('Admin user already exists!');
      process.exit(0);
    }

    // Create Super Admin role
    const [superAdminRole] = await db.Role.findOrCreate({
      where: { name: 'Super Admin' },
      defaults: {
        description: 'Full system access',
        permissions: {
          'users.create': true,
          'users.read': true,
          'users.update': true,
          'users.delete': true,
          'roles.create': true,
          'roles.read': true,
          'roles.update': true,
          'roles.delete': true,
          'modules.create': true,
          'modules.read': true,
          'modules.update': true,
          'modules.delete': true
        },
        isActive: true
      }
    });

    // Create admin user
    const adminUser = await db.User.create({
      firstName: 'System',
      lastName: 'Administrator',
      email: '<EMAIL>',
      password: 'admin123', // Will be hashed by the model hook
      isActive: true
    });

    // Assign Super Admin role to admin user
    await adminUser.addRole(superAdminRole);

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('');
    console.log('You can now start the server with: npm run dev');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    process.exit(0);
  }
};

createAdminUser();
