{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\components\\\\common\\\\Card.js\";\nimport React from 'react';\nimport styled, { css } from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StyledCard = styled.div`\n  background-color: ${props => props.theme.colors.cardBg};\n  border: 1px solid ${props => props.theme.colors.cardBorder};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.colors.cardShadow};\n  overflow: hidden;\n\n  ${props => props.padding && css`\n    padding: ${props.theme.spacing.lg};\n  `}\n\n  ${props => props.hover && css`\n    transition: all ${props.theme.transitions.fast};\n    cursor: pointer;\n\n    &:hover {\n      box-shadow: ${props.theme.shadows.md};\n      transform: translateY(-1px);\n    }\n  `}\n`;\n_c = StyledCard;\nconst CardHeader = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n  \n  ${props => props.noBorder && css`\n    border-bottom: none;\n  `}\n`;\nconst CardTitle = styled.h3`\n  margin: 0;\n  font-size: ${props => props.theme.fontSize.lg};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n`;\nconst CardSubtitle = styled.p`\n  margin: ${props => props.theme.spacing.xs} 0 0 0;\n  font-size: ${props => props.theme.fontSize.sm};\n  color: ${props => props.theme.colors.textSecondary};\n`;\nconst CardBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n`;\nconst CardFooter = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-top: 1px solid ${props => props.theme.colors.border};\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n\n  ${props => props.noBorder && css`\n    border-top: none;\n    background-color: transparent;\n  `}\n`;\nconst Card = ({\n  children,\n  padding = false,\n  hover = false,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(StyledCard, {\n    padding: padding,\n    hover: hover,\n    className: className,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_c2 = Card;\nCard.Header = CardHeader;\nCard.Title = CardTitle;\nCard.Subtitle = CardSubtitle;\nCard.Body = CardBody;\nCard.Footer = CardFooter;\nexport default Card;\nvar _c, _c2;\n$RefreshReg$(_c, \"StyledCard\");\n$RefreshReg$(_c2, \"Card\");", "map": {"version": 3, "names": ["React", "styled", "css", "jsxDEV", "_jsxDEV", "StyledCard", "div", "props", "theme", "colors", "cardBg", "cardBorder", "borderRadius", "lg", "cardShadow", "padding", "spacing", "hover", "transitions", "fast", "shadows", "md", "_c", "<PERSON><PERSON><PERSON><PERSON>", "border", "noBorder", "CardTitle", "h3", "fontSize", "fontWeight", "semibold", "textPrimary", "CardSubtitle", "p", "xs", "sm", "textSecondary", "CardBody", "<PERSON><PERSON><PERSON>er", "backgroundSecondary", "Card", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "Header", "Title", "Subtitle", "Body", "Footer", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/components/common/Card.js"], "sourcesContent": ["import React from 'react';\nimport styled, { css } from 'styled-components';\n\nconst StyledCard = styled.div`\n  background-color: ${props => props.theme.colors.cardBg};\n  border: 1px solid ${props => props.theme.colors.cardBorder};\n  border-radius: ${props => props.theme.borderRadius.lg};\n  box-shadow: ${props => props.theme.colors.cardShadow};\n  overflow: hidden;\n\n  ${props => props.padding && css`\n    padding: ${props.theme.spacing.lg};\n  `}\n\n  ${props => props.hover && css`\n    transition: all ${props.theme.transitions.fast};\n    cursor: pointer;\n\n    &:hover {\n      box-shadow: ${props.theme.shadows.md};\n      transform: translateY(-1px);\n    }\n  `}\n`;\n\nconst CardHeader = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-bottom: 1px solid ${props => props.theme.colors.border};\n  \n  ${props => props.noBorder && css`\n    border-bottom: none;\n  `}\n`;\n\nconst CardTitle = styled.h3`\n  margin: 0;\n  font-size: ${props => props.theme.fontSize.lg};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n`;\n\nconst CardSubtitle = styled.p`\n  margin: ${props => props.theme.spacing.xs} 0 0 0;\n  font-size: ${props => props.theme.fontSize.sm};\n  color: ${props => props.theme.colors.textSecondary};\n`;\n\nconst CardBody = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n`;\n\nconst CardFooter = styled.div`\n  padding: ${props => props.theme.spacing.lg};\n  border-top: 1px solid ${props => props.theme.colors.border};\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n\n  ${props => props.noBorder && css`\n    border-top: none;\n    background-color: transparent;\n  `}\n`;\n\nconst Card = ({ \n  children, \n  padding = false, \n  hover = false, \n  className,\n  ...props \n}) => {\n  return (\n    <StyledCard \n      padding={padding} \n      hover={hover} \n      className={className}\n      {...props}\n    >\n      {children}\n    </StyledCard>\n  );\n};\n\nCard.Header = CardHeader;\nCard.Title = CardTitle;\nCard.Subtitle = CardSubtitle;\nCard.Body = CardBody;\nCard.Footer = CardFooter;\n\nexport default Card;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,GAAG,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAGJ,MAAM,CAACK,GAAG;AAC7B,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM;AACxD,sBAAsBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACE,UAAU;AAC5D,mBAAmBJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,YAAY,CAACC,EAAE;AACvD,gBAAgBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,UAAU;AACtD;AACA;AACA,IAAIP,KAAK,IAAIA,KAAK,CAACQ,OAAO,IAAIb,GAAG;AACjC,eAAeK,KAAK,CAACC,KAAK,CAACQ,OAAO,CAACH,EAAE;AACrC,GAAG;AACH;AACA,IAAIN,KAAK,IAAIA,KAAK,CAACU,KAAK,IAAIf,GAAG;AAC/B,sBAAsBK,KAAK,CAACC,KAAK,CAACU,WAAW,CAACC,IAAI;AAClD;AACA;AACA;AACA,oBAAoBZ,KAAK,CAACC,KAAK,CAACY,OAAO,CAACC,EAAE;AAC1C;AACA;AACA,GAAG;AACH,CAAC;AAACC,EAAA,GApBIjB,UAAU;AAsBhB,MAAMkB,UAAU,GAAGtB,MAAM,CAACK,GAAG;AAC7B,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,OAAO,CAACH,EAAE;AAC5C,6BAA6BN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACe,MAAM;AAC/D;AACA,IAAIjB,KAAK,IAAIA,KAAK,CAACkB,QAAQ,IAAIvB,GAAG;AAClC;AACA,GAAG;AACH,CAAC;AAED,MAAMwB,SAAS,GAAGzB,MAAM,CAAC0B,EAAE;AAC3B;AACA,eAAepB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACoB,QAAQ,CAACf,EAAE;AAC/C,iBAAiBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqB,UAAU,CAACC,QAAQ;AACzD,WAAWvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACsB,WAAW;AAClD,CAAC;AAED,MAAMC,YAAY,GAAG/B,MAAM,CAACgC,CAAC;AAC7B,YAAY1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,OAAO,CAACkB,EAAE;AAC3C,eAAe3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACoB,QAAQ,CAACO,EAAE;AAC/C,WAAW5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC2B,aAAa;AACpD,CAAC;AAED,MAAMC,QAAQ,GAAGpC,MAAM,CAACK,GAAG;AAC3B,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,OAAO,CAACH,EAAE;AAC5C,CAAC;AAED,MAAMyB,UAAU,GAAGrC,MAAM,CAACK,GAAG;AAC7B,aAAaC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,OAAO,CAACH,EAAE;AAC5C,0BAA0BN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACe,MAAM;AAC5D,sBAAsBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC8B,mBAAmB;AACrE;AACA,IAAIhC,KAAK,IAAIA,KAAK,CAACkB,QAAQ,IAAIvB,GAAG;AAClC;AACA;AACA,GAAG;AACH,CAAC;AAED,MAAMsC,IAAI,GAAGA,CAAC;EACZC,QAAQ;EACR1B,OAAO,GAAG,KAAK;EACfE,KAAK,GAAG,KAAK;EACbyB,SAAS;EACT,GAAGnC;AACL,CAAC,KAAK;EACJ,oBACEH,OAAA,CAACC,UAAU;IACTU,OAAO,EAAEA,OAAQ;IACjBE,KAAK,EAAEA,KAAM;IACbyB,SAAS,EAAEA,SAAU;IAAA,GACjBnC,KAAK;IAAAkC,QAAA,EAERA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;AAACC,GAAA,GAjBIP,IAAI;AAmBVA,IAAI,CAACQ,MAAM,GAAGzB,UAAU;AACxBiB,IAAI,CAACS,KAAK,GAAGvB,SAAS;AACtBc,IAAI,CAACU,QAAQ,GAAGlB,YAAY;AAC5BQ,IAAI,CAACW,IAAI,GAAGd,QAAQ;AACpBG,IAAI,CAACY,MAAM,GAAGd,UAAU;AAExB,eAAeE,IAAI;AAAC,IAAAlB,EAAA,EAAAyB,GAAA;AAAAM,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAAN,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}