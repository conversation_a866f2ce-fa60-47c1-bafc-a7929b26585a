{"ast": null, "code": "import { createGlobalStyle } from 'styled-components';\nconst GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html {\n    font-size: 16px;\n    line-height: 1.5;\n  }\n\n  body {\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background-color: ${props => props.theme.colors.background};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n\n  code {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n      monospace;\n  }\n\n  /* Scrollbar Styles */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${props => props.theme.colors.backgroundSecondary};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${props => props.theme.colors.border};\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: ${props => props.theme.colors.borderDark};\n  }\n\n  /* Focus styles */\n  button:focus-visible,\n  input:focus-visible,\n  select:focus-visible,\n  textarea:focus-visible {\n    outline: 2px solid ${props => props.theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* React Toastify custom styles */\n  .Toastify__toast-container {\n    font-family: inherit;\n  }\n\n  .Toastify__toast {\n    border-radius: ${props => props.theme.borderRadius.lg};\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n\n  .Toastify__toast--success {\n    background-color: ${props => props.theme.colors.success};\n  }\n\n  .Toastify__toast--error {\n    background-color: ${props => props.theme.colors.error};\n  }\n\n  .Toastify__toast--warning {\n    background-color: ${props => props.theme.colors.warning};\n  }\n\n  .Toastify__toast--info {\n    background-color: ${props => props.theme.colors.info};\n  }\n\n  /* Utility classes */\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n\n  .text-center {\n    text-align: center;\n  }\n\n  .text-left {\n    text-align: left;\n  }\n\n  .text-right {\n    text-align: right;\n  }\n\n  .mb-0 { margin-bottom: 0; }\n  .mb-1 { margin-bottom: ${props => props.theme.spacing.xs}; }\n  .mb-2 { margin-bottom: ${props => props.theme.spacing.sm}; }\n  .mb-3 { margin-bottom: ${props => props.theme.spacing.md}; }\n  .mb-4 { margin-bottom: ${props => props.theme.spacing.lg}; }\n  .mb-5 { margin-bottom: ${props => props.theme.spacing.xl}; }\n\n  .mt-0 { margin-top: 0; }\n  .mt-1 { margin-top: ${props => props.theme.spacing.xs}; }\n  .mt-2 { margin-top: ${props => props.theme.spacing.sm}; }\n  .mt-3 { margin-top: ${props => props.theme.spacing.md}; }\n  .mt-4 { margin-top: ${props => props.theme.spacing.lg}; }\n  .mt-5 { margin-top: ${props => props.theme.spacing.xl}; }\n`;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["createGlobalStyle", "GlobalStyles", "props", "theme", "colors", "background", "textPrimary", "backgroundSecondary", "border", "borderDark", "primary", "borderRadius", "lg", "shadows", "success", "error", "warning", "info", "spacing", "xs", "sm", "md", "xl"], "sources": ["D:/Projects/qmsus/frontend/src/styles/GlobalStyles.js"], "sourcesContent": ["import { createGlobalStyle } from 'styled-components';\n\nconst GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html {\n    font-size: 16px;\n    line-height: 1.5;\n  }\n\n  body {\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background-color: ${props => props.theme.colors.background};\n    color: ${props => props.theme.colors.textPrimary};\n  }\n\n  code {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n      monospace;\n  }\n\n  /* Scrollbar Styles */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${props => props.theme.colors.backgroundSecondary};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${props => props.theme.colors.border};\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: ${props => props.theme.colors.borderDark};\n  }\n\n  /* Focus styles */\n  button:focus-visible,\n  input:focus-visible,\n  select:focus-visible,\n  textarea:focus-visible {\n    outline: 2px solid ${props => props.theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* React Toastify custom styles */\n  .Toastify__toast-container {\n    font-family: inherit;\n  }\n\n  .Toastify__toast {\n    border-radius: ${props => props.theme.borderRadius.lg};\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n\n  .Toastify__toast--success {\n    background-color: ${props => props.theme.colors.success};\n  }\n\n  .Toastify__toast--error {\n    background-color: ${props => props.theme.colors.error};\n  }\n\n  .Toastify__toast--warning {\n    background-color: ${props => props.theme.colors.warning};\n  }\n\n  .Toastify__toast--info {\n    background-color: ${props => props.theme.colors.info};\n  }\n\n  /* Utility classes */\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n\n  .text-center {\n    text-align: center;\n  }\n\n  .text-left {\n    text-align: left;\n  }\n\n  .text-right {\n    text-align: right;\n  }\n\n  .mb-0 { margin-bottom: 0; }\n  .mb-1 { margin-bottom: ${props => props.theme.spacing.xs}; }\n  .mb-2 { margin-bottom: ${props => props.theme.spacing.sm}; }\n  .mb-3 { margin-bottom: ${props => props.theme.spacing.md}; }\n  .mb-4 { margin-bottom: ${props => props.theme.spacing.lg}; }\n  .mb-5 { margin-bottom: ${props => props.theme.spacing.xl}; }\n\n  .mt-0 { margin-top: 0; }\n  .mt-1 { margin-top: ${props => props.theme.spacing.xs}; }\n  .mt-2 { margin-top: ${props => props.theme.spacing.sm}; }\n  .mt-3 { margin-top: ${props => props.theme.spacing.md}; }\n  .mt-4 { margin-top: ${props => props.theme.spacing.lg}; }\n  .mt-5 { margin-top: ${props => props.theme.spacing.xl}; }\n`;\n\nexport default GlobalStyles;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,mBAAmB;AAErD,MAAMC,YAAY,GAAGD,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,UAAU;AAC9D,aAAaH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACE,WAAW;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACG,mBAAmB;AACjE;AACA;AACA;AACA,kBAAkBL,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACI,MAAM;AACpD;AACA;AACA;AACA;AACA,kBAAkBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACK,UAAU;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACM,OAAO;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqBR,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACQ,YAAY,CAACC,EAAE;AACzD,kBAAkBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,OAAO,CAACD,EAAE;AACjD;AACA;AACA;AACA,wBAAwBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACU,OAAO;AAC3D;AACA;AACA;AACA,wBAAwBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACW,KAAK;AACzD;AACA;AACA;AACA,wBAAwBb,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACY,OAAO;AAC3D;AACA;AACA;AACA,wBAAwBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2Bf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACC,EAAE;AAC1D,2BAA2BjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACE,EAAE;AAC1D,2BAA2BlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACG,EAAE;AAC1D,2BAA2BnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACN,EAAE;AAC1D,2BAA2BV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACI,EAAE;AAC1D;AACA;AACA,wBAAwBpB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACC,EAAE;AACvD,wBAAwBjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACE,EAAE;AACvD,wBAAwBlB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACG,EAAE;AACvD,wBAAwBnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACN,EAAE;AACvD,wBAAwBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,OAAO,CAACI,EAAE;AACvD,CAAC;AAED,eAAerB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}