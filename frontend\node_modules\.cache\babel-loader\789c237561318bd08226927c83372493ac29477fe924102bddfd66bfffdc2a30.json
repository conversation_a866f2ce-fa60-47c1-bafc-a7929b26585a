{"ast": null, "code": "import React from \"react\";\nexport var DefaultContext = {\n  color: undefined,\n  size: undefined,\n  className: undefined,\n  style: undefined,\n  attr: undefined\n};\nexport var IconContext = React.createContext && React.createContext(DefaultContext);", "map": {"version": 3, "names": ["React", "DefaultContext", "color", "undefined", "size", "className", "style", "attr", "IconContext", "createContext"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-icons/lib/esm/iconContext.js"], "sourcesContent": ["import React from \"react\";\nexport var DefaultContext = {\n  color: undefined,\n  size: undefined,\n  className: undefined,\n  style: undefined,\n  attr: undefined\n};\nexport var IconContext = React.createContext && React.createContext(DefaultContext);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,cAAc,GAAG;EAC1BC,KAAK,EAAEC,SAAS;EAChBC,IAAI,EAAED,SAAS;EACfE,SAAS,EAAEF,SAAS;EACpBG,KAAK,EAAEH,SAAS;EAChBI,IAAI,EAAEJ;AACR,CAAC;AACD,OAAO,IAAIK,WAAW,GAAGR,KAAK,CAACS,aAAa,IAAIT,KAAK,CAACS,aAAa,CAACR,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}