{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\pages\\\\settings\\\\ModulesPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { toast } from 'react-toastify';\nimport { FiPlus, FiEdit, FiTrash2, FiSearch } from 'react-icons/fi';\nimport { modulesAPI } from '../../api/modules';\nimport Card from '../../components/common/Card';\nimport Button from '../../components/common/Button';\nimport Input from '../../components/common/Input';\nimport Table from '../../components/common/Table';\nimport Modal from '../../components/common/Modal';\nimport ModuleForm from '../../components/forms/ModuleForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.lg};\n`;\n_c = PageContainer;\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n  flex-wrap: wrap;\n`;\n_c2 = PageHeader;\nconst PageTitle = styled.h1`\n  font-size: ${props => props.theme.fontSize['2xl']};\n  font-weight: ${props => props.theme.fontWeight.bold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0;\n  flex: 1;\n`;\n_c3 = PageTitle;\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  align-items: center;\n`;\n_c4 = HeaderActions;\nconst SearchContainer = styled.div`\n  position: relative;\n  width: 300px;\n`;\n_c5 = SearchContainer;\nconst SearchIcon = styled.div`\n  position: absolute;\n  left: ${props => props.theme.spacing.md};\n  top: 50%;\n  transform: translateY(-50%);\n  color: ${props => props.theme.colors.textMuted};\n`;\n_c6 = SearchIcon;\nconst SearchInput = styled(Input)`\n  input {\n    padding-left: ${props => props.theme.spacing.xl};\n  }\n`;\n_c7 = SearchInput;\nconst TableContainer = styled(Card)`\n  overflow: hidden;\n`;\n_c8 = TableContainer;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c9 = ActionButtons;\nconst StatusBadge = styled.span`\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSize.xs};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  \n  ${props => props.active ? `\n    background-color: ${props.theme.colors.success}20;\n    color: ${props.theme.colors.success};\n  ` : `\n    background-color: ${props.theme.colors.error}20;\n    color: ${props.theme.colors.error};\n  `}\n`;\n_c0 = StatusBadge;\nconst CodeBadge = styled.span`\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n  color: ${props => props.theme.colors.textSecondary};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSize.xs};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  font-family: monospace;\n`;\n_c1 = CodeBadge;\nconst ModulesPage = () => {\n  _s();\n  const [modules, setModules] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [showModal, setShowModal] = useState(false);\n  const [editingModule, setEditingModule] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n  useEffect(() => {\n    fetchModules();\n  }, [currentPage, searchTerm]);\n  const fetchModules = async () => {\n    try {\n      setLoading(true);\n      const response = await modulesAPI.getModules({\n        page: currentPage,\n        limit: 10,\n        search: searchTerm\n      });\n      setModules(response.data.modules);\n      setTotalPages(response.data.pagination.totalPages);\n    } catch (error) {\n      toast.error('Failed to fetch modules');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1);\n  };\n  const handleAddModule = () => {\n    setEditingModule(null);\n    setShowModal(true);\n  };\n  const handleEditModule = module => {\n    setEditingModule(module);\n    setShowModal(true);\n  };\n  const handleDeleteModule = async moduleId => {\n    try {\n      await modulesAPI.deleteModule(moduleId);\n      toast.success('Module deleted successfully');\n      fetchModules();\n      setDeleteConfirm(null);\n    } catch (error) {\n      toast.error('Failed to delete module');\n    }\n  };\n  const handleFormSubmit = async moduleData => {\n    try {\n      if (editingModule) {\n        await modulesAPI.updateModule(editingModule.id, moduleData);\n        toast.success('Module updated successfully');\n      } else {\n        await modulesAPI.createModule(moduleData);\n        toast.success('Module created successfully');\n      }\n      setShowModal(false);\n      fetchModules();\n    } catch (error) {\n      toast.error(editingModule ? 'Failed to update module' : 'Failed to create module');\n    }\n  };\n  const columns = [{\n    header: 'Name',\n    accessor: 'name'\n  }, {\n    header: 'Code',\n    accessor: 'code',\n    render: code => /*#__PURE__*/_jsxDEV(CodeBadge, {\n      children: code\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 25\n    }, this)\n  }, {\n    header: 'Description',\n    accessor: 'description',\n    render: description => description || '-'\n  }, {\n    header: 'Parent',\n    accessor: 'parent',\n    render: parent => (parent === null || parent === void 0 ? void 0 : parent.name) || '-'\n  }, {\n    header: 'Order',\n    accessor: 'sortOrder'\n  }, {\n    header: 'Status',\n    accessor: 'isActive',\n    render: isActive => /*#__PURE__*/_jsxDEV(StatusBadge, {\n      active: isActive,\n      children: isActive ? 'Active' : 'Inactive'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this)\n  }, {\n    header: 'Actions',\n    accessor: 'actions',\n    render: (_, module) => /*#__PURE__*/_jsxDEV(ActionButtons, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"ghost\",\n        size: \"sm\",\n        onClick: () => handleEditModule(module),\n        children: /*#__PURE__*/_jsxDEV(FiEdit, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"ghost\",\n        size: \"sm\",\n        onClick: () => setDeleteConfirm(module),\n        children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"Module Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeaderActions, {\n        children: [/*#__PURE__*/_jsxDEV(SearchContainer, {\n          children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n            children: /*#__PURE__*/_jsxDEV(FiSearch, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SearchInput, {\n            placeholder: \"Search modules...\",\n            value: searchTerm,\n            onChange: handleSearch\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleAddModule,\n          children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), \"Add Module\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        data: modules,\n        loading: loading,\n        emptyMessage: \"No modules found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingModule ? 'Edit Module' : 'Add New Module',\n      onClose: () => setShowModal(false),\n      children: /*#__PURE__*/_jsxDEV(ModuleForm, {\n        module: editingModule,\n        modules: modules,\n        onSubmit: handleFormSubmit,\n        onCancel: () => setShowModal(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this), deleteConfirm && /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Confirm Delete\",\n      onClose: () => setDeleteConfirm(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Are you sure you want to delete module \\\"\", deleteConfirm.name, \"\\\"?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            marginTop: '1.5rem',\n            justifyContent: 'flex-end'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setDeleteConfirm(null),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"danger\",\n            onClick: () => handleDeleteModule(deleteConfirm.id),\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(ModulesPage, \"ON77pUmLvkYX4wyIaZsK49Q+Ths=\");\n_c10 = ModulesPage;\nexport default ModulesPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"PageHeader\");\n$RefreshReg$(_c3, \"PageTitle\");\n$RefreshReg$(_c4, \"HeaderActions\");\n$RefreshReg$(_c5, \"SearchContainer\");\n$RefreshReg$(_c6, \"SearchIcon\");\n$RefreshReg$(_c7, \"SearchInput\");\n$RefreshReg$(_c8, \"TableContainer\");\n$RefreshReg$(_c9, \"ActionButtons\");\n$RefreshReg$(_c0, \"StatusBadge\");\n$RefreshReg$(_c1, \"CodeBadge\");\n$RefreshReg$(_c10, \"ModulesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "toast", "FiPlus", "FiEdit", "FiTrash2", "FiSearch", "modulesAPI", "Card", "<PERSON><PERSON>", "Input", "Table", "Modal", "ModuleForm", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "div", "props", "theme", "spacing", "lg", "_c", "<PERSON><PERSON><PERSON><PERSON>", "md", "_c2", "Page<PERSON><PERSON>le", "h1", "fontSize", "fontWeight", "bold", "colors", "textPrimary", "_c3", "HeaderActions", "_c4", "SearchContainer", "_c5", "SearchIcon", "textMuted", "_c6", "SearchInput", "xl", "_c7", "TableContainer", "_c8", "ActionButtons", "sm", "_c9", "StatusBadge", "span", "xs", "borderRadius", "medium", "active", "success", "error", "_c0", "CodeBadge", "backgroundSecondary", "textSecondary", "_c1", "ModulesPage", "_s", "modules", "setModules", "loading", "setLoading", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "showModal", "setShowModal", "editingModule", "setEditingModule", "deleteConfirm", "setDeleteConfirm", "fetchModules", "response", "getModules", "page", "limit", "search", "data", "pagination", "handleSearch", "e", "target", "value", "handleAddModule", "handleEditModule", "module", "handleDeleteModule", "moduleId", "deleteModule", "handleFormSubmit", "moduleData", "updateModule", "id", "createModule", "columns", "header", "accessor", "render", "code", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "parent", "name", "isActive", "_", "variant", "size", "onClick", "placeholder", "onChange", "emptyMessage", "title", "onClose", "onSubmit", "onCancel", "style", "padding", "display", "gap", "marginTop", "justifyContent", "_c10", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/pages/settings/ModulesPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { toast } from 'react-toastify';\nimport { FiPlus, FiEdit, FiTrash2, FiSearch } from 'react-icons/fi';\nimport { modulesAPI } from '../../api/modules';\nimport Card from '../../components/common/Card';\nimport Button from '../../components/common/Button';\nimport Input from '../../components/common/Input';\nimport Table from '../../components/common/Table';\nimport Modal from '../../components/common/Modal';\nimport ModuleForm from '../../components/forms/ModuleForm';\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.lg};\n`;\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: ${props => props.theme.spacing.md};\n  flex-wrap: wrap;\n`;\n\nconst PageTitle = styled.h1`\n  font-size: ${props => props.theme.fontSize['2xl']};\n  font-weight: ${props => props.theme.fontWeight.bold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0;\n  flex: 1;\n`;\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  align-items: center;\n`;\n\nconst SearchContainer = styled.div`\n  position: relative;\n  width: 300px;\n`;\n\nconst SearchIcon = styled.div`\n  position: absolute;\n  left: ${props => props.theme.spacing.md};\n  top: 50%;\n  transform: translateY(-50%);\n  color: ${props => props.theme.colors.textMuted};\n`;\n\nconst SearchInput = styled(Input)`\n  input {\n    padding-left: ${props => props.theme.spacing.xl};\n  }\n`;\n\nconst TableContainer = styled(Card)`\n  overflow: hidden;\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst StatusBadge = styled.span`\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSize.xs};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  \n  ${props => props.active ? `\n    background-color: ${props.theme.colors.success}20;\n    color: ${props.theme.colors.success};\n  ` : `\n    background-color: ${props.theme.colors.error}20;\n    color: ${props.theme.colors.error};\n  `}\n`;\n\nconst CodeBadge = styled.span`\n  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};\n  background-color: ${props => props.theme.colors.backgroundSecondary};\n  color: ${props => props.theme.colors.textSecondary};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSize.xs};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  font-family: monospace;\n`;\n\nconst ModulesPage = () => {\n  const [modules, setModules] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [showModal, setShowModal] = useState(false);\n  const [editingModule, setEditingModule] = useState(null);\n  const [deleteConfirm, setDeleteConfirm] = useState(null);\n\n  useEffect(() => {\n    fetchModules();\n  }, [currentPage, searchTerm]);\n\n  const fetchModules = async () => {\n    try {\n      setLoading(true);\n      const response = await modulesAPI.getModules({\n        page: currentPage,\n        limit: 10,\n        search: searchTerm\n      });\n      setModules(response.data.modules);\n      setTotalPages(response.data.pagination.totalPages);\n    } catch (error) {\n      toast.error('Failed to fetch modules');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1);\n  };\n\n  const handleAddModule = () => {\n    setEditingModule(null);\n    setShowModal(true);\n  };\n\n  const handleEditModule = (module) => {\n    setEditingModule(module);\n    setShowModal(true);\n  };\n\n  const handleDeleteModule = async (moduleId) => {\n    try {\n      await modulesAPI.deleteModule(moduleId);\n      toast.success('Module deleted successfully');\n      fetchModules();\n      setDeleteConfirm(null);\n    } catch (error) {\n      toast.error('Failed to delete module');\n    }\n  };\n\n  const handleFormSubmit = async (moduleData) => {\n    try {\n      if (editingModule) {\n        await modulesAPI.updateModule(editingModule.id, moduleData);\n        toast.success('Module updated successfully');\n      } else {\n        await modulesAPI.createModule(moduleData);\n        toast.success('Module created successfully');\n      }\n      setShowModal(false);\n      fetchModules();\n    } catch (error) {\n      toast.error(editingModule ? 'Failed to update module' : 'Failed to create module');\n    }\n  };\n\n  const columns = [\n    {\n      header: 'Name',\n      accessor: 'name'\n    },\n    {\n      header: 'Code',\n      accessor: 'code',\n      render: (code) => <CodeBadge>{code}</CodeBadge>\n    },\n    {\n      header: 'Description',\n      accessor: 'description',\n      render: (description) => description || '-'\n    },\n    {\n      header: 'Parent',\n      accessor: 'parent',\n      render: (parent) => parent?.name || '-'\n    },\n    {\n      header: 'Order',\n      accessor: 'sortOrder'\n    },\n    {\n      header: 'Status',\n      accessor: 'isActive',\n      render: (isActive) => (\n        <StatusBadge active={isActive}>\n          {isActive ? 'Active' : 'Inactive'}\n        </StatusBadge>\n      )\n    },\n    {\n      header: 'Actions',\n      accessor: 'actions',\n      render: (_, module) => (\n        <ActionButtons>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => handleEditModule(module)}\n          >\n            <FiEdit size={16} />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setDeleteConfirm(module)}\n          >\n            <FiTrash2 size={16} />\n          </Button>\n        </ActionButtons>\n      )\n    }\n  ];\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <PageTitle>Module Management</PageTitle>\n        <HeaderActions>\n          <SearchContainer>\n            <SearchIcon>\n              <FiSearch size={16} />\n            </SearchIcon>\n            <SearchInput\n              placeholder=\"Search modules...\"\n              value={searchTerm}\n              onChange={handleSearch}\n            />\n          </SearchContainer>\n          <Button\n            variant=\"primary\"\n            onClick={handleAddModule}\n          >\n            <FiPlus size={16} />\n            Add Module\n          </Button>\n        </HeaderActions>\n      </PageHeader>\n\n      <TableContainer>\n        <Table\n          columns={columns}\n          data={modules}\n          loading={loading}\n          emptyMessage=\"No modules found\"\n        />\n      </TableContainer>\n\n      {showModal && (\n        <Modal\n          title={editingModule ? 'Edit Module' : 'Add New Module'}\n          onClose={() => setShowModal(false)}\n        >\n          <ModuleForm\n            module={editingModule}\n            modules={modules}\n            onSubmit={handleFormSubmit}\n            onCancel={() => setShowModal(false)}\n          />\n        </Modal>\n      )}\n\n      {deleteConfirm && (\n        <Modal\n          title=\"Confirm Delete\"\n          onClose={() => setDeleteConfirm(null)}\n        >\n          <div style={{ padding: '1rem 0' }}>\n            <p>Are you sure you want to delete module \"{deleteConfirm.name}\"?</p>\n            <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem', justifyContent: 'flex-end' }}>\n              <Button variant=\"secondary\" onClick={() => setDeleteConfirm(null)}>\n                Cancel\n              </Button>\n              <Button variant=\"danger\" onClick={() => handleDeleteModule(deleteConfirm.id)}>\n                Delete\n              </Button>\n            </div>\n          </div>\n        </Modal>\n      )}\n    </PageContainer>\n  );\n};\n\nexport default ModulesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACnE,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,IAAI,MAAM,8BAA8B;AAC/C,OAAOC,MAAM,MAAM,gCAAgC;AACnD,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAOC,UAAU,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,aAAa,GAAGf,MAAM,CAACgB,GAAG;AAChC;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AACxC,CAAC;AAACC,EAAA,GAJIN,aAAa;AAMnB,MAAMO,UAAU,GAAGtB,MAAM,CAACgB,GAAG;AAC7B;AACA;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACI,EAAE;AACxC;AACA,CAAC;AAACC,GAAA,GANIF,UAAU;AAQhB,MAAMG,SAAS,GAAGzB,MAAM,CAAC0B,EAAE;AAC3B,eAAeT,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,QAAQ,CAAC,KAAK,CAAC;AACnD,iBAAiBV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,UAAU,CAACC,IAAI;AACrD,WAAWZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,MAAM,CAACC,WAAW;AAClD;AACA;AACA,CAAC;AAACC,GAAA,GANIP,SAAS;AAQf,MAAMQ,aAAa,GAAGjC,MAAM,CAACgB,GAAG;AAChC;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACI,EAAE;AACxC;AACA,CAAC;AAACW,GAAA,GAJID,aAAa;AAMnB,MAAME,eAAe,GAAGnC,MAAM,CAACgB,GAAG;AAClC;AACA;AACA,CAAC;AAACoB,GAAA,GAHID,eAAe;AAKrB,MAAME,UAAU,GAAGrC,MAAM,CAACgB,GAAG;AAC7B;AACA,UAAUC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACI,EAAE;AACzC;AACA;AACA,WAAWN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,MAAM,CAACQ,SAAS;AAChD,CAAC;AAACC,GAAA,GANIF,UAAU;AAQhB,MAAMG,WAAW,GAAGxC,MAAM,CAACS,KAAK,CAAC;AACjC;AACA,oBAAoBQ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACsB,EAAE;AACnD;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,cAAc,GAAG3C,MAAM,CAACO,IAAI,CAAC;AACnC;AACA,CAAC;AAACqC,GAAA,GAFID,cAAc;AAIpB,MAAME,aAAa,GAAG7C,MAAM,CAACgB,GAAG;AAChC;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC2B,EAAE;AACxC,CAAC;AAACC,GAAA,GAHIF,aAAa;AAKnB,MAAMG,WAAW,GAAGhD,MAAM,CAACiD,IAAI;AAC/B,aAAahC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC+B,EAAE,IAAIjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC2B,EAAE;AAC/E,mBAAmB7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiC,YAAY,CAAC5B,EAAE;AACvD,eAAeN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,QAAQ,CAACuB,EAAE;AAC/C,iBAAiBjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,UAAU,CAACwB,MAAM;AACvD;AACA,IAAInC,KAAK,IAAIA,KAAK,CAACoC,MAAM,GAAG;AAC5B,wBAAwBpC,KAAK,CAACC,KAAK,CAACY,MAAM,CAACwB,OAAO;AAClD,aAAarC,KAAK,CAACC,KAAK,CAACY,MAAM,CAACwB,OAAO;AACvC,GAAG,GAAG;AACN,wBAAwBrC,KAAK,CAACC,KAAK,CAACY,MAAM,CAACyB,KAAK;AAChD,aAAatC,KAAK,CAACC,KAAK,CAACY,MAAM,CAACyB,KAAK;AACrC,GAAG;AACH,CAAC;AAACC,GAAA,GAbIR,WAAW;AAejB,MAAMS,SAAS,GAAGzD,MAAM,CAACiD,IAAI;AAC7B,aAAahC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC+B,EAAE,IAAIjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAAC2B,EAAE;AAC/E,sBAAsB7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,MAAM,CAAC4B,mBAAmB;AACrE,WAAWzC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,MAAM,CAAC6B,aAAa;AACpD,mBAAmB1C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACiC,YAAY,CAAC5B,EAAE;AACvD,eAAeN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACS,QAAQ,CAACuB,EAAE;AAC/C,iBAAiBjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,UAAU,CAACwB,MAAM;AACvD;AACA,CAAC;AAACQ,GAAA,GARIH,SAAS;AAUf,MAAMI,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmE,OAAO,EAAEC,UAAU,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdgF,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACV,WAAW,EAAEF,UAAU,CAAC,CAAC;EAE7B,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,QAAQ,GAAG,MAAM1E,UAAU,CAAC2E,UAAU,CAAC;QAC3CC,IAAI,EAAEb,WAAW;QACjBc,KAAK,EAAE,EAAE;QACTC,MAAM,EAAEjB;MACV,CAAC,CAAC;MACFH,UAAU,CAACgB,QAAQ,CAACK,IAAI,CAACtB,OAAO,CAAC;MACjCS,aAAa,CAACQ,QAAQ,CAACK,IAAI,CAACC,UAAU,CAACf,UAAU,CAAC;IACpD,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACRW,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1BpB,aAAa,CAACoB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC7BpB,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5Bf,gBAAgB,CAAC,IAAI,CAAC;IACtBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkB,gBAAgB,GAAIC,MAAM,IAAK;IACnCjB,gBAAgB,CAACiB,MAAM,CAAC;IACxBnB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoB,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAMzF,UAAU,CAAC0F,YAAY,CAACD,QAAQ,CAAC;MACvC9F,KAAK,CAACqD,OAAO,CAAC,6BAA6B,CAAC;MAC5CyB,YAAY,CAAC,CAAC;MACdD,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;EAED,MAAM0C,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACF,IAAIvB,aAAa,EAAE;QACjB,MAAMrE,UAAU,CAAC6F,YAAY,CAACxB,aAAa,CAACyB,EAAE,EAAEF,UAAU,CAAC;QAC3DjG,KAAK,CAACqD,OAAO,CAAC,6BAA6B,CAAC;MAC9C,CAAC,MAAM;QACL,MAAMhD,UAAU,CAAC+F,YAAY,CAACH,UAAU,CAAC;QACzCjG,KAAK,CAACqD,OAAO,CAAC,6BAA6B,CAAC;MAC9C;MACAoB,YAAY,CAAC,KAAK,CAAC;MACnBK,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAACoB,aAAa,GAAG,yBAAyB,GAAG,yBAAyB,CAAC;IACpF;EACF,CAAC;EAED,MAAM2B,OAAO,GAAG,CACd;IACEC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACZ,CAAC,EACD;IACED,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAGC,IAAI,iBAAK5F,OAAA,CAAC2C,SAAS;MAAAkD,QAAA,EAAED;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAChD,CAAC,EACD;IACER,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,aAAa;IACvBC,MAAM,EAAGO,WAAW,IAAKA,WAAW,IAAI;EAC1C,CAAC,EACD;IACET,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAGQ,MAAM,IAAK,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,IAAI,KAAI;EACtC,CAAC,EACD;IACEX,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACZ,CAAC,EACD;IACED,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAGU,QAAQ,iBACfrG,OAAA,CAACkC,WAAW;MAACK,MAAM,EAAE8D,QAAS;MAAAR,QAAA,EAC3BQ,QAAQ,GAAG,QAAQ,GAAG;IAAU;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB;EAEjB,CAAC,EACD;IACER,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAEA,CAACW,CAAC,EAAEvB,MAAM,kBAChB/E,OAAA,CAAC+B,aAAa;MAAA8D,QAAA,gBACZ7F,OAAA,CAACN,MAAM;QACL6G,OAAO,EAAC,OAAO;QACfC,IAAI,EAAC,IAAI;QACTC,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACC,MAAM,CAAE;QAAAc,QAAA,eAExC7F,OAAA,CAACX,MAAM;UAACmH,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACTjG,OAAA,CAACN,MAAM;QACL6G,OAAO,EAAC,OAAO;QACfC,IAAI,EAAC,IAAI;QACTC,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAACe,MAAM,CAAE;QAAAc,QAAA,eAExC7F,OAAA,CAACV,QAAQ;UAACkH,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEnB,CAAC,CACF;EAED,oBACEjG,OAAA,CAACC,aAAa;IAAA4F,QAAA,gBACZ7F,OAAA,CAACQ,UAAU;MAAAqF,QAAA,gBACT7F,OAAA,CAACW,SAAS;QAAAkF,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACxCjG,OAAA,CAACmB,aAAa;QAAA0E,QAAA,gBACZ7F,OAAA,CAACqB,eAAe;UAAAwE,QAAA,gBACd7F,OAAA,CAACuB,UAAU;YAAAsE,QAAA,eACT7F,OAAA,CAACT,QAAQ;cAACiH,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbjG,OAAA,CAAC0B,WAAW;YACVgF,WAAW,EAAC,mBAAmB;YAC/B9B,KAAK,EAAEvB,UAAW;YAClBsD,QAAQ,EAAElC;UAAa;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,eAClBjG,OAAA,CAACN,MAAM;UACL6G,OAAO,EAAC,SAAS;UACjBE,OAAO,EAAE5B,eAAgB;UAAAgB,QAAA,gBAEzB7F,OAAA,CAACZ,MAAM;YAACoH,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEbjG,OAAA,CAAC6B,cAAc;MAAAgE,QAAA,eACb7F,OAAA,CAACJ,KAAK;QACJ4F,OAAO,EAAEA,OAAQ;QACjBjB,IAAI,EAAEtB,OAAQ;QACdE,OAAO,EAAEA,OAAQ;QACjByD,YAAY,EAAC;MAAkB;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,EAEhBtC,SAAS,iBACR3D,OAAA,CAACH,KAAK;MACJgH,KAAK,EAAEhD,aAAa,GAAG,aAAa,GAAG,gBAAiB;MACxDiD,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAAC,KAAK,CAAE;MAAAiC,QAAA,eAEnC7F,OAAA,CAACF,UAAU;QACTiF,MAAM,EAAElB,aAAc;QACtBZ,OAAO,EAAEA,OAAQ;QACjB8D,QAAQ,EAAE5B,gBAAiB;QAC3B6B,QAAQ,EAAEA,CAAA,KAAMpD,YAAY,CAAC,KAAK;MAAE;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACR,EAEAlC,aAAa,iBACZ/D,OAAA,CAACH,KAAK;MACJgH,KAAK,EAAC,gBAAgB;MACtBC,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC,IAAI,CAAE;MAAA6B,QAAA,eAEtC7F,OAAA;QAAKiH,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAArB,QAAA,gBAChC7F,OAAA;UAAA6F,QAAA,GAAG,2CAAwC,EAAC9B,aAAa,CAACqC,IAAI,EAAC,KAAE;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrEjG,OAAA;UAAKiH,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEC,SAAS,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAW,CAAE;UAAAzB,QAAA,gBAC5F7F,OAAA,CAACN,MAAM;YAAC6G,OAAO,EAAC,WAAW;YAACE,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAAC,IAAI,CAAE;YAAA6B,QAAA,EAAC;UAEnE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjG,OAAA,CAACN,MAAM;YAAC6G,OAAO,EAAC,QAAQ;YAACE,OAAO,EAAEA,CAAA,KAAMzB,kBAAkB,CAACjB,aAAa,CAACuB,EAAE,CAAE;YAAAO,QAAA,EAAC;UAE9E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEpB,CAAC;AAACjD,EAAA,CAtMID,WAAW;AAAAwE,IAAA,GAAXxE,WAAW;AAwMjB,eAAeA,WAAW;AAAC,IAAAxC,EAAA,EAAAG,GAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAS,GAAA,EAAAI,GAAA,EAAAyE,IAAA;AAAAC,YAAA,CAAAjH,EAAA;AAAAiH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}