import React, { useState } from 'react';
import styled from 'styled-components';
import Sidebar from '../components/layout/Sidebar';
import Header from '../components/layout/Header';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${props => props.theme.colors.backgroundSecondary};
`;

const SidebarContainer = styled.div`
  width: ${props => props.collapsed ? '60px' : '250px'};
  transition: width ${props => props.theme.transitions.normal};
  flex-shrink: 0;
`;

const MainContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const HeaderContainer = styled.div`
  height: 64px;
  flex-shrink: 0;
`;

const ContentContainer = styled.main`
  flex: 1;
  padding: ${props => props.theme.spacing.lg};
  overflow-y: auto;
`;

const DashboardLayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <LayoutContainer>
      <SidebarContainer collapsed={sidebarCollapsed}>
        <Sidebar collapsed={sidebarCollapsed} />
      </SidebarContainer>
      
      <MainContainer>
        <HeaderContainer>
          <Header 
            onToggleSidebar={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
          />
        </HeaderContainer>
        
        <ContentContainer>
          {children}
        </ContentContainer>
      </MainContainer>
    </LayoutContainer>
  );
};

export default DashboardLayout;
