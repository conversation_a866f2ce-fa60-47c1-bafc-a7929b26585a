{"ast": null, "code": "import { COMMENT, RULESET, DECLARATION } from './Enum.js';\nimport { abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof } from './Utility.js';\nimport { node, char, prev, next, peek, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter } from './Tokenizer.js';\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile(value) {\n  return dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value));\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse(value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n  var index = 0;\n  var offset = 0;\n  var length = pseudo;\n  var atrule = 0;\n  var property = 0;\n  var previous = 0;\n  var variable = 1;\n  var scanning = 1;\n  var ampersand = 1;\n  var character = 0;\n  var type = '';\n  var props = rules;\n  var children = rulesets;\n  var reference = rule;\n  var characters = type;\n  while (scanning) switch (previous = character, character = next()) {\n    // (\n    case 40:\n      if (previous != 108 && charat(characters, length - 1) == 58) {\n        if (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1) ampersand = -1;\n        break;\n      }\n    // \" ' [\n    case 34:\n    case 39:\n    case 91:\n      characters += delimit(character);\n      break;\n    // \\t \\n \\r \\s\n    case 9:\n    case 10:\n    case 13:\n    case 32:\n      characters += whitespace(previous);\n      break;\n    // \\\n    case 92:\n      characters += escaping(caret() - 1, 7);\n      continue;\n    // /\n    case 47:\n      switch (peek()) {\n        case 42:\n        case 47:\n          append(comment(commenter(next(), caret()), root, parent, declarations), declarations);\n          break;\n        default:\n          characters += '/';\n      }\n      break;\n    // {\n    case 123 * variable:\n      points[index++] = strlen(characters) * ampersand;\n    // } ; \\0\n    case 125 * variable:\n    case 59:\n    case 0:\n      switch (character) {\n        // \\0 }\n        case 0:\n        case 125:\n          scanning = 0;\n        // ;\n        case 59 + offset:\n          if (ampersand == -1) characters = replace(characters, /\\f/g, '');\n          if (property > 0 && strlen(characters) - length) append(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations);\n          break;\n        // @ ;\n        case 59:\n          characters += ';';\n        // { rule/at-rule\n        default:\n          append(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets);\n          if (character === 123) if (offset === 0) parse(characters, root, reference, reference, props, rulesets, length, points, children);else switch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n            // d l m s\n            case 100:\n            case 108:\n            case 109:\n            case 115:\n              parse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children);\n              break;\n            default:\n              parse(characters, reference, reference, reference, [''], children, 0, points, children);\n          }\n      }\n      index = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo;\n      break;\n    // :\n    case 58:\n      length = 1 + strlen(characters), property = previous;\n    default:\n      if (variable < 1) if (character == 123) --variable;else if (character == 125 && variable++ == 0 && prev() == 125) continue;\n      switch (characters += from(character), character * variable) {\n        // &\n        case 38:\n          ampersand = offset > 0 ? 1 : (characters += '\\f', -1);\n          break;\n        // ,\n        case 44:\n          points[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1;\n          break;\n        // @\n        case 64:\n          // -\n          if (peek() === 45) characters += delimit(next());\n          atrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++;\n          break;\n        // -\n        case 45:\n          if (previous === 45 && strlen(characters) == 2) variable = 0;\n      }\n  }\n  return rulesets;\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset(value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n  var post = offset - 1;\n  var rule = offset === 0 ? rules : [''];\n  var size = sizeof(rule);\n  for (var i = 0, j = 0, k = 0; i < index; ++i) for (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x) if (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x]))) props[k++] = z;\n  return node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings);\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment(value, root, parent, siblings) {\n  return node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings);\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration(value, root, parent, length, siblings) {\n  return node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings);\n}", "map": {"version": 3, "names": ["COMMENT", "RULESET", "DECLARATION", "abs", "charat", "trim", "from", "sizeof", "strlen", "substr", "append", "replace", "indexof", "node", "char", "prev", "next", "peek", "caret", "alloc", "dealloc", "delimit", "whitespace", "escaping", "identifier", "commenter", "compile", "value", "parse", "root", "parent", "rule", "rules", "rulesets", "pseudo", "points", "declarations", "index", "offset", "length", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "character", "type", "props", "children", "reference", "characters", "comment", "declaration", "ruleset", "siblings", "post", "size", "i", "j", "k", "x", "y", "z"], "sources": ["D:/Projects/qmsus/frontend/node_modules/stylis/src/Parser.js"], "sourcesContent": ["import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n"], "mappings": "AAAA,SAAQA,OAAO,EAAEC,OAAO,EAAEC,WAAW,QAAO,WAAW;AACvD,SAAQC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAO,cAAc;AACtG,SAAQC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAO,gBAAgB;;AAExI;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAAEC,KAAK,EAAE;EAC/B,OAAOP,OAAO,CAACQ,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAED,KAAK,GAAGR,KAAK,CAACQ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC;AACvF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAAED,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAE;EAChG,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAGL,MAAM;EACnB,IAAIM,MAAM,GAAG,CAAC;EACd,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,KAAK,GAAGhB,KAAK;EACjB,IAAIiB,QAAQ,GAAGhB,QAAQ;EACvB,IAAIiB,SAAS,GAAGnB,IAAI;EACpB,IAAIoB,UAAU,GAAGJ,IAAI;EAErB,OAAOH,QAAQ,EACd,QAAQF,QAAQ,GAAGI,SAAS,EAAEA,SAAS,GAAG9B,IAAI,CAAC,CAAC;IAC/C;IACA,KAAK,EAAE;MACN,IAAI0B,QAAQ,IAAI,GAAG,IAAItC,MAAM,CAAC+C,UAAU,EAAEZ,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAC5D,IAAI3B,OAAO,CAACuC,UAAU,IAAIxC,OAAO,CAACU,OAAO,CAACyB,SAAS,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE3C,GAAG,CAACkC,KAAK,GAAGF,MAAM,CAACE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAClHQ,SAAS,GAAG,CAAC,CAAC;QACf;MACD;IACD;IACA,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;MACxBM,UAAU,IAAI9B,OAAO,CAACyB,SAAS,CAAC;MAChC;IACD;IACA,KAAK,CAAC;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;MAChCK,UAAU,IAAI7B,UAAU,CAACoB,QAAQ,CAAC;MAClC;IACD;IACA,KAAK,EAAE;MACNS,UAAU,IAAI5B,QAAQ,CAACL,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACtC;IACD;IACA,KAAK,EAAE;MACN,QAAQD,IAAI,CAAC,CAAC;QACb,KAAK,EAAE;QAAE,KAAK,EAAE;UACfP,MAAM,CAAC0C,OAAO,CAAC3B,SAAS,CAACT,IAAI,CAAC,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC,EAAEW,IAAI,EAAEC,MAAM,EAAEM,YAAY,CAAC,EAAEA,YAAY,CAAC;UACrF;QACD;UACCe,UAAU,IAAI,GAAG;MACnB;MACA;IACD;IACA,KAAK,GAAG,GAAGR,QAAQ;MAClBR,MAAM,CAACE,KAAK,EAAE,CAAC,GAAG7B,MAAM,CAAC2C,UAAU,CAAC,GAAGN,SAAS;IACjD;IACA,KAAK,GAAG,GAAGF,QAAQ;IAAE,KAAK,EAAE;IAAE,KAAK,CAAC;MACnC,QAAQG,SAAS;QAChB;QACA,KAAK,CAAC;QAAE,KAAK,GAAG;UAAEF,QAAQ,GAAG,CAAC;QAC9B;QACA,KAAK,EAAE,GAAGN,MAAM;UAAE,IAAIO,SAAS,IAAI,CAAC,CAAC,EAAEM,UAAU,GAAGxC,OAAO,CAACwC,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC;UACjF,IAAIV,QAAQ,GAAG,CAAC,IAAKjC,MAAM,CAAC2C,UAAU,CAAC,GAAGZ,MAAO,EAChD7B,MAAM,CAAC+B,QAAQ,GAAG,EAAE,GAAGY,WAAW,CAACF,UAAU,GAAG,GAAG,EAAEpB,IAAI,EAAED,MAAM,EAAES,MAAM,GAAG,CAAC,EAAEH,YAAY,CAAC,GAAGiB,WAAW,CAAC1C,OAAO,CAACwC,UAAU,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAEpB,IAAI,EAAED,MAAM,EAAES,MAAM,GAAG,CAAC,EAAEH,YAAY,CAAC,EAAEA,YAAY,CAAC;UACtM;QACD;QACA,KAAK,EAAE;UAAEe,UAAU,IAAI,GAAG;QAC1B;QACA;UACCzC,MAAM,CAACwC,SAAS,GAAGI,OAAO,CAACH,UAAU,EAAEtB,IAAI,EAAEC,MAAM,EAAEO,KAAK,EAAEC,MAAM,EAAEN,KAAK,EAAEG,MAAM,EAAEY,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAEC,QAAQ,GAAG,EAAE,EAAEV,MAAM,EAAEN,QAAQ,CAAC,EAAEA,QAAQ,CAAC;UAEhJ,IAAIa,SAAS,KAAK,GAAG,EACpB,IAAIR,MAAM,KAAK,CAAC,EACfV,KAAK,CAACuB,UAAU,EAAEtB,IAAI,EAAEqB,SAAS,EAAEA,SAAS,EAAEF,KAAK,EAAEf,QAAQ,EAAEM,MAAM,EAAEJ,MAAM,EAAEc,QAAQ,CAAC,MAExF,QAAQT,MAAM,KAAK,EAAE,IAAIpC,MAAM,CAAC+C,UAAU,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAGX,MAAM;YACpE;YACA,KAAK,GAAG;YAAE,KAAK,GAAG;YAAE,KAAK,GAAG;YAAE,KAAK,GAAG;cACrCZ,KAAK,CAACD,KAAK,EAAEuB,SAAS,EAAEA,SAAS,EAAEnB,IAAI,IAAIrB,MAAM,CAAC4C,OAAO,CAAC3B,KAAK,EAAEuB,SAAS,EAAEA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAElB,KAAK,EAAEG,MAAM,EAAEY,IAAI,EAAEf,KAAK,EAAEgB,KAAK,GAAG,EAAE,EAAET,MAAM,EAAEU,QAAQ,CAAC,EAAEA,QAAQ,CAAC,EAAEjB,KAAK,EAAEiB,QAAQ,EAAEV,MAAM,EAAEJ,MAAM,EAAEJ,IAAI,GAAGiB,KAAK,GAAGC,QAAQ,CAAC;cAC5N;YACD;cACCrB,KAAK,CAACuB,UAAU,EAAED,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAED,QAAQ,EAAE,CAAC,EAAEd,MAAM,EAAEc,QAAQ,CAAC;UACzF;MACJ;MAEAZ,KAAK,GAAGC,MAAM,GAAGG,QAAQ,GAAG,CAAC,EAAEE,QAAQ,GAAGE,SAAS,GAAG,CAAC,EAAEE,IAAI,GAAGI,UAAU,GAAG,EAAE,EAAEZ,MAAM,GAAGL,MAAM;MAChG;IACD;IACA,KAAK,EAAE;MACNK,MAAM,GAAG,CAAC,GAAG/B,MAAM,CAAC2C,UAAU,CAAC,EAAEV,QAAQ,GAAGC,QAAQ;IACrD;MACC,IAAIC,QAAQ,GAAG,CAAC,EACf,IAAIG,SAAS,IAAI,GAAG,EACnB,EAAEH,QAAQ,MACN,IAAIG,SAAS,IAAI,GAAG,IAAIH,QAAQ,EAAE,IAAI,CAAC,IAAI5B,IAAI,CAAC,CAAC,IAAI,GAAG,EAC5D;MAEF,QAAQoC,UAAU,IAAI7C,IAAI,CAACwC,SAAS,CAAC,EAAEA,SAAS,GAAGH,QAAQ;QAC1D;QACA,KAAK,EAAE;UACNE,SAAS,GAAGP,MAAM,GAAG,CAAC,GAAG,CAAC,IAAIa,UAAU,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;UACrD;QACD;QACA,KAAK,EAAE;UACNhB,MAAM,CAACE,KAAK,EAAE,CAAC,GAAG,CAAC7B,MAAM,CAAC2C,UAAU,CAAC,GAAG,CAAC,IAAIN,SAAS,EAAEA,SAAS,GAAG,CAAC;UACrE;QACD;QACA,KAAK,EAAE;UACN;UACA,IAAI5B,IAAI,CAAC,CAAC,KAAK,EAAE,EAChBkC,UAAU,IAAI9B,OAAO,CAACL,IAAI,CAAC,CAAC,CAAC;UAE9BwB,MAAM,GAAGvB,IAAI,CAAC,CAAC,EAAEqB,MAAM,GAAGC,MAAM,GAAG/B,MAAM,CAACuC,IAAI,GAAGI,UAAU,IAAI3B,UAAU,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE4B,SAAS,EAAE;UAChG;QACD;QACA,KAAK,EAAE;UACN,IAAIJ,QAAQ,KAAK,EAAE,IAAIlC,MAAM,CAAC2C,UAAU,CAAC,IAAI,CAAC,EAC7CR,QAAQ,GAAG,CAAC;MACf;EACF;EAED,OAAOV,QAAQ;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,OAAOA,CAAE3B,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAEO,KAAK,EAAEC,MAAM,EAAEN,KAAK,EAAEG,MAAM,EAAEY,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEV,MAAM,EAAEgB,QAAQ,EAAE;EACpH,IAAIC,IAAI,GAAGlB,MAAM,GAAG,CAAC;EACrB,IAAIP,IAAI,GAAGO,MAAM,KAAK,CAAC,GAAGN,KAAK,GAAG,CAAC,EAAE,CAAC;EACtC,IAAIyB,IAAI,GAAGlD,MAAM,CAACwB,IAAI,CAAC;EAEvB,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGrB,KAAK,EAAE,EAAEqB,CAAC,EAC3C,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGrD,MAAM,CAACkB,KAAK,EAAE6B,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGrD,GAAG,CAACwD,CAAC,GAAGxB,MAAM,CAACuB,CAAC,CAAC,CAAC,CAAC,EAAEK,CAAC,GAAGpC,KAAK,EAAEkC,CAAC,GAAGJ,IAAI,EAAE,EAAEI,CAAC,EAC/F,IAAIE,CAAC,GAAG1D,IAAI,CAACsD,CAAC,GAAG,CAAC,GAAG5B,IAAI,CAAC8B,CAAC,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAGnD,OAAO,CAACmD,CAAC,EAAE,MAAM,EAAE/B,IAAI,CAAC8B,CAAC,CAAC,CAAC,CAAC,EACpEb,KAAK,CAACY,CAAC,EAAE,CAAC,GAAGG,CAAC;EAEjB,OAAOlD,IAAI,CAACc,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAEQ,MAAM,KAAK,CAAC,GAAGrC,OAAO,GAAG8C,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEV,MAAM,EAAEgB,QAAQ,CAAC;AACnG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASH,OAAOA,CAAEzB,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAEyB,QAAQ,EAAE;EACvD,OAAO1C,IAAI,CAACc,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAE9B,OAAO,EAAEM,IAAI,CAACQ,IAAI,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACkB,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE4B,QAAQ,CAAC;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASF,WAAWA,CAAE1B,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAES,MAAM,EAAEgB,QAAQ,EAAE;EACnE,OAAO1C,IAAI,CAACc,KAAK,EAAEE,IAAI,EAAEC,MAAM,EAAE5B,WAAW,EAAEO,MAAM,CAACkB,KAAK,EAAE,CAAC,EAAEY,MAAM,CAAC,EAAE9B,MAAM,CAACkB,KAAK,EAAEY,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEA,MAAM,EAAEgB,QAAQ,CAAC;AACzH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}