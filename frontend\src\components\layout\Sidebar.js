import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiHome, 
  FiSettings, 
  FiUsers, 
  FiShield, 
  FiGrid,
  FiChevronRight 
} from 'react-icons/fi';

const SidebarContainer = styled.div`
  height: 100vh;
  background-color: ${props => props.theme.colors.sidebarBg};
  color: ${props => props.theme.colors.sidebarText};
  display: flex;
  flex-direction: column;
  position: fixed;
  width: ${props => props.collapsed ? '60px' : '250px'};
  transition: width ${props => props.theme.transitions.normal};
  z-index: 1000;
`;

const Logo = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.sidebarHover};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const LogoText = styled.h2`
  font-size: ${props => props.theme.fontSize.xl};
  font-weight: ${props => props.theme.fontWeight.bold};
  margin: 0;
  color: ${props => props.theme.colors.sidebarTextActive};
  opacity: ${props => props.collapsed ? 0 : 1};
  transition: opacity ${props => props.theme.transitions.fast};
`;

const LogoIcon = styled.div`
  width: 32px;
  height: 32px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
`;

const Navigation = styled.nav`
  flex: 1;
  padding: ${props => props.theme.spacing.md} 0;
`;

const NavSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const NavSectionTitle = styled.div`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  font-size: ${props => props.theme.fontSize.xs};
  font-weight: ${props => props.theme.fontWeight.semibold};
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: ${props => props.theme.colors.textMuted};
  opacity: ${props => props.collapsed ? 0 : 1};
  transition: opacity ${props => props.theme.transitions.fast};
`;

const NavItem = styled(NavLink)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.sidebarText};
  text-decoration: none;
  transition: all ${props => props.theme.transitions.fast};
  position: relative;

  &:hover {
    background-color: ${props => props.theme.colors.sidebarHover};
    color: ${props => props.theme.colors.sidebarTextActive};
  }

  &.active {
    background-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.textInverse};
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: ${props => props.theme.colors.textInverse};
    }
  }
`;

const NavIcon = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`;

const NavText = styled.span`
  opacity: ${props => props.collapsed ? 0 : 1};
  transition: opacity ${props => props.theme.transitions.fast};
  white-space: nowrap;
`;

const NavArrow = styled.div`
  margin-left: auto;
  opacity: ${props => props.collapsed ? 0 : 1};
  transition: all ${props => props.theme.transitions.fast};
  transform: ${props => props.expanded ? 'rotate(90deg)' : 'rotate(0deg)'};
`;

const SubNavItem = styled(NavLink)`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  padding-left: ${props => props.theme.spacing.xxl};
  color: ${props => props.theme.colors.sidebarText};
  text-decoration: none;
  font-size: ${props => props.theme.fontSize.sm};
  transition: all ${props => props.theme.transitions.fast};
  opacity: ${props => props.collapsed ? 0 : 1};

  &:hover {
    background-color: ${props => props.theme.colors.sidebarHover};
    color: ${props => props.theme.colors.sidebarTextActive};
  }

  &.active {
    background-color: ${props => props.theme.colors.primary}20;
    color: ${props => props.theme.colors.primary};
  }
`;

const Sidebar = ({ collapsed }) => {
  const location = useLocation();
  const [expandedSections, setExpandedSections] = React.useState(['settings']);

  const toggleSection = (section) => {
    if (collapsed) return;
    
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const menuItems = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: FiHome,
      path: '/dashboard'
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: FiSettings,
      path: '/settings',
      children: [
        { title: 'Users', path: '/settings/users', icon: FiUsers },
        { title: 'Roles', path: '/settings/roles', icon: FiShield },
        { title: 'Modules', path: '/settings/modules', icon: FiGrid }
      ]
    }
  ];

  return (
    <SidebarContainer collapsed={collapsed}>
      <Logo>
        <LogoIcon>Q</LogoIcon>
        <LogoText>QMS</LogoText>
      </Logo>

      <Navigation>
        <NavSection>
          {!collapsed && <NavSectionTitle>Main</NavSectionTitle>}
          
          {menuItems.map(item => (
            <div key={item.id}>
              {item.children ? (
                <>
                  <NavItem
                    as="div"
                    onClick={() => toggleSection(item.id)}
                    style={{ cursor: 'pointer' }}
                  >
                    <NavIcon>
                      <item.icon size={20} />
                    </NavIcon>
                    <NavText>{item.title}</NavText>
                    <NavArrow expanded={expandedSections.includes(item.id)}>
                      <FiChevronRight size={16} />
                    </NavArrow>
                  </NavItem>
                  
                  {expandedSections.includes(item.id) && item.children.map(child => (
                    <SubNavItem
                      key={child.path}
                      to={child.path}
                      className={({ isActive }) => isActive ? 'active' : ''}
                    >
                      {child.title}
                    </SubNavItem>
                  ))}
                </>
              ) : (
                <NavItem
                  to={item.path}
                  className={({ isActive }) => isActive ? 'active' : ''}
                >
                  <NavIcon>
                    <item.icon size={20} />
                  </NavIcon>
                  <NavText>{item.title}</NavText>
                </NavItem>
              )}
            </div>
          ))}
        </NavSection>
      </Navigation>
    </SidebarContainer>
  );
};

export default Sidebar;
