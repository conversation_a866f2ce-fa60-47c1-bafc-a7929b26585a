{"ast": null, "code": "import api from './axios';\nexport const authAPI = {\n  login: (email, password) => {\n    return api.post('/auth/login', {\n      email,\n      password\n    });\n  },\n  logout: () => {\n    return api.post('/auth/logout');\n  },\n  getProfile: () => {\n    return api.get('/auth/profile');\n  }\n};", "map": {"version": 3, "names": ["api", "authAPI", "login", "email", "password", "post", "logout", "getProfile", "get"], "sources": ["D:/Projects/qmsus/frontend/src/api/auth.js"], "sourcesContent": ["import api from './axios';\n\nexport const authAPI = {\n  login: (email, password) => {\n    return api.post('/auth/login', { email, password });\n  },\n\n  logout: () => {\n    return api.post('/auth/logout');\n  },\n\n  getProfile: () => {\n    return api.get('/auth/profile');\n  }\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,SAAS;AAEzB,OAAO,MAAMC,OAAO,GAAG;EACrBC,KAAK,EAAEA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC1B,OAAOJ,GAAG,CAACK,IAAI,CAAC,aAAa,EAAE;MAAEF,KAAK;MAAEC;IAAS,CAAC,CAAC;EACrD,CAAC;EAEDE,MAAM,EAAEA,CAAA,KAAM;IACZ,OAAON,GAAG,CAACK,IAAI,CAAC,cAAc,CAAC;EACjC,CAAC;EAEDE,UAAU,EAAEA,CAAA,KAAM;IAChB,OAAOP,GAAG,CAACQ,GAAG,CAAC,eAAe,CAAC;EACjC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}