[{"D:\\Projects\\qmsus\\frontend\\src\\index.js": "1", "D:\\Projects\\qmsus\\frontend\\src\\App.js": "2", "D:\\Projects\\qmsus\\frontend\\src\\pages\\Dashboard.js": "3", "D:\\Projects\\qmsus\\frontend\\src\\context\\AuthContext.js": "4", "D:\\Projects\\qmsus\\frontend\\src\\styles\\GlobalStyles.js": "5", "D:\\Projects\\qmsus\\frontend\\src\\layouts\\DashboardLayout.js": "6", "D:\\Projects\\qmsus\\frontend\\src\\context\\ThemeContext.js": "7", "D:\\Projects\\qmsus\\frontend\\src\\pages\\Login.js": "8", "D:\\Projects\\qmsus\\frontend\\src\\pages\\settings\\UsersPage.js": "9", "D:\\Projects\\qmsus\\frontend\\src\\pages\\settings\\ModulesPage.js": "10", "D:\\Projects\\qmsus\\frontend\\src\\pages\\settings\\RolesPage.js": "11", "D:\\Projects\\qmsus\\frontend\\src\\components\\auth\\ProtectedRoute.js": "12", "D:\\Projects\\qmsus\\frontend\\src\\api\\auth.js": "13", "D:\\Projects\\qmsus\\frontend\\src\\api\\users.js": "14", "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Card.js": "15", "D:\\Projects\\qmsus\\frontend\\src\\api\\modules.js": "16", "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Input.js": "17", "D:\\Projects\\qmsus\\frontend\\src\\api\\roles.js": "18", "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Button.js": "19", "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Modal.js": "20", "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Table.js": "21", "D:\\Projects\\qmsus\\frontend\\src\\theme\\theme.js": "22", "D:\\Projects\\qmsus\\frontend\\src\\components\\layout\\Header.js": "23", "D:\\Projects\\qmsus\\frontend\\src\\components\\layout\\Sidebar.js": "24", "D:\\Projects\\qmsus\\frontend\\src\\components\\forms\\UserForm.js": "25", "D:\\Projects\\qmsus\\frontend\\src\\components\\forms\\RoleForm.js": "26", "D:\\Projects\\qmsus\\frontend\\src\\components\\forms\\ModuleForm.js": "27", "D:\\Projects\\qmsus\\frontend\\src\\api\\axios.js": "28"}, {"size": 232, "mtime": 1756578788070, "results": "29", "hashOfConfig": "30"}, {"size": 2848, "mtime": 1756578465112, "results": "31", "hashOfConfig": "30"}, {"size": 6120, "mtime": 1756578533851, "results": "32", "hashOfConfig": "30"}, {"size": 3612, "mtime": 1756578179507, "results": "33", "hashOfConfig": "30"}, {"size": 3011, "mtime": 1756578498542, "results": "34", "hashOfConfig": "30"}, {"size": 1554, "mtime": 1756578383051, "results": "35", "hashOfConfig": "30"}, {"size": 1257, "mtime": 1756578194821, "results": "36", "hashOfConfig": "30"}, {"size": 5251, "mtime": 1756636292316, "results": "37", "hashOfConfig": "30"}, {"size": 8137, "mtime": 1756578580578, "results": "38", "hashOfConfig": "30"}, {"size": 7949, "mtime": 1756578744725, "results": "39", "hashOfConfig": "30"}, {"size": 7653, "mtime": 1756578679761, "results": "40", "hashOfConfig": "30"}, {"size": 1044, "mtime": 1756578478299, "results": "41", "hashOfConfig": "30"}, {"size": 275, "mtime": 1756578216317, "results": "42", "hashOfConfig": "30"}, {"size": 433, "mtime": 1756578225254, "results": "43", "hashOfConfig": "30"}, {"size": 2098, "mtime": 1756578305244, "results": "44", "hashOfConfig": "30"}, {"size": 463, "mtime": 1756578244274, "results": "45", "hashOfConfig": "30"}, {"size": 2615, "mtime": 1756578290208, "results": "46", "hashOfConfig": "30"}, {"size": 433, "mtime": 1756578234457, "results": "47", "hashOfConfig": "30"}, {"size": 3170, "mtime": 1756578272583, "results": "48", "hashOfConfig": "30"}, {"size": 2812, "mtime": 1756578602509, "results": "49", "hashOfConfig": "30"}, {"size": 3033, "mtime": 1756578338839, "results": "50", "hashOfConfig": "30"}, {"size": 2507, "mtime": 1756578156786, "results": "51", "hashOfConfig": "30"}, {"size": 8108, "mtime": 1756636913177, "results": "52", "hashOfConfig": "30"}, {"size": 6915, "mtime": 1756578413178, "results": "53", "hashOfConfig": "30"}, {"size": 7705, "mtime": 1756578640022, "results": "54", "hashOfConfig": "30"}, {"size": 7965, "mtime": 1756578713284, "results": "55", "hashOfConfig": "30"}, {"size": 8693, "mtime": 1756578777788, "results": "56", "hashOfConfig": "30"}, {"size": 1720, "mtime": 1756578207942, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xwqjfj", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\qmsus\\frontend\\src\\index.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\App.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\pages\\Dashboard.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\context\\AuthContext.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\styles\\GlobalStyles.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\layouts\\DashboardLayout.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\context\\ThemeContext.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\pages\\Login.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\pages\\settings\\UsersPage.js", ["142", "143"], [], "D:\\Projects\\qmsus\\frontend\\src\\pages\\settings\\ModulesPage.js", ["144", "145"], [], "D:\\Projects\\qmsus\\frontend\\src\\pages\\settings\\RolesPage.js", ["146", "147"], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\api\\auth.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\api\\users.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Card.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\api\\modules.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Input.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\api\\roles.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Button.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Modal.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\common\\Table.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\theme\\theme.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\layout\\Header.js", ["148"], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\layout\\Sidebar.js", ["149"], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\forms\\UserForm.js", ["150", "151"], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\forms\\RoleForm.js", [], [], "D:\\Projects\\qmsus\\frontend\\src\\components\\forms\\ModuleForm.js", ["152"], [], "D:\\Projects\\qmsus\\frontend\\src\\api\\axios.js", [], [], {"ruleId": "153", "severity": 1, "message": "154", "line": 101, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 101, "endColumn": 20}, {"ruleId": "157", "severity": 1, "message": "158", "line": 109, "column": 6, "nodeType": "159", "endLine": 109, "endColumn": 31, "suggestions": "160"}, {"ruleId": "153", "severity": 1, "message": "154", "line": 99, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 99, "endColumn": 20}, {"ruleId": "157", "severity": 1, "message": "161", "line": 106, "column": 6, "nodeType": "159", "endLine": 106, "endColumn": 31, "suggestions": "162"}, {"ruleId": "153", "severity": 1, "message": "154", "line": 98, "column": 10, "nodeType": "155", "messageId": "156", "endLine": 98, "endColumn": 20}, {"ruleId": "157", "severity": 1, "message": "163", "line": 105, "column": 6, "nodeType": "159", "endLine": 105, "endColumn": 31, "suggestions": "164"}, {"ruleId": "153", "severity": 1, "message": "165", "line": 13, "column": 8, "nodeType": "155", "messageId": "156", "endLine": 13, "endColumn": 14}, {"ruleId": "153", "severity": 1, "message": "166", "line": 151, "column": 9, "nodeType": "155", "messageId": "156", "endLine": 151, "endColumn": 17}, {"ruleId": "153", "severity": 1, "message": "167", "line": 34, "column": 7, "nodeType": "155", "messageId": "156", "endLine": 34, "endColumn": 13}, {"ruleId": "153", "severity": 1, "message": "168", "line": 100, "column": 7, "nodeType": "155", "messageId": "156", "endLine": 100, "endColumn": 19}, {"ruleId": "153", "severity": 1, "message": "168", "line": 113, "column": 7, "nodeType": "155", "messageId": "156", "endLine": 113, "endColumn": 19}, "no-unused-vars", "'totalPages' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["169"], "React Hook useEffect has a missing dependency: 'fetchModules'. Either include it or remove the dependency array.", ["170"], "React Hook useEffect has a missing dependency: 'fetchRoles'. Either include it or remove the dependency array.", ["171"], "'Button' is defined but never used.", "'location' is assigned a value but never used.", "'Select' is assigned a value but never used.", "'ErrorMessage' is assigned a value but never used.", {"desc": "172", "fix": "173"}, {"desc": "174", "fix": "175"}, {"desc": "176", "fix": "177"}, "Update the dependencies array to be: [currentPage, fetchUsers, searchTerm]", {"range": "178", "text": "179"}, "Update the dependencies array to be: [currentPage, fetchModules, searchTerm]", {"range": "180", "text": "181"}, "Update the dependencies array to be: [currentPage, fetchRoles, searchTerm]", {"range": "182", "text": "183"}, [3282, 3307], "[currentPage, fetchUsers, searchTerm]", [3194, 3219], "[currentPage, fetchModules, searchTerm]", [3148, 3173], "[currentPage, fetchRoles, searchTerm]"]