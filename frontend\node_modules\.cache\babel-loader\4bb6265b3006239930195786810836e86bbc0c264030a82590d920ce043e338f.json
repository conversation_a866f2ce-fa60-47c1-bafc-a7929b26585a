{"ast": null, "code": "import axios from 'axios';\nimport { toast } from 'react-toastify';\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor for error handling\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  const {\n    response\n  } = error;\n  if (response) {\n    const {\n      status,\n      data\n    } = response;\n\n    // Handle authentication errors\n    if (status === 401) {\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n      return Promise.reject(error);\n    }\n\n    // Handle validation errors\n    if (status === 400 && data.errors) {\n      const errorMessages = data.errors.map(err => err.msg).join(', ');\n      toast.error(errorMessages);\n      return Promise.reject(error);\n    }\n\n    // Handle other errors\n    if (data.message) {\n      toast.error(data.message);\n    } else {\n      toast.error('An unexpected error occurred');\n    }\n  } else if (error.request) {\n    // Network error\n    toast.error('Network error. Please check your connection.');\n  } else {\n    // Other error\n    toast.error('An unexpected error occurred');\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "toast", "API_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "status", "data", "removeItem", "window", "location", "href", "errors", "errorMessages", "map", "err", "msg", "join", "message"], "sources": ["D:/Projects/qmsus/frontend/src/api/axios.js"], "sourcesContent": ["import axios from 'axios';\nimport { toast } from 'react-toastify';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    const { response } = error;\n    \n    if (response) {\n      const { status, data } = response;\n      \n      // Handle authentication errors\n      if (status === 401) {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n        return Promise.reject(error);\n      }\n      \n      // Handle validation errors\n      if (status === 400 && data.errors) {\n        const errorMessages = data.errors.map(err => err.msg).join(', ');\n        toast.error(errorMessages);\n        return Promise.reject(error);\n      }\n      \n      // Handle other errors\n      if (data.message) {\n        toast.error(data.message);\n      } else {\n        toast.error('An unexpected error occurred');\n      }\n    } else if (error.request) {\n      // Network error\n      toast.error('Network error. Please check your connection.');\n    } else {\n      // Other error\n      toast.error('An unexpected error occurred');\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAE5E;AACA,MAAMC,GAAG,GAAGN,KAAK,CAACO,MAAM,CAAC;EACvBC,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT,MAAM;IAAEG;EAAS,CAAC,GAAGH,KAAK;EAE1B,IAAIG,QAAQ,EAAE;IACZ,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,QAAQ;;IAEjC;IACA,IAAIC,MAAM,KAAK,GAAG,EAAE;MAClBP,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B;;IAEA;IACA,IAAII,MAAM,KAAK,GAAG,IAAIC,IAAI,CAACK,MAAM,EAAE;MACjC,MAAMC,aAAa,GAAGN,IAAI,CAACK,MAAM,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAChEjC,KAAK,CAACkB,KAAK,CAACW,aAAa,CAAC;MAC1B,OAAOV,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B;;IAEA;IACA,IAAIK,IAAI,CAACW,OAAO,EAAE;MAChBlC,KAAK,CAACkB,KAAK,CAACK,IAAI,CAACW,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLlC,KAAK,CAACkB,KAAK,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC,MAAM,IAAIA,KAAK,CAACP,OAAO,EAAE;IACxB;IACAX,KAAK,CAACkB,KAAK,CAAC,8CAA8C,CAAC;EAC7D,CAAC,MAAM;IACL;IACAlB,KAAK,CAACkB,KAAK,CAAC,8BAA8B,CAAC;EAC7C;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}