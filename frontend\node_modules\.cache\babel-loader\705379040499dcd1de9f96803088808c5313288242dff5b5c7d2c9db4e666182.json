{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\components\\\\forms\\\\RoleForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Button from '../common/Button';\nimport Input from '../common/Input';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.lg};\n`;\n_c = Form;\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.xs};\n`;\n_c2 = FormGroup;\nconst Label = styled.label`\n  font-size: ${props => props.theme.fontSize.sm};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n`;\n_c3 = Label;\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSize.sm};\n  color: ${props => props.theme.colors.textPrimary};\n  background-color: ${props => props.theme.colors.background};\n  transition: all ${props => props.theme.transitions.fast};\n  resize: vertical;\n  font-family: inherit;\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;\n  }\n\n  &::placeholder {\n    color: ${props => props.theme.colors.textMuted};\n  }\n`;\n_c4 = TextArea;\nconst PermissionsSection = styled.div`\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.lg};\n`;\n_c5 = PermissionsSection;\nconst PermissionsTitle = styled.h3`\n  font-size: ${props => props.theme.fontSize.lg};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0 0 ${props => props.theme.spacing.md} 0;\n`;\n_c6 = PermissionsTitle;\nconst PermissionGroup = styled.div`\n  margin-bottom: ${props => props.theme.spacing.lg};\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n_c7 = PermissionGroup;\nconst PermissionGroupTitle = styled.h4`\n  font-size: ${props => props.theme.fontSize.base};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0 0 ${props => props.theme.spacing.sm} 0;\n`;\n_c8 = PermissionGroupTitle;\nconst PermissionsList = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${props => props.theme.spacing.sm};\n`;\n_c9 = PermissionsList;\nconst CheckboxItem = styled.label`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n  cursor: pointer;\n  padding: ${props => props.theme.spacing.xs};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  transition: background-color ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n  }\n`;\n_c0 = CheckboxItem;\nconst Checkbox = styled.input`\n  width: 16px;\n  height: 16px;\n  accent-color: ${props => props.theme.colors.primary};\n`;\n_c1 = Checkbox;\nconst CheckboxLabel = styled.span`\n  font-size: ${props => props.theme.fontSize.sm};\n  color: ${props => props.theme.colors.textPrimary};\n`;\n_c10 = CheckboxLabel;\nconst FormActions = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  justify-content: flex-end;\n  margin-top: ${props => props.theme.spacing.lg};\n`;\n_c11 = FormActions;\nconst RoleForm = ({\n  role,\n  onSubmit,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    permissions: {},\n    isActive: true\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Available permissions grouped by module\n  const availablePermissions = {\n    'Users': [{\n      key: 'users.create',\n      label: 'Create Users'\n    }, {\n      key: 'users.read',\n      label: 'View Users'\n    }, {\n      key: 'users.update',\n      label: 'Update Users'\n    }, {\n      key: 'users.delete',\n      label: 'Delete Users'\n    }],\n    'Roles': [{\n      key: 'roles.create',\n      label: 'Create Roles'\n    }, {\n      key: 'roles.read',\n      label: 'View Roles'\n    }, {\n      key: 'roles.update',\n      label: 'Update Roles'\n    }, {\n      key: 'roles.delete',\n      label: 'Delete Roles'\n    }],\n    'Modules': [{\n      key: 'modules.create',\n      label: 'Create Modules'\n    }, {\n      key: 'modules.read',\n      label: 'View Modules'\n    }, {\n      key: 'modules.update',\n      label: 'Update Modules'\n    }, {\n      key: 'modules.delete',\n      label: 'Delete Modules'\n    }]\n  };\n  useEffect(() => {\n    if (role) {\n      setFormData({\n        name: role.name || '',\n        description: role.description || '',\n        permissions: role.permissions || {},\n        isActive: role.isActive !== undefined ? role.isActive : true\n      });\n    }\n  }, [role]);\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handlePermissionChange = (permissionKey, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      permissions: {\n        ...prev.permissions,\n        [permissionKey]: checked\n      }\n    }));\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Role name is required';\n    }\n    return newErrors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n    setLoading(true);\n    setErrors({});\n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      console.error('Form submission error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(Input, {\n      label: \"Role Name\",\n      name: \"name\",\n      value: formData.name,\n      onChange: handleChange,\n      error: errors.name,\n      placeholder: \"Enter role name\",\n      required: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        htmlFor: \"description\",\n        children: \"Description\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n        id: \"description\",\n        name: \"description\",\n        value: formData.description,\n        onChange: handleChange,\n        placeholder: \"Enter role description (optional)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PermissionsSection, {\n      children: [/*#__PURE__*/_jsxDEV(PermissionsTitle, {\n        children: \"Permissions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), Object.entries(availablePermissions).map(([groupName, permissions]) => /*#__PURE__*/_jsxDEV(PermissionGroup, {\n        children: [/*#__PURE__*/_jsxDEV(PermissionGroupTitle, {\n          children: groupName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(PermissionsList, {\n          children: permissions.map(permission => /*#__PURE__*/_jsxDEV(CheckboxItem, {\n            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n              type: \"checkbox\",\n              checked: formData.permissions[permission.key] || false,\n              onChange: e => handlePermissionChange(permission.key, e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CheckboxLabel, {\n              children: permission.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this)]\n          }, permission.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)]\n      }, groupName, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n      children: /*#__PURE__*/_jsxDEV(CheckboxItem, {\n        children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n          type: \"checkbox\",\n          name: \"isActive\",\n          checked: formData.isActive,\n          onChange: handleChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CheckboxLabel, {\n          children: \"Active Role\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"button\",\n        variant: \"secondary\",\n        onClick: onCancel,\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"primary\",\n        loading: loading,\n        disabled: loading,\n        children: role ? 'Update Role' : 'Create Role'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleForm, \"royAIWMyAiaaDE3VMFIwdFlYUZA=\");\n_c12 = RoleForm;\nexport default RoleForm;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"Form\");\n$RefreshReg$(_c2, \"FormGroup\");\n$RefreshReg$(_c3, \"Label\");\n$RefreshReg$(_c4, \"TextArea\");\n$RefreshReg$(_c5, \"PermissionsSection\");\n$RefreshReg$(_c6, \"PermissionsTitle\");\n$RefreshReg$(_c7, \"PermissionGroup\");\n$RefreshReg$(_c8, \"PermissionGroupTitle\");\n$RefreshReg$(_c9, \"PermissionsList\");\n$RefreshReg$(_c0, \"CheckboxItem\");\n$RefreshReg$(_c1, \"Checkbox\");\n$RefreshReg$(_c10, \"CheckboxLabel\");\n$RefreshReg$(_c11, \"FormActions\");\n$RefreshReg$(_c12, \"RoleForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "<PERSON><PERSON>", "Input", "jsxDEV", "_jsxDEV", "Form", "form", "props", "theme", "spacing", "lg", "_c", "FormGroup", "div", "xs", "_c2", "Label", "label", "fontSize", "sm", "fontWeight", "medium", "colors", "textPrimary", "_c3", "TextArea", "textarea", "md", "border", "borderRadius", "background", "transitions", "fast", "primary", "textMuted", "_c4", "PermissionsSection", "_c5", "PermissionsTitle", "h3", "semibold", "_c6", "PermissionGroup", "_c7", "PermissionGroupTitle", "h4", "base", "_c8", "PermissionsList", "_c9", "CheckboxItem", "backgroundSecondary", "_c0", "Checkbox", "input", "_c1", "CheckboxLabel", "span", "_c10", "FormActions", "_c11", "RoleForm", "role", "onSubmit", "onCancel", "_s", "formData", "setFormData", "name", "description", "permissions", "isActive", "errors", "setErrors", "loading", "setLoading", "availablePermissions", "key", "undefined", "handleChange", "e", "value", "type", "checked", "target", "prev", "handlePermissionChange", "<PERSON><PERSON><PERSON>", "validateForm", "newErrors", "trim", "handleSubmit", "preventDefault", "Object", "keys", "length", "error", "console", "children", "onChange", "placeholder", "required", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "entries", "map", "groupName", "permission", "variant", "onClick", "disabled", "_c12", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/components/forms/RoleForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Button from '../common/Button';\nimport Input from '../common/Input';\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.lg};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${props => props.theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${props => props.theme.fontSize.sm};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  font-size: ${props => props.theme.fontSize.sm};\n  color: ${props => props.theme.colors.textPrimary};\n  background-color: ${props => props.theme.colors.background};\n  transition: all ${props => props.theme.transitions.fast};\n  resize: vertical;\n  font-family: inherit;\n\n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;\n  }\n\n  &::placeholder {\n    color: ${props => props.theme.colors.textMuted};\n  }\n`;\n\nconst PermissionsSection = styled.div`\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: ${props => props.theme.borderRadius.md};\n  padding: ${props => props.theme.spacing.lg};\n`;\n\nconst PermissionsTitle = styled.h3`\n  font-size: ${props => props.theme.fontSize.lg};\n  font-weight: ${props => props.theme.fontWeight.semibold};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0 0 ${props => props.theme.spacing.md} 0;\n`;\n\nconst PermissionGroup = styled.div`\n  margin-bottom: ${props => props.theme.spacing.lg};\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst PermissionGroupTitle = styled.h4`\n  font-size: ${props => props.theme.fontSize.base};\n  font-weight: ${props => props.theme.fontWeight.medium};\n  color: ${props => props.theme.colors.textPrimary};\n  margin: 0 0 ${props => props.theme.spacing.sm} 0;\n`;\n\nconst PermissionsList = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${props => props.theme.spacing.sm};\n`;\n\nconst CheckboxItem = styled.label`\n  display: flex;\n  align-items: center;\n  gap: ${props => props.theme.spacing.sm};\n  cursor: pointer;\n  padding: ${props => props.theme.spacing.xs};\n  border-radius: ${props => props.theme.borderRadius.sm};\n  transition: background-color ${props => props.theme.transitions.fast};\n\n  &:hover {\n    background-color: ${props => props.theme.colors.backgroundSecondary};\n  }\n`;\n\nconst Checkbox = styled.input`\n  width: 16px;\n  height: 16px;\n  accent-color: ${props => props.theme.colors.primary};\n`;\n\nconst CheckboxLabel = styled.span`\n  font-size: ${props => props.theme.fontSize.sm};\n  color: ${props => props.theme.colors.textPrimary};\n`;\n\nconst FormActions = styled.div`\n  display: flex;\n  gap: ${props => props.theme.spacing.md};\n  justify-content: flex-end;\n  margin-top: ${props => props.theme.spacing.lg};\n`;\n\nconst RoleForm = ({ role, onSubmit, onCancel }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    permissions: {},\n    isActive: true\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Available permissions grouped by module\n  const availablePermissions = {\n    'Users': [\n      { key: 'users.create', label: 'Create Users' },\n      { key: 'users.read', label: 'View Users' },\n      { key: 'users.update', label: 'Update Users' },\n      { key: 'users.delete', label: 'Delete Users' }\n    ],\n    'Roles': [\n      { key: 'roles.create', label: 'Create Roles' },\n      { key: 'roles.read', label: 'View Roles' },\n      { key: 'roles.update', label: 'Update Roles' },\n      { key: 'roles.delete', label: 'Delete Roles' }\n    ],\n    'Modules': [\n      { key: 'modules.create', label: 'Create Modules' },\n      { key: 'modules.read', label: 'View Modules' },\n      { key: 'modules.update', label: 'Update Modules' },\n      { key: 'modules.delete', label: 'Delete Modules' }\n    ]\n  };\n\n  useEffect(() => {\n    if (role) {\n      setFormData({\n        name: role.name || '',\n        description: role.description || '',\n        permissions: role.permissions || {},\n        isActive: role.isActive !== undefined ? role.isActive : true\n      });\n    }\n  }, [role]);\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handlePermissionChange = (permissionKey, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      permissions: {\n        ...prev.permissions,\n        [permissionKey]: checked\n      }\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Role name is required';\n    }\n\n    return newErrors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const newErrors = validateForm();\n    if (Object.keys(newErrors).length > 0) {\n      setErrors(newErrors);\n      return;\n    }\n\n    setLoading(true);\n    setErrors({});\n\n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      console.error('Form submission error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Form onSubmit={handleSubmit}>\n      <Input\n        label=\"Role Name\"\n        name=\"name\"\n        value={formData.name}\n        onChange={handleChange}\n        error={errors.name}\n        placeholder=\"Enter role name\"\n        required\n      />\n\n      <FormGroup>\n        <Label htmlFor=\"description\">Description</Label>\n        <TextArea\n          id=\"description\"\n          name=\"description\"\n          value={formData.description}\n          onChange={handleChange}\n          placeholder=\"Enter role description (optional)\"\n        />\n      </FormGroup>\n\n      <PermissionsSection>\n        <PermissionsTitle>Permissions</PermissionsTitle>\n        {Object.entries(availablePermissions).map(([groupName, permissions]) => (\n          <PermissionGroup key={groupName}>\n            <PermissionGroupTitle>{groupName}</PermissionGroupTitle>\n            <PermissionsList>\n              {permissions.map(permission => (\n                <CheckboxItem key={permission.key}>\n                  <Checkbox\n                    type=\"checkbox\"\n                    checked={formData.permissions[permission.key] || false}\n                    onChange={(e) => handlePermissionChange(permission.key, e.target.checked)}\n                  />\n                  <CheckboxLabel>{permission.label}</CheckboxLabel>\n                </CheckboxItem>\n              ))}\n            </PermissionsList>\n          </PermissionGroup>\n        ))}\n      </PermissionsSection>\n\n      <FormGroup>\n        <CheckboxItem>\n          <Checkbox\n            type=\"checkbox\"\n            name=\"isActive\"\n            checked={formData.isActive}\n            onChange={handleChange}\n          />\n          <CheckboxLabel>Active Role</CheckboxLabel>\n        </CheckboxItem>\n      </FormGroup>\n\n      <FormActions>\n        <Button\n          type=\"button\"\n          variant=\"secondary\"\n          onClick={onCancel}\n          disabled={loading}\n        >\n          Cancel\n        </Button>\n        <Button\n          type=\"submit\"\n          variant=\"primary\"\n          loading={loading}\n          disabled={loading}\n        >\n          {role ? 'Update Role' : 'Create Role'}\n        </Button>\n      </FormActions>\n    </Form>\n  );\n};\n\nexport default RoleForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,IAAI,GAAGL,MAAM,CAACM,IAAI;AACxB;AACA;AACA,SAASC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AACxC,CAAC;AAACC,EAAA,GAJIN,IAAI;AAMV,MAAMO,SAAS,GAAGZ,MAAM,CAACa,GAAG;AAC5B;AACA;AACA,SAASN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACK,EAAE;AACxC,CAAC;AAACC,GAAA,GAJIH,SAAS;AAMf,MAAMI,KAAK,GAAGhB,MAAM,CAACiB,KAAK;AAC1B,eAAeV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,QAAQ,CAACC,EAAE;AAC/C,iBAAiBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,UAAU,CAACC,MAAM;AACvD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACC,WAAW;AAClD,CAAC;AAACC,GAAA,GAJIR,KAAK;AAMX,MAAMS,QAAQ,GAAGzB,MAAM,CAAC0B,QAAQ;AAChC;AACA;AACA,aAAanB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE,IAAIZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACkB,EAAE;AAC/E,sBAAsBpB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACM,MAAM;AACxD,mBAAmBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqB,YAAY,CAACF,EAAE;AACvD,eAAepB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,QAAQ,CAACC,EAAE;AAC/C,WAAWZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACC,WAAW;AAClD,sBAAsBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACQ,UAAU;AAC5D,oBAAoBvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuB,WAAW,CAACC,IAAI;AACzD;AACA;AACA;AACA;AACA;AACA,oBAAoBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACW,OAAO;AACvD,4BAA4B1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACW,OAAO;AAC/D;AACA;AACA;AACA,aAAa1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACY,SAAS;AAClD;AACA,CAAC;AAACC,GAAA,GAtBIV,QAAQ;AAwBd,MAAMW,kBAAkB,GAAGpC,MAAM,CAACa,GAAG;AACrC,sBAAsBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACM,MAAM;AACxD,mBAAmBrB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqB,YAAY,CAACF,EAAE;AACvD,aAAapB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC5C,CAAC;AAAC2B,GAAA,GAJID,kBAAkB;AAMxB,MAAME,gBAAgB,GAAGtC,MAAM,CAACuC,EAAE;AAClC,eAAehC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,QAAQ,CAACR,EAAE;AAC/C,iBAAiBH,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,UAAU,CAACoB,QAAQ;AACzD,WAAWjC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACC,WAAW;AAClD,gBAAgBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACkB,EAAE;AAC/C,CAAC;AAACc,GAAA,GALIH,gBAAgB;AAOtB,MAAMI,eAAe,GAAG1C,MAAM,CAACa,GAAG;AAClC,mBAAmBN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD;AACA;AACA;AACA;AACA,CAAC;AAACiC,GAAA,GANID,eAAe;AAQrB,MAAME,oBAAoB,GAAG5C,MAAM,CAAC6C,EAAE;AACtC,eAAetC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,QAAQ,CAAC4B,IAAI;AACjD,iBAAiBvC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,UAAU,CAACC,MAAM;AACvD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACC,WAAW;AAClD,gBAAgBhB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AAC/C,CAAC;AAAC4B,GAAA,GALIH,oBAAoB;AAO1B,MAAMI,eAAe,GAAGhD,MAAM,CAACa,GAAG;AAClC;AACA;AACA,SAASN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AACxC,CAAC;AAAC8B,GAAA,GAJID,eAAe;AAMrB,MAAME,YAAY,GAAGlD,MAAM,CAACiB,KAAK;AACjC;AACA;AACA,SAASV,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACU,EAAE;AACxC;AACA,aAAaZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACK,EAAE;AAC5C,mBAAmBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACqB,YAAY,CAACV,EAAE;AACvD,iCAAiCZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACuB,WAAW,CAACC,IAAI;AACtE;AACA;AACA,wBAAwBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAAC6B,mBAAmB;AACvE;AACA,CAAC;AAACC,GAAA,GAZIF,YAAY;AAclB,MAAMG,QAAQ,GAAGrD,MAAM,CAACsD,KAAK;AAC7B;AACA;AACA,kBAAkB/C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACW,OAAO;AACrD,CAAC;AAACsB,GAAA,GAJIF,QAAQ;AAMd,MAAMG,aAAa,GAAGxD,MAAM,CAACyD,IAAI;AACjC,eAAelD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACU,QAAQ,CAACC,EAAE;AAC/C,WAAWZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,MAAM,CAACC,WAAW;AAClD,CAAC;AAACmC,IAAA,GAHIF,aAAa;AAKnB,MAAMG,WAAW,GAAG3D,MAAM,CAACa,GAAG;AAC9B;AACA,SAASN,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACkB,EAAE;AACxC;AACA,gBAAgBpB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,OAAO,CAACC,EAAE;AAC/C,CAAC;AAACkD,IAAA,GALID,WAAW;AAOjB,MAAME,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC;IACvCsE,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,CAAC,CAAC;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM8E,oBAAoB,GAAG;IAC3B,OAAO,EAAE,CACP;MAAEC,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,EAC9C;MAAE4D,GAAG,EAAE,YAAY;MAAE5D,KAAK,EAAE;IAAa,CAAC,EAC1C;MAAE4D,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,EAC9C;MAAE4D,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,CAC/C;IACD,OAAO,EAAE,CACP;MAAE4D,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,EAC9C;MAAE4D,GAAG,EAAE,YAAY;MAAE5D,KAAK,EAAE;IAAa,CAAC,EAC1C;MAAE4D,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,EAC9C;MAAE4D,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,CAC/C;IACD,SAAS,EAAE,CACT;MAAE4D,GAAG,EAAE,gBAAgB;MAAE5D,KAAK,EAAE;IAAiB,CAAC,EAClD;MAAE4D,GAAG,EAAE,cAAc;MAAE5D,KAAK,EAAE;IAAe,CAAC,EAC9C;MAAE4D,GAAG,EAAE,gBAAgB;MAAE5D,KAAK,EAAE;IAAiB,CAAC,EAClD;MAAE4D,GAAG,EAAE,gBAAgB;MAAE5D,KAAK,EAAE;IAAiB,CAAC;EAEtD,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACd,IAAI+D,IAAI,EAAE;MACRK,WAAW,CAAC;QACVC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,EAAE;QACrBC,WAAW,EAAEP,IAAI,CAACO,WAAW,IAAI,EAAE;QACnCC,WAAW,EAAER,IAAI,CAACQ,WAAW,IAAI,CAAC,CAAC;QACnCC,QAAQ,EAAET,IAAI,CAACS,QAAQ,KAAKO,SAAS,GAAGhB,IAAI,CAACS,QAAQ,GAAG;MAC1D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACT,IAAI,CAAC,CAAC;EAEV,MAAMiB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEZ,IAAI;MAAEa,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CjB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACjB,IAAI,GAAGc,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIT,MAAM,CAACJ,IAAI,CAAC,EAAE;MAChBK,SAAS,CAACY,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACjB,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMkB,sBAAsB,GAAGA,CAACC,aAAa,EAAEJ,OAAO,KAAK;IACzDhB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPf,WAAW,EAAE;QACX,GAAGe,IAAI,CAACf,WAAW;QACnB,CAACiB,aAAa,GAAGJ;MACnB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACvB,QAAQ,CAACE,IAAI,CAACsB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACrB,IAAI,GAAG,uBAAuB;IAC1C;IAEA,OAAOqB,SAAS;EAClB,CAAC;EAED,MAAME,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,MAAMH,SAAS,GAAGD,YAAY,CAAC,CAAC;IAChC,IAAIK,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MACrCtB,SAAS,CAACgB,SAAS,CAAC;MACpB;IACF;IAEAd,UAAU,CAAC,IAAI,CAAC;IAChBF,SAAS,CAAC,CAAC,CAAC,CAAC;IAEb,IAAI;MACF,MAAMV,QAAQ,CAACG,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEvE,OAAA,CAACC,IAAI;IAAC0D,QAAQ,EAAE4B,YAAa;IAAAO,QAAA,gBAC3B9F,OAAA,CAACF,KAAK;MACJe,KAAK,EAAC,WAAW;MACjBmD,IAAI,EAAC,MAAM;MACXa,KAAK,EAAEf,QAAQ,CAACE,IAAK;MACrB+B,QAAQ,EAAEpB,YAAa;MACvBiB,KAAK,EAAExB,MAAM,CAACJ,IAAK;MACnBgC,WAAW,EAAC,iBAAiB;MAC7BC,QAAQ;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAEFrG,OAAA,CAACQ,SAAS;MAAAsF,QAAA,gBACR9F,OAAA,CAACY,KAAK;QAAC0F,OAAO,EAAC,aAAa;QAAAR,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChDrG,OAAA,CAACqB,QAAQ;QACPkF,EAAE,EAAC,aAAa;QAChBvC,IAAI,EAAC,aAAa;QAClBa,KAAK,EAAEf,QAAQ,CAACG,WAAY;QAC5B8B,QAAQ,EAAEpB,YAAa;QACvBqB,WAAW,EAAC;MAAmC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEZrG,OAAA,CAACgC,kBAAkB;MAAA8D,QAAA,gBACjB9F,OAAA,CAACkC,gBAAgB;QAAA4D,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC,EAC/CZ,MAAM,CAACe,OAAO,CAAChC,oBAAoB,CAAC,CAACiC,GAAG,CAAC,CAAC,CAACC,SAAS,EAAExC,WAAW,CAAC,kBACjElE,OAAA,CAACsC,eAAe;QAAAwD,QAAA,gBACd9F,OAAA,CAACwC,oBAAoB;UAAAsD,QAAA,EAAEY;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAuB,CAAC,eACxDrG,OAAA,CAAC4C,eAAe;UAAAkD,QAAA,EACb5B,WAAW,CAACuC,GAAG,CAACE,UAAU,iBACzB3G,OAAA,CAAC8C,YAAY;YAAAgD,QAAA,gBACX9F,OAAA,CAACiD,QAAQ;cACP6B,IAAI,EAAC,UAAU;cACfC,OAAO,EAAEjB,QAAQ,CAACI,WAAW,CAACyC,UAAU,CAAClC,GAAG,CAAC,IAAI,KAAM;cACvDsB,QAAQ,EAAGnB,CAAC,IAAKM,sBAAsB,CAACyB,UAAU,CAAClC,GAAG,EAAEG,CAAC,CAACI,MAAM,CAACD,OAAO;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACFrG,OAAA,CAACoD,aAAa;cAAA0C,QAAA,EAAEa,UAAU,CAAC9F;YAAK;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GANhCM,UAAU,CAAClC,GAAG;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOnB,CACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC;MAAA,GAbEK,SAAS;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcd,CAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACgB,CAAC,eAErBrG,OAAA,CAACQ,SAAS;MAAAsF,QAAA,eACR9F,OAAA,CAAC8C,YAAY;QAAAgD,QAAA,gBACX9F,OAAA,CAACiD,QAAQ;UACP6B,IAAI,EAAC,UAAU;UACfd,IAAI,EAAC,UAAU;UACfe,OAAO,EAAEjB,QAAQ,CAACK,QAAS;UAC3B4B,QAAQ,EAAEpB;QAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFrG,OAAA,CAACoD,aAAa;UAAA0C,QAAA,EAAC;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEZrG,OAAA,CAACuD,WAAW;MAAAuC,QAAA,gBACV9F,OAAA,CAACH,MAAM;QACLiF,IAAI,EAAC,QAAQ;QACb8B,OAAO,EAAC,WAAW;QACnBC,OAAO,EAAEjD,QAAS;QAClBkD,QAAQ,EAAExC,OAAQ;QAAAwB,QAAA,EACnB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrG,OAAA,CAACH,MAAM;QACLiF,IAAI,EAAC,QAAQ;QACb8B,OAAO,EAAC,SAAS;QACjBtC,OAAO,EAAEA,OAAQ;QACjBwC,QAAQ,EAAExC,OAAQ;QAAAwB,QAAA,EAEjBpC,IAAI,GAAG,aAAa,GAAG;MAAa;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACxC,EAAA,CAhLIJ,QAAQ;AAAAsD,IAAA,GAARtD,QAAQ;AAkLd,eAAeA,QAAQ;AAAC,IAAAlD,EAAA,EAAAI,GAAA,EAAAS,GAAA,EAAAW,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAuD,IAAA;AAAAC,YAAA,CAAAzG,EAAA;AAAAyG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}