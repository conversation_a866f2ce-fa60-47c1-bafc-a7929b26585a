import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import { themeVariants, defaultTheme } from '../theme/theme';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState(() => {
    const savedTheme = localStorage.getItem('qms-theme');
    return savedTheme || 'blue';
  });

  useEffect(() => {
    localStorage.setItem('qms-theme', currentTheme);
  }, [currentTheme]);

  const changeTheme = (themeName) => {
    if (themeVariants[themeName]) {
      setCurrentTheme(themeName);
    }
  };

  const theme = themeVariants[currentTheme] || defaultTheme;

  const value = {
    currentTheme,
    changeTheme,
    availableThemes: Object.keys(themeVariants),
    theme
  };

  return (
    <ThemeContext.Provider value={value}>
      <StyledThemeProvider theme={theme}>
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
