{"ast": null, "code": "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { parseMutationFilterArgs } from '../core/utils';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useIsMutating(arg1, arg2) {\n  var mountedRef = React.useRef(false);\n  var filters = parseMutationFilterArgs(arg1, arg2);\n  var queryClient = useQueryClient();\n  var _React$useState = React.useState(queryClient.isMutating(filters)),\n    isMutating = _React$useState[0],\n    setIsMutating = _React$useState[1];\n  var filtersRef = React.useRef(filters);\n  filtersRef.current = filters;\n  var isMutatingRef = React.useRef(isMutating);\n  isMutatingRef.current = isMutating;\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = queryClient.getMutationCache().subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        var newIsMutating = queryClient.isMutating(filtersRef.current);\n        if (isMutatingRef.current !== newIsMutating) {\n          setIsMutating(newIsMutating);\n        }\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isMutating;\n}", "map": {"version": 3, "names": ["React", "notify<PERSON><PERSON>ger", "parseMutationFilterArgs", "useQueryClient", "useIsMutating", "arg1", "arg2", "mountedRef", "useRef", "filters", "queryClient", "_React$useState", "useState", "isMutating", "setIsMutating", "filtersRef", "current", "isMutatingRef", "useEffect", "unsubscribe", "getMutationCache", "subscribe", "batchCalls", "newIsMutating"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-query/es/react/useIsMutating.js"], "sourcesContent": ["import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { parseMutationFilterArgs } from '../core/utils';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useIsMutating(arg1, arg2) {\n  var mountedRef = React.useRef(false);\n  var filters = parseMutationFilterArgs(arg1, arg2);\n  var queryClient = useQueryClient();\n\n  var _React$useState = React.useState(queryClient.isMutating(filters)),\n      isMutating = _React$useState[0],\n      setIsMutating = _React$useState[1];\n\n  var filtersRef = React.useRef(filters);\n  filtersRef.current = filters;\n  var isMutatingRef = React.useRef(isMutating);\n  isMutatingRef.current = isMutating;\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = queryClient.getMutationCache().subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        var newIsMutating = queryClient.isMutating(filtersRef.current);\n\n        if (isMutatingRef.current !== newIsMutating) {\n          setIsMutating(newIsMutating);\n        }\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isMutating;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,uBAAuB,QAAQ,eAAe;AACvD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIC,OAAO,GAAGP,uBAAuB,CAACG,IAAI,EAAEC,IAAI,CAAC;EACjD,IAAII,WAAW,GAAGP,cAAc,CAAC,CAAC;EAElC,IAAIQ,eAAe,GAAGX,KAAK,CAACY,QAAQ,CAACF,WAAW,CAACG,UAAU,CAACJ,OAAO,CAAC,CAAC;IACjEI,UAAU,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC/BG,aAAa,GAAGH,eAAe,CAAC,CAAC,CAAC;EAEtC,IAAII,UAAU,GAAGf,KAAK,CAACQ,MAAM,CAACC,OAAO,CAAC;EACtCM,UAAU,CAACC,OAAO,GAAGP,OAAO;EAC5B,IAAIQ,aAAa,GAAGjB,KAAK,CAACQ,MAAM,CAACK,UAAU,CAAC;EAC5CI,aAAa,CAACD,OAAO,GAAGH,UAAU;EAClCb,KAAK,CAACkB,SAAS,CAAC,YAAY;IAC1BX,UAAU,CAACS,OAAO,GAAG,IAAI;IACzB,IAAIG,WAAW,GAAGT,WAAW,CAACU,gBAAgB,CAAC,CAAC,CAACC,SAAS,CAACpB,aAAa,CAACqB,UAAU,CAAC,YAAY;MAC9F,IAAIf,UAAU,CAACS,OAAO,EAAE;QACtB,IAAIO,aAAa,GAAGb,WAAW,CAACG,UAAU,CAACE,UAAU,CAACC,OAAO,CAAC;QAE9D,IAAIC,aAAa,CAACD,OAAO,KAAKO,aAAa,EAAE;UAC3CT,aAAa,CAACS,aAAa,CAAC;QAC9B;MACF;IACF,CAAC,CAAC,CAAC;IACH,OAAO,YAAY;MACjBhB,UAAU,CAACS,OAAO,GAAG,KAAK;MAC1BG,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;EACjB,OAAOG,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}