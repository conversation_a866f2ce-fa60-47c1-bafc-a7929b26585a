import React from 'react';
import styled, { css } from 'styled-components';

const StyledButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid transparent;
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.fontSize.sm};
  font-weight: ${props => props.theme.fontWeight.medium};
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  transition: all ${props => props.theme.transitions.fast};
  white-space: nowrap;
  user-select: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  ${props => props.size === 'sm' && css`
    padding: ${props.theme.spacing.xs} ${props.theme.spacing.sm};
    font-size: ${props.theme.fontSize.xs};
  `}

  ${props => props.size === 'lg' && css`
    padding: ${props.theme.spacing.md} ${props.theme.spacing.lg};
    font-size: ${props.theme.fontSize.base};
  `}

  ${props => props.variant === 'primary' && css`
    background-color: ${props.theme.colors.primary};
    color: ${props.theme.colors.textInverse};
    border-color: ${props.theme.colors.primary};

    &:hover:not(:disabled) {
      background-color: ${props.theme.colors.primaryHover};
      border-color: ${props.theme.colors.primaryHover};
    }
  `}

  ${props => props.variant === 'secondary' && css`
    background-color: transparent;
    color: ${props.theme.colors.textPrimary};
    border-color: ${props.theme.colors.border};

    &:hover:not(:disabled) {
      background-color: ${props.theme.colors.backgroundSecondary};
    }
  `}

  ${props => props.variant === 'outline' && css`
    background-color: transparent;
    color: ${props.theme.colors.primary};
    border-color: ${props.theme.colors.primary};

    &:hover:not(:disabled) {
      background-color: ${props.theme.colors.primary};
      color: ${props.theme.colors.textInverse};
    }
  `}

  ${props => props.variant === 'danger' && css`
    background-color: ${props.theme.colors.error};
    color: ${props.theme.colors.textInverse};
    border-color: ${props.theme.colors.error};

    &:hover:not(:disabled) {
      background-color: #dc2626;
      border-color: #dc2626;
    }
  `}

  ${props => props.variant === 'ghost' && css`
    background-color: transparent;
    color: ${props.theme.colors.textSecondary};
    border-color: transparent;

    &:hover:not(:disabled) {
      background-color: ${props.theme.colors.backgroundSecondary};
      color: ${props.theme.colors.textPrimary};
    }
  `}

  ${props => props.fullWidth && css`
    width: 100%;
  `}
`;

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      {...props}
    >
      {loading ? 'Loading...' : children}
    </StyledButton>
  );
};

export default Button;
