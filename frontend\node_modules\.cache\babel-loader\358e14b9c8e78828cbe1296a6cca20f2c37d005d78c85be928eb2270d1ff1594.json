{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\qmsus\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../api/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'LOGIN_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'LOGIN_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'LOAD_USER_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'LOAD_USER_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start if token exists\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          const response = await authAPI.getProfile();\n          dispatch({\n            type: 'LOAD_USER_SUCCESS',\n            payload: response.data.user\n          });\n        } catch (error) {\n          localStorage.removeItem('token');\n          dispatch({\n            type: 'LOAD_USER_FAILURE'\n          });\n        }\n      } else {\n        dispatch({\n          type: 'LOAD_USER_FAILURE'\n        });\n      }\n    };\n    loadUser();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      dispatch({\n        type: 'LOGIN_START'\n      });\n      const response = await authAPI.login(email, password);\n      const {\n        user,\n        token\n      } = response.data;\n      localStorage.setItem('token', token);\n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: {\n          user,\n          token\n        }\n      });\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      dispatch({\n        type: 'LOGIN_FAILURE',\n        payload: message\n      });\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      dispatch({\n        type: 'LOGOUT'\n      });\n    }\n  };\n  const clearError = () => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    logout,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "AuthContext", "initialState", "user", "token", "localStorage", "getItem", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "loadUser", "response", "getProfile", "data", "removeItem", "login", "email", "password", "setItem", "success", "_error$response", "_error$response$data", "message", "logout", "console", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/Projects/qmsus/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../api/auth';\n\nconst AuthContext = createContext();\n\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'LOGIN_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'LOGIN_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'LOAD_USER_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'LOAD_USER_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Load user on app start if token exists\n  useEffect(() => {\n    const loadUser = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          const response = await authAPI.getProfile();\n          dispatch({\n            type: 'LOAD_USER_SUCCESS',\n            payload: response.data.user\n          });\n        } catch (error) {\n          localStorage.removeItem('token');\n          dispatch({ type: 'LOAD_USER_FAILURE' });\n        }\n      } else {\n        dispatch({ type: 'LOAD_USER_FAILURE' });\n      }\n    };\n\n    loadUser();\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      dispatch({ type: 'LOGIN_START' });\n      \n      const response = await authAPI.login(email, password);\n      const { user, token } = response.data;\n      \n      localStorage.setItem('token', token);\n      \n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: { user, token }\n      });\n      \n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: 'LOGIN_FAILURE',\n        payload: message\n      });\n      return { success: false, message };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      dispatch({ type: 'LOGOUT' });\n    }\n  };\n\n  const clearError = () => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  };\n\n  const value = {\n    ...state,\n    login,\n    logout,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,MAAMQ,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,eAAe;MAClB,OAAO;QACL,GAAGE,KAAK;QACRR,IAAI,EAAES,MAAM,CAACE,OAAO,CAACX,IAAI;QACzBC,KAAK,EAAEQ,MAAM,CAACE,OAAO,CAACV,KAAK;QAC3BG,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,eAAe;MAClB,OAAO;QACL,GAAGE,KAAK;QACRR,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGH,KAAK;QACRR,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,mBAAmB;MACtB,OAAO;QACL,GAAGE,KAAK;QACRR,IAAI,EAAES,MAAM,CAACE,OAAO;QACpBP,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,mBAAmB;MACtB,OAAO;QACL,GAAGE,KAAK;QACRR,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACN,KAAK,EAAEO,QAAQ,CAAC,GAAGtB,UAAU,CAACc,WAAW,EAAER,YAAY,CAAC;;EAE/D;EACAL,SAAS,CAAC,MAAM;IACd,MAAMsB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMf,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,IAAI;UACF,MAAMgB,QAAQ,GAAG,MAAMtB,OAAO,CAACuB,UAAU,CAAC,CAAC;UAC3CH,QAAQ,CAAC;YACPL,IAAI,EAAE,mBAAmB;YACzBC,OAAO,EAAEM,QAAQ,CAACE,IAAI,CAACnB;UACzB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;UACdJ,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC;UAChCL,QAAQ,CAAC;YAAEL,IAAI,EAAE;UAAoB,CAAC,CAAC;QACzC;MACF,CAAC,MAAM;QACLK,QAAQ,CAAC;UAAEL,IAAI,EAAE;QAAoB,CAAC,CAAC;MACzC;IACF,CAAC;IAEDM,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACFR,QAAQ,CAAC;QAAEL,IAAI,EAAE;MAAc,CAAC,CAAC;MAEjC,MAAMO,QAAQ,GAAG,MAAMtB,OAAO,CAAC0B,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;MACrD,MAAM;QAAEvB,IAAI;QAAEC;MAAM,CAAC,GAAGgB,QAAQ,CAACE,IAAI;MAErCjB,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAEvB,KAAK,CAAC;MAEpCc,QAAQ,CAAC;QACPL,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAE;UAAEX,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEF,OAAO;QAAEwB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOnB,KAAK,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAApB,KAAK,CAACW,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,cAAc;MAC/Db,QAAQ,CAAC;QACPL,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAEiB;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMlC,OAAO,CAACkC,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRJ,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC;MAChCL,QAAQ,CAAC;QAAEL,IAAI,EAAE;MAAS,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvBhB,QAAQ,CAAC;MAAEL,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;EAED,MAAMsB,KAAK,GAAG;IACZ,GAAGxB,KAAK;IACRa,KAAK;IACLQ,MAAM;IACNE;EACF,CAAC;EAED,oBACElC,OAAA,CAACC,WAAW,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAnB,QAAA,EAChCA;EAAQ;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACvB,EAAA,CA9EWF,YAAY;AAAA0B,EAAA,GAAZ1B,YAAY;AAgFzB,OAAO,MAAM2B,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGjD,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAAC2C,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAezC,WAAW;AAAC,IAAAwC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}