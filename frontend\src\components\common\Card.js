import React from 'react';
import styled, { css } from 'styled-components';

const StyledCard = styled.div`
  background-color: ${props => props.theme.colors.cardBg};
  border: 1px solid ${props => props.theme.colors.cardBorder};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.colors.cardShadow};
  overflow: hidden;

  ${props => props.padding && css`
    padding: ${props.theme.spacing.lg};
  `}

  ${props => props.hover && css`
    transition: all ${props.theme.transitions.fast};
    cursor: pointer;

    &:hover {
      box-shadow: ${props.theme.shadows.md};
      transform: translateY(-1px);
    }
  `}
`;

const CardHeader = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  
  ${props => props.noBorder && css`
    border-bottom: none;
  `}
`;

const CardTitle = styled.h3`
  margin: 0;
  font-size: ${props => props.theme.fontSize.lg};
  font-weight: ${props => props.theme.fontWeight.semibold};
  color: ${props => props.theme.colors.textPrimary};
`;

const CardSubtitle = styled.p`
  margin: ${props => props.theme.spacing.xs} 0 0 0;
  font-size: ${props => props.theme.fontSize.sm};
  color: ${props => props.theme.colors.textSecondary};
`;

const CardBody = styled.div`
  padding: ${props => props.theme.spacing.lg};
`;

const CardFooter = styled.div`
  padding: ${props => props.theme.spacing.lg};
  border-top: 1px solid ${props => props.theme.colors.border};
  background-color: ${props => props.theme.colors.backgroundSecondary};

  ${props => props.noBorder && css`
    border-top: none;
    background-color: transparent;
  `}
`;

const Card = ({ 
  children, 
  padding = false, 
  hover = false, 
  className,
  ...props 
}) => {
  return (
    <StyledCard 
      padding={padding} 
      hover={hover} 
      className={className}
      {...props}
    >
      {children}
    </StyledCard>
  );
};

Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Subtitle = CardSubtitle;
Card.Body = CardBody;
Card.Footer = CardFooter;

export default Card;
