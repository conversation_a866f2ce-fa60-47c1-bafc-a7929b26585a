require('dotenv').config();
const mysql = require('mysql2/promise');

const testConnection = async () => {
  try {
    console.log('Testing database connection...');
    console.log('Host:', process.env.DB_HOST);
    console.log('User:', process.env.DB_USER);
    console.log('Database:', process.env.DB_NAME);
    console.log('Port:', process.env.DB_PORT);
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306
    });

    console.log('✅ Database connection successful!');
    
    // Test if tables exist
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('\n📋 Available tables:');
    tables.forEach(table => {
      console.log('-', Object.values(table)[0]);
    });
    
    // Check if users table exists and show structure
    try {
      const [userColumns] = await connection.execute('DESCRIBE users');
      console.log('\n👤 Users table structure:');
      userColumns.forEach(col => {
        console.log(`- ${col.Field} (${col.Type})`);
      });
    } catch (err) {
      console.log('\n❌ Users table not found or different name');
    }
    
    // Check if user_settings_roles table exists
    try {
      const [roleColumns] = await connection.execute('DESCRIBE user_settings_roles');
      console.log('\n🔐 user_settings_roles table structure:');
      roleColumns.forEach(col => {
        console.log(`- ${col.Field} (${col.Type})`);
      });
    } catch (err) {
      console.log('\n❌ user_settings_roles table not found');
    }

    // Check if settings_roles table exists
    try {
      const [settingsRoleColumns] = await connection.execute('DESCRIBE settings_roles');
      console.log('\n⚙️ settings_roles table structure:');
      settingsRoleColumns.forEach(col => {
        console.log(`- ${col.Field} (${col.Type})`);
      });
    } catch (err) {
      console.log('\n❌ settings_roles table not found');
    }

    // Check sample user data
    try {
      const [users] = await connection.execute('SELECT id, email, firstName, lastName, role, isActive FROM users LIMIT 3');
      console.log('\n👥 Sample users:');
      users.forEach(user => {
        console.log(`- ID: ${user.id}, Email: ${user.email}, Name: ${user.firstName} ${user.lastName}, Role: ${user.role}, Active: ${user.isActive}`);
      });
    } catch (err) {
      console.log('\n❌ Could not fetch user data');
    }

    await connection.end();
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Suggestions:');
      console.log('- Check your username and password');
      console.log('- Make sure MySQL user has proper permissions');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Suggestions:');
      console.log('- Make sure MySQL server is running');
      console.log('- Check if the port is correct (default: 3306)');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 Suggestions:');
      console.log('- Make sure the database "qmsdb" exists');
      console.log('- Check the database name spelling');
    }
  }
};

testConnection();
