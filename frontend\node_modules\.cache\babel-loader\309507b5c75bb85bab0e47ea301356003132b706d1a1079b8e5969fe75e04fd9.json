{"ast": null, "code": "export var Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n  var _proto = Subscribable.prototype;\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n    var callback = listener || function () {\n      return undefined;\n    };\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n      _this.onUnsubscribe();\n    };\n  };\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n  return Subscribable;\n}();", "map": {"version": 3, "names": ["Subscribable", "listeners", "_proto", "prototype", "subscribe", "listener", "_this", "callback", "undefined", "push", "onSubscribe", "filter", "x", "onUnsubscribe", "hasListeners", "length"], "sources": ["D:/Projects/qmsus/frontend/node_modules/react-query/es/core/subscribable.js"], "sourcesContent": ["export var Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n\n  var _proto = Subscribable.prototype;\n\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n\n    var callback = listener || function () {\n      return undefined;\n    };\n\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n\n      _this.onUnsubscribe();\n    };\n  };\n\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n\n  return Subscribable;\n}();"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG,aAAa,YAAY;EACjD,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;EAEA,IAAIC,MAAM,GAAGF,YAAY,CAACG,SAAS;EAEnCD,MAAM,CAACE,SAAS,GAAG,SAASA,SAASA,CAACC,QAAQ,EAAE;IAC9C,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAIC,QAAQ,GAAGF,QAAQ,IAAI,YAAY;MACrC,OAAOG,SAAS;IAClB,CAAC;IAED,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACF,QAAQ,CAAC;IAC7B,IAAI,CAACG,WAAW,CAAC,CAAC;IAClB,OAAO,YAAY;MACjBJ,KAAK,CAACL,SAAS,GAAGK,KAAK,CAACL,SAAS,CAACU,MAAM,CAAC,UAAUC,CAAC,EAAE;QACpD,OAAOA,CAAC,KAAKL,QAAQ;MACvB,CAAC,CAAC;MAEFD,KAAK,CAACO,aAAa,CAAC,CAAC;IACvB,CAAC;EACH,CAAC;EAEDX,MAAM,CAACY,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IAC5C,OAAO,IAAI,CAACb,SAAS,CAACc,MAAM,GAAG,CAAC;EAClC,CAAC;EAEDb,MAAM,CAACQ,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG,CAAC;EAAA,CAC5C;EAEDR,MAAM,CAACW,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG,CAAC;EAAA,CAChD;EAED,OAAOb,YAAY;AACrB,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}